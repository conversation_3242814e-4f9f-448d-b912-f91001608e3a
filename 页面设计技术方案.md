# 页面设计技术方案

## 1. 概述

基于PageManagerController的深入分析，本文档提供了一套完整的页面设计技术方案。该方案采用分层架构、组件化设计和模板驱动的方式，支持多端适配、多租户管理和复杂的业务场景。

## 2. 整体架构设计

### 2.1 核心架构模式

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Controller    │    │    Service      │    │      DAO        │
│   表现层        │───▶│    业务层       │───▶│    数据访问层    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Business Engine│    │   Database      │
│   网关层        │    │   业务引擎      │    │   数据库        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈选型

- **后端框架**: Spring Boot + MyBatis
- **数据存储**: MySQL（主数据） + Redis（缓存）
- **序列化**: FastJSON
- **文档**: Swagger API
- **分布式锁**: Redis分布式锁
- **消息队列**: 异步处理机制
- **监控**: 业务监控 + 系统监控

## 3. 数据模型设计

### 3.1 核心实体关系

```mermaid
erDiagram
    PAGE ||--o{ COMPONENT : contains
    PAGE ||--|| TEMPLATE : based_on
    PAGE ||--o| TIME_CONFIG : has
    PAGE ||--o| SHARE_CONFIG : has
    COMPONENT ||--o{ COMPONENT_DETAIL : has
    COMPONENT ||--o| TIME_CONFIG : has
    PAGE {
        string pageCode PK
        string templateCode FK
        string type
        string ownerType
        string ownerId
        integer status
        string components
        datetime createTime
        datetime updateTime
    }
    COMPONENT {
        string componentCode PK
        string pageCode FK
        string type
        string metaConfig
        integer orderValue
    }
    TEMPLATE {
        string templateCode PK
        string templateName
        string pageUseType
        string useChannels
    }
```

### 3.2 页面状态流转

```mermaid
stateDiagram-v2
    [*] --> INIT : 创建页面
    INIT --> DRAFT : 编辑保存
    DRAFT --> APPROVAL : 提交审批
    DRAFT --> DRAFT : 继续编辑
    APPROVAL --> PUBLISH : 审批通过
    APPROVAL --> FAIL : 审批驳回
    FAIL --> DRAFT : 重新编辑
    PUBLISH --> DISABLED : 失效页面
    PUBLISH --> EXECUTORY : 定时发布
    EXECUTORY --> PUBLISH : 到达生效时间
    DISABLED --> [*] : 删除页面
```

## 4. 业务流程设计

### 4.1 页面保存流程

基于时序图分析，页面保存采用五阶段处理模式：

#### 阶段一：参数校验
- **基础参数校验**: 必填字段、数据格式验证
- **页面状态校验**: 检查页面当前状态是否允许编辑
- **页面类型校验**: 确保唯一性页面类型不重复
- **私密会场校验**: 验证私密会场必须包含档期组件

#### 阶段二：数据解析
- **组件JSON解析**: 将前端传入的组件JSON解析为BO对象
- **页面信息解析**: 解析页面基础配置和元数据

#### 阶段三：组件校验
- **组件业务规则校验**: 各类组件的专项业务规则验证
- **组件关联性校验**: 检查组件间的依赖关系
- **内容合规性校验**: 图片、视频等内容的合规性检查

#### 阶段四：数据转换
- **BO到DO转换**: 业务对象转换为数据对象
- **组件关系处理**: 处理组件间的关联关系
- **时间配置处理**: 处理组件和页面的时间配置

#### 阶段五：数据持久化
- **事务性保存**: 页面、组件数据的事务性保存
- **缓存同步**: 更新Redis缓存
- **异步处理**: 内容安全检查、消息通知等
- **操作日志**: 记录操作审计日志

### 4.2 分布式锁机制

```java
// 分布式锁实现
ILock iLock = iLockFactory.getLock(
    RedisRepository.REDIS_PREFIX + 
    RedisRepository.PAGE_OPRATE + 
    request.getPageCode()
);
try {
    boolean lock = iLock.lock(timeout, timeout);
    if (!lock) {
        throw new BusinessException("页面正在被其他用户编辑");
    }
    // 执行业务逻辑
} finally {
    iLock.releaseLock();
}
```

## 5. 组件体系设计

### 5.1 组件类型体系

```
组件基类 (ComponentBO)
├── 展示类组件
│   ├── 图片组件 (IMAGE)
│   ├── 视频组件 (VIDEO)
│   ├── 文本组件 (TEXT)
│   └── 分隔符组件 (SEPARATOR)
├── 导航类组件
│   ├── Banner组件 (BANNER)
│   ├── 导航组件 (NAVIGATION)
│   └── 图片魔方组件 (CUBE)
├── 商品类组件
│   ├── 商品组件 (PRODUCT)
│   ├── 全部商品组件 (ALLPRODUCT)
│   └── 榜单组件 (TOPLIST)
├── 营销类组件
│   ├── 优惠券组件 (COUPON)
│   ├── 积分优惠券组件 (POINTSCOUPON)
│   ├── 秒杀组件 (SECKILL)
│   └── 档期组件 (ACTIVITY)
└── 增值类组件
    ├── 高佣组件 (PROFITADDITIONAL)
    ├── 素材组件 (MATERIAL)
    └── 爱豆激励组件 (USERINCENTIVE)
```

### 5.2 组件配置结构

```json
{
  "componentCode": "COMP_001",
  "componentName": "商品展示组件",
  "type": "PRODUCT",
  "order": 1,
  "timeConfig": {
    "effectiveType": 1,
    "timeType": 1,
    "timeList": [...]
  },
  "directUserGroup": {
    "directShowType": 1,
    "userGroups": "group1,group2"
  },
  "configDetails": [...]
}
```

## 6. 技术特性设计

### 6.1 时间配置系统

支持灵活的时间配置策略：

- **立即生效**: 保存后立即生效
- **定时生效**: 指定时间点生效
- **多时段生效**: 支持多个时间段
- **循环时段**: 支持按周、按月循环

### 6.2 用户分群系统

支持精准的用户定向投放：

- **全部用户**: 不限制用户群体
- **用户类型**: 按用户类型（普通用户、店主、店长）
- **用户分群**: 基于用户标签的精准分群
- **用户等级**: 按用户等级进行定向

### 6.3 多租户支持

- **租户隔离**: 基于ownerType和ownerId的数据隔离
- **权限控制**: 不同租户类型的权限差异化
- **资源配额**: 支持不同租户的资源配额管理

### 6.4 缓存策略

```
缓存层次结构:
├── L1缓存: 本地缓存 (页面模板、配置信息)
├── L2缓存: Redis缓存 (页面数据、组件数据)
└── L3缓存: CDN缓存 (静态资源、图片)

缓存更新策略:
├── 写入时更新: 页面保存时同步更新缓存
├── 定时刷新: 定时任务刷新缓存
└── 失效重建: 缓存失效时重新构建
```

## 7. 安全控制设计

### 7.1 内容安全

- **图片安全检查**: 检测违规图片内容
- **视频安全检查**: 异步检查视频内容合规性
- **文本安全检查**: 敏感词过滤和内容审核

### 7.2 访问控制

- **API鉴权**: 基于Token的API访问控制
- **操作权限**: 基于角色的操作权限控制
- **数据权限**: 基于租户的数据访问权限

### 7.3 审计日志

```java
// 操作日志记录
OperationLogSaveRequest logRequest = new OperationLogSaveRequest();
logRequest.setBizType(OperationLogBizTypeEnum.PAGE.getCode());
logRequest.setBizCode(pageCode);
logRequest.setAction(OperationLogActionEnum.SAVE.getCode());
logRequest.setCreateBy(request.getCreateBy());
operationLogService.saveOperationLog(logRequest);
```

## 8. 实施方案

### 8.1 开发规范

#### 8.1.1 接口设计规范
```java
// 统一返回结果封装
@ApiOperation(value = "保存页面")
@PostMapping(value = "/api/page/manager/save")
public Result<PageBO> save(@RequestBody SavePageRequest request) {
    // 业务逻辑处理
    PageBO page = pageOperateService.save(request);

    // 统一结果封装
    Result<PageBO> result = Result.success(page);

    // 特殊情况处理
    if (CollectionUtils.isNotEmpty(page.getContentCheckResults())) {
        result.setSuccess(false);
        result.setCode(ErrorEnum.VIDEO_CHECK_ERROR.getCode());
        result.setMessage(ErrorEnum.VIDEO_CHECK_ERROR.getMsg());
    }

    return result;
}
```

#### 8.1.2 异常处理规范
```java
// 业务异常统一处理
public enum CmsErrorCodeEnum {
    TEMPLATE_ISNOTEXISTS_ERROR("CMS_001", "模板不存在"),
    PAGE_ISNOTEXISTS_ERROR("CMS_002", "页面不存在"),
    PAGE_STATUS_ERROR("CMS_003", "页面状态不允许编辑"),
    SAVE_PAGE_DRAFT_ERROR("CMS_004", "页面保存失败，请稍后重试");

    private String code;
    private String errorMsg;
}
```

#### 8.1.3 参数校验规范
```java
// 参数校验注解
@Data
public class SavePageRequest extends BaseRequest {
    @ApiModelProperty("页面名称")
    @NotBlank(message = "页面名称不能为空")
    private String name;

    @ApiModelProperty("页面类型")
    @NotBlank(message = "页面类型不能为空")
    private String type;

    @ApiModelProperty("生效方式")
    @NotNull(message = "页面生效方式不能为空")
    private TimeConfigBO timeConfig;
}
```

### 8.2 数据库设计

#### 8.2.1 主要数据表结构
```sql
-- 页面实例表
CREATE TABLE mshop_page_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    page_code VARCHAR(64) NOT NULL COMMENT '页面编号',
    template_code VARCHAR(64) COMMENT '模板编号',
    name VARCHAR(128) NOT NULL COMMENT '页面名称',
    title VARCHAR(128) COMMENT '页面标题',
    type VARCHAR(32) NOT NULL COMMENT '页面类型',
    status INT NOT NULL DEFAULT 1 COMMENT '页面状态',
    owner_id VARCHAR(64) NOT NULL COMMENT '所属者ID',
    owner_type VARCHAR(32) NOT NULL COMMENT '所属者类型',
    channel VARCHAR(32) COMMENT '使用端',
    version VARCHAR(32) COMMENT '版本号',
    time_config TEXT COMMENT '时间配置',
    share_config TEXT COMMENT '分享配置',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_code (page_code),
    INDEX idx_owner (owner_id, owner_type),
    INDEX idx_type_status (type, status)
);

-- 页面草稿表
CREATE TABLE mshop_page_draft (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    page_code VARCHAR(64) NOT NULL COMMENT '页面编号',
    version VARCHAR(32) NOT NULL COMMENT '版本号',
    name VARCHAR(128) NOT NULL COMMENT '页面名称',
    title VARCHAR(128) COMMENT '页面标题',
    components TEXT COMMENT '组件配置JSON',
    time_config TEXT COMMENT '时间配置',
    share_config TEXT COMMENT '分享配置',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_version (page_code, version)
);

-- 组件实例表
CREATE TABLE mshop_component_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    component_code VARCHAR(64) NOT NULL COMMENT '组件编号',
    page_code VARCHAR(64) NOT NULL COMMENT '页面编号',
    version VARCHAR(32) NOT NULL COMMENT '版本号',
    type VARCHAR(32) NOT NULL COMMENT '组件类型',
    meta_config TEXT COMMENT '组件配置',
    time_config TEXT COMMENT '时间配置',
    order_value INT DEFAULT 0 COMMENT '排序值',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_version (page_code, version),
    INDEX idx_component_code (component_code)
);
```

#### 8.2.2 索引优化策略
- **主键索引**: 使用自增ID作为主键
- **业务索引**: 基于查询场景建立复合索引
- **分区策略**: 按时间或租户进行分区
- **读写分离**: 查询走从库，写入走主库

### 8.3 缓存设计

#### 8.3.1 缓存键设计
```java
public class CacheKeyConstants {
    // 页面缓存键
    public static final String PAGE_CACHE_KEY = "cms:page:{pageCode}:{version}";

    // 组件缓存键
    public static final String COMPONENT_CACHE_KEY = "cms:component:{pageCode}:{version}";

    // 模板缓存键
    public static final String TEMPLATE_CACHE_KEY = "cms:template:{templateCode}";

    // 用户分群缓存键
    public static final String USER_GROUP_CACHE_KEY = "cms:usergroup:{groupId}";
}
```

#### 8.3.2 缓存更新策略
```java
@Service
public class PageCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 同步页面缓存
     */
    public boolean syncCache(PageBO pageBO) {
        try {
            String cacheKey = String.format(CacheKeyConstants.PAGE_CACHE_KEY,
                pageBO.getPageCode(), pageBO.getVersion());

            // 设置缓存，过期时间24小时
            redisTemplate.opsForValue().set(cacheKey, pageBO, 24, TimeUnit.HOURS);

            return true;
        } catch (Exception e) {
            log.error("同步页面缓存失败", e);
            return false;
        }
    }

    /**
     * 清除页面缓存
     */
    public void clearCache(String pageCode, String version) {
        String cacheKey = String.format(CacheKeyConstants.PAGE_CACHE_KEY, pageCode, version);
        redisTemplate.delete(cacheKey);
    }
}
```

### 8.4 监控告警

#### 8.4.1 业务监控指标
- **页面创建成功率**: 监控页面创建的成功率
- **页面发布成功率**: 监控页面发布的成功率
- **组件校验失败率**: 监控组件校验的失败率
- **内容安全检查通过率**: 监控内容安全检查的通过率

#### 8.4.2 系统监控指标
- **接口响应时间**: 监控API接口的响应时间
- **数据库连接数**: 监控数据库连接池使用情况
- **缓存命中率**: 监控Redis缓存的命中率
- **分布式锁竞争**: 监控分布式锁的竞争情况

#### 8.4.3 告警配置
```yaml
# 监控告警配置
monitoring:
  alerts:
    - name: "页面保存失败率过高"
      metric: "page_save_failure_rate"
      threshold: 0.05  # 5%
      duration: "5m"

    - name: "接口响应时间过长"
      metric: "api_response_time"
      threshold: 2000  # 2秒
      duration: "3m"

    - name: "缓存命中率过低"
      metric: "cache_hit_rate"
      threshold: 0.8   # 80%
      duration: "10m"
```

## 9. 部署架构

### 9.1 微服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gateway       │    │   CMS Service   │    │   Config Center │
│   网关服务      │───▶│   CMS核心服务   │───▶│   配置中心      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Service Mesh  │    │   Service       │
│   负载均衡      │    │   服务网格      │    │   Registry      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 9.2 数据存储架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL Master │    │   MySQL Slave   │    │   Redis Cluster │
│   主数据库      │───▶│   从数据库      │    │   缓存集群      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Backup        │    │   Read Replica  │    │   CDN           │
│   数据备份      │    │   读副本        │    │   内容分发      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 9.3 容器化部署
```dockerfile
# Dockerfile示例
FROM openjdk:11-jre-slim

WORKDIR /app

COPY target/mshop-cms-center.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# Kubernetes部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cms-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cms-service
  template:
    metadata:
      labels:
        app: cms-service
    spec:
      containers:
      - name: cms-service
        image: cms-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
```

## 10. 性能优化

### 10.1 数据库优化
- **连接池优化**: 合理配置数据库连接池参数
- **SQL优化**: 优化慢查询，添加合适索引
- **分库分表**: 按业务维度进行分库分表
- **读写分离**: 查询操作走从库，写操作走主库

### 10.2 缓存优化
- **多级缓存**: 本地缓存 + Redis缓存 + CDN缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存穿透**: 使用布隆过滤器防止缓存穿透
- **缓存雪崩**: 设置随机过期时间防止缓存雪崩

### 10.3 应用优化
- **异步处理**: 耗时操作采用异步处理
- **批量操作**: 数据库批量插入和更新
- **对象池**: 重用对象减少GC压力
- **JVM调优**: 合理配置JVM参数

## 11. 总结

本技术方案基于PageManagerController的实际代码分析，提供了一套完整的页面设计解决方案。方案具有以下特点：

1. **架构完整**: 涵盖了从接口层到数据层的完整架构设计
2. **扩展性强**: 采用组件化和插件化设计，易于扩展新功能
3. **性能优秀**: 通过多级缓存和异步处理保证系统性能
4. **安全可靠**: 完善的安全控制和异常处理机制
5. **运维友好**: 完整的监控告警和部署方案

该方案可以作为企业级CMS系统的技术参考，支持复杂的业务场景和大规模的用户访问。
