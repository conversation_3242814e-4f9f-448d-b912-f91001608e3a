package com.mengxiang.mshop.cms.service.facade.common.feign.rule;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.service.facade.common.fallback.RuleFallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "规则")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = RuleFallBack.class)
public interface RuleFeign {
    
    @ApiOperation(value = "获取规则信息")
    @PostMapping(value = "/api/page/rules")
    Result<List<PageRuleInfoResult>> rules(@RequestBody SellRuleGetBatchRequest request);


    @ApiOperation(value = "查询选品中心营销标签")
    @RequestMapping(value = "/api/page/manager/findSafeModelRuleLabel")
    Result<List<SafeModelRuleLabelResult>> findSafeModelRuleLabel();
}
