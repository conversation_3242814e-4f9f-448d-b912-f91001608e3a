package com.mengxiang.mshop.cms.service.facade.common.feign.page;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PageShareConfigBO;
import com.mengxiang.mshop.cms.service.facade.common.fallback.PageFallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "页面")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = PageFallBack.class)
public interface PageFeign {
    
    @ApiOperation(value = "查询页面分享信息")
    @RequestMapping(value = "/api/page/shareConfig")
    Result<PageShareConfigBO> pageShareConfig(@RequestParam("pageCode")String pageCode);
    
    @ApiOperation(value = "查询页面详情")
    @RequestMapping(value = "/api/page/detail")
    Result<PageBO> detail(@RequestParam("pageCode")String pageCode);

    @ApiOperation(value = "查询页面详情")
    @RequestMapping(value = "/api/page/detailByVersion")
    Result<PageBO> detailByVersion(@RequestParam("pageCode") String pageCode, @RequestParam("version") String version);

    @ApiOperation(value = "查询页面信息")
    @RequestMapping(value = "/api/page/detailByType")
    Result<PageBO> detailByType(@RequestParam("ownerId")String ownerId, @RequestParam("pageType")String pageType);

    @ApiOperation(value = "批量查询页面信息")
    @PostMapping(value = "/api/page/findDetailByCache")
    Result<Map<String,PageBO>> findDetailByCache(@RequestBody List<String> pageCodeList);
}
