package com.mengxiang.mshop.cms.service.facade.common.feign.template;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.fallback.TemplateFallBack;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "模版")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign",fallback = TemplateFallBack.class)
public interface TemplateFeign {

    @ApiOperation(value = "模版列表")
    @RequestMapping(value = "/api/page/template/detail")
    Result<PageTemplateResult> detail(@RequestParam("templateCode")String templateCode);

    @ApiOperation(value = "模版列表")
    @RequestMapping(value = "/api/page/template/list")
    Result<List<PageTemplateResult>> list (@RequestParam("ownerId")String ownerId, @RequestParam("ownerType")String ownerType,@RequestParam("pageUseType")String pageUseType);
}
