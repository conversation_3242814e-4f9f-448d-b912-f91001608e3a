package com.mengxiang.mshop.cms.service.facade.common.feign.page;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.service.facade.common.fallback.PageManagerFallBack;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "页面(后台接口不抗量)")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = PageManagerFallBack.class)
public interface PageManagerFeign {

    @ApiOperation(value = "保存页面信息")
    @PostMapping(value = "/api/page/manager/save")
    Result<PageBO> save(@RequestBody SavePageRequest request);

    @ApiOperation(value = "页面信息")
    @RequestMapping(value = "/api/page/manager/setPageInvalidation")
    Result<Boolean> setPageInvalidation(@RequestParam("pageCode") String pageCode, @RequestParam("version") String version,@RequestParam("updateBy")String updateBy,@RequestParam("updateUserId")String updateUserId);

    @ApiOperation(value = "页面信息")
    @RequestMapping(value = "/api/page/manager/detail")
    Result<PageBO> detail(@RequestParam("pageCode") String pageCode, @RequestParam("version") String version);

    @ApiOperation(value = "恢复至上一个发布版本")
    @RequestMapping(value = "/api/page/manager/detailToBeforePublished")
    Result<PageBO> detailToBeforePublished(@RequestParam("pageCode")String pageCode);

    @ApiOperation(value = "复制并创建页面")
    @RequestMapping(value = "/api/page/manager/detailToNewPage")
    Result<PageBO> detailToNewPage(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version);

    @ApiOperation(value = "预览")
    @RequestMapping(value = "/api/page/manager/preview")
    Result<String> preview(@RequestParam("ownerType")String ownerType,@RequestParam("pageCode")String pageCode,@RequestParam("role")String role);

    @ApiOperation(value = "页面列表条件分页查询")
    @PostMapping(value = "/api/page/manager/pageSelect")
    Result<Pagination<PageSelectResult>> pageSelect(@RequestBody PageSearchRequest req);

    @ApiOperation(value = "页面列表条件分页查询")
    @PostMapping(value = "/api/page/manager/pageSelectV2")
    Result<Pagination<PageSelectResult>> pageSelectV2(@RequestBody PageSearchRequest req);

    @ApiOperation(value = "模版查页面信息")
    @RequestMapping(value = "/api/page/manager/detailByTemplate")
    Result<PageBO> detailByTemplate(@RequestParam("templateCode")String templateCode,@RequestParam("ownerId")String ownerId,@RequestParam("ownerType")String ownerType);

    @ApiOperation(value = "设置成主页")
    @RequestMapping(value = "/api/page/manager/setPageIndex")
    Result<Void> setPageIndex(@RequestParam("pageCode")String pageCode,@RequestParam("ownerId")String ownerId,@RequestParam("pageType")String pageType,@RequestParam("ownerType")String ownerType);
}
