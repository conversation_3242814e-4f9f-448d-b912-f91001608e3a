package com.mengxiang.mshop.cms.service.facade.common.feign.component;


import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.fallback.ComponentDetailFallBack;
import com.mengxiang.mshop.cms.core.model.request.ComponetCreateRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "组件详情")
@FeignClient(name = "mshop-cms-center" , path = "/api/mshop/mshop-cms-center/feign",fallback = ComponentDetailFallBack.class)
public interface ComponentDetailFeign {
    
    @ApiOperation(value = "创建组件详情")
    @PostMapping(path = "/api/component/detail/create")
    Result<String> create(@RequestBody ComponetCreateRequest req);
}
