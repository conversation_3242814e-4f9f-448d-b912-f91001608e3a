package com.mengxiang.mshop.cms.service.facade.common.feign.operationLog;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.fallback.OperationLogFallBack;
import com.mengxiang.mshop.cms.core.model.request.OperationLogRequest;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.result.OperationLogResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "操作日志")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign",fallback = OperationLogFallBack.class)
public interface OperationLogFeign {
    
    @ApiOperation(value = "操作记录分页查询")
    @PostMapping(value = "/api/operationLog/page")
    Result<Pagination<OperationLogResult>> operationLogPage(@RequestBody OperationLogRequest req);

    @ApiOperation(value = "操作记录保存")
    @PostMapping(value = "/api/operationLog/save")
    Result<Boolean> saveOperationLog(@RequestBody OperationLogSaveRequest req);
}
