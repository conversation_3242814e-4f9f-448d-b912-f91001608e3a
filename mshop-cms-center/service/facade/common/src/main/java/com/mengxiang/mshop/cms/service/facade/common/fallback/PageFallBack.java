package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PageShareConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.PageFeign;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class PageFallBack implements PageFeign {
    
    @Override
    public Result<PageShareConfigBO> pageShareConfig(String pageCode) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
    
    @Override
    public Result<PageBO> detail(String pageCode) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<PageBO> detailByVersion(String pageCode, String version) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<PageBO> detailByType(String ownerId,String pageType) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Map<String, PageBO>> findDetailByCache(List<String> pageCodeList) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
