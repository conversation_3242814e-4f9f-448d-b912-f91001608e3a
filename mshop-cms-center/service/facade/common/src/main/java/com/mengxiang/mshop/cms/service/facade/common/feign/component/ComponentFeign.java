package com.mengxiang.mshop.cms.service.facade.common.feign.component;


import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.fallback.ComponentFallBack;
import com.mengxiang.mshop.cms.core.model.request.ComponetAggregationRequest;
import com.mengxiang.mshop.cms.core.model.request.ComponetCreateRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "组件")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign",fallback = ComponentFallBack.class)
public interface ComponentFeign {
    
    @ApiOperation(value = "创建组件")
    @PostMapping(path = "/api/component/create")
    Result<String> create(@RequestBody ComponetCreateRequest req);

    @ApiOperation(value = "组件详情")
    @PostMapping(path = "/api/component/detail")
    Result<String> detail(ComponetAggregationRequest req);
}
