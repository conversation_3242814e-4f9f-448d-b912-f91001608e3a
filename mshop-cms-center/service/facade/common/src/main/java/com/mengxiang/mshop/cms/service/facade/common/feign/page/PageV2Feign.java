package com.mengxiang.mshop.cms.service.facade.common.feign.page;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.service.facade.common.fallback.PageV2FallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "页面查询 支持用户分群")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = PageV2FallBack.class)
public interface PageV2Feign {

    @ApiOperation(value = "查询页面详情 支持用户分群")
    @PostMapping(value = "/api/page/v2/detail")
    Result<PageBO> detailV2(@RequestBody PageInfoRequest request);

    @ApiOperation(value = "查询页面详情 支持用户分群")
    @PostMapping(value = "/api/page/v2/detailByVersion")
    Result<PageBO> detailByVersionV2(@RequestBody PageInfoRequest request);

    @ApiOperation(value = "查询页面信息 支持用户分群")
    @PostMapping(value = "/api/page/v2/detailByType")
    Result<PageBO> detailByTypeV2(@RequestBody PageInfoRequest request);

    @ApiOperation(value = "预览页面信息")
    @PostMapping(value = "/api/page/detailByPreview")
    Result<PageBO> detailByPreview(@RequestBody PageInfoRequest request);

}
