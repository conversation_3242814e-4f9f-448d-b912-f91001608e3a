package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.PageV2Feign;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class PageV2FallBack implements PageV2Feign {

    @Override
    public Result<PageBO> detailV2(PageInfoRequest request) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<PageBO> detailByVersionV2(PageInfoRequest request) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<PageBO> detailByTypeV2(PageInfoRequest request) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<PageBO> detailByPreview(PageInfoRequest request) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
