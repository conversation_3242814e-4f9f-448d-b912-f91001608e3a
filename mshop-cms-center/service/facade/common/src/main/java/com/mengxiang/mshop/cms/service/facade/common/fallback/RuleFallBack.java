package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.service.facade.common.feign.rule.RuleFeign;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class RuleFallBack implements RuleFeign {

    @Override
    public Result<List<PageRuleInfoResult>> rules(SellRuleGetBatchRequest request) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<List<SafeModelRuleLabelResult>> findSafeModelRuleLabel() {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
