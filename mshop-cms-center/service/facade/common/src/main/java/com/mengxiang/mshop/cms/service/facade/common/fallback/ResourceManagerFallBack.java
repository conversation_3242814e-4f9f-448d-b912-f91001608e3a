package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.resource.*;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.service.facade.common.feign.resource.ResourceManagerFeign;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class ResourceManagerFallBack implements ResourceManagerFeign {


    @Override
    public Result<Pagination<BaseResourceBO>> selectPage(ResourceRequest req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> saveDiamondResource(DiamondResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<List<DiamondResourceBO.DiamondResourceConfig>> findDiamondResourceList(DiamondResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> saveBannerResource(BannerResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<BannerResourceBO> getBannerResourceById(BannerResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> saveNavigationResource(NavigationResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<NavigationResourceBO> getNavigationResourceById(NavigationResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> savePlatPageData(NavigationResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<NavigationResourceBO> getPlatPageDataByTenantInfo(@RequestBody NavigationResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> saveStartupAdvertisement(StartupAdvertisementBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<StartupAdvertisementBO> getStartupAdvertisementById(StartupAdvertisementBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> deleteById(BaseResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<Void> updateStatus(BaseResourceBO req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
