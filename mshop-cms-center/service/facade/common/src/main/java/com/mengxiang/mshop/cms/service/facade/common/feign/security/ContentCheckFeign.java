package com.mengxiang.mshop.cms.service.facade.common.feign.security;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.fallback.ContentCheckFallBack;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 */
@ApiOperation(value = "内容检测")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign",fallback = ContentCheckFallBack.class)
public interface ContentCheckFeign {
    
    @ApiOperation("内容检查 文本|图片|视频")
    @PostMapping("/api/content/contextCheck")
    Result<List<ContentCheckResponse>> contextTextCheck(@RequestBody ContentCheckRequest req);
}
