package com.mengxiang.mshop.cms.service.facade.common.feign.resource;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.resource.BannerResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.ResourceAggBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.StartupAdvertisementBO;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.service.facade.common.fallback.ResourceFallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "资源位查询")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = ResourceFallBack.class)
public interface ResourceFeign {

    @ApiOperation(value = "聚合查询资源位信息")
    @PostMapping(value = "/api/resource/query/findResourceAgg")
    Result<ResourceAggBO> findResourceAgg(@RequestBody ResourceRequest request);

    @ApiOperation(value = "查询开机广告")
    @PostMapping(value = "/api/resource/query/findStartupAdvertisement")
    Result<StartupAdvertisementBO> findStartupAdvertisement(@RequestBody ResourceRequest request);

    @ApiOperation(value = "查询banner")
    @PostMapping(value = "/api/resource/query/findBannerList")
    Result<List<BannerResourceBO>> findBannerList(@RequestBody ResourceRequest request);

    @ApiOperation(value = "查询导航")
    @PostMapping(value = "/api/resource/query/findNavigation")
    Result<List<NavigationResourceBO.ResourceConfig>> findNavigation(@RequestBody ResourceRequest request);
}
