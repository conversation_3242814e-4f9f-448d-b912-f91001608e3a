package com.mengxiang.mshop.cms.service.facade.common.feign.page;
import com.mengxiang.base.common.model.result.Result;

import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketRuleBO;
import com.mengxiang.mshop.cms.service.facade.common.fallback.PrivateMarketFallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@ApiOperation(value = "私密会场相关接口")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = PrivateMarketFallBack.class)
public interface PrivateMarketFeign {

    /**
     * 查询所有有效的私密策略
     */
    @ApiOperation(value = "查询所有有效的私密策略")
    @PostMapping(value = "/api/page/privateMarket/queryAllPrivateRule")
    Result<List<PrivateMarketRuleBO>> queryAllPrivateRule();

    /**
     * 根据活动id查询有效的私密策略
     */
    @ApiOperation(value = "根据活动id查询有效的私密策略")
    @PostMapping(value = "/api/page/privateMarket/queryPrivateRuleByActivityId")
    Result<List<PrivateMarketRuleBO>> queryPrivateRuleByActivityId(@RequestParam("activityId") String activityId);

}
