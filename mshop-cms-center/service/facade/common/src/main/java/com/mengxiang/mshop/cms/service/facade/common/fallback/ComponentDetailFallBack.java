package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.service.facade.common.feign.component.ComponentDetailFeign;
import com.mengxiang.mshop.cms.core.model.request.ComponetCreateRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class ComponentDetailFallBack implements ComponentDetailFeign {
    @Override
    public Result<String> create(ComponetCreateRequest req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
