package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketRuleBO;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.PrivateMarketFeign;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PrivateMarketFallBack implements PrivateMarketFeign {

    @Override
    public Result<List<PrivateMarketRuleBO>> queryAllPrivateRule() {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());

    }

    @Override
    public Result<List<PrivateMarketRuleBO>> queryPrivateRuleByActivityId(String activityId) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
