package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import com.mengxiang.mshop.cms.service.facade.common.feign.template.TemplateFeign;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class TemplateFallBack implements TemplateFeign {
    @Override
    public Result<PageTemplateResult> detail(String templateCode) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }

    @Override
    public Result<List<PageTemplateResult>> list(String ownerId, String ownerType,String pageUseType) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
