package com.mengxiang.mshop.cms.service.facade.common.feign.page;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoResp;
import com.mengxiang.mshop.cms.service.facade.common.fallback.PageManagerFallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "工作流(后台接口不抗量)")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = PageManagerFallBack.class)
public interface WorkbenchFeign {

    @ApiOperation("查询工作流信息")
    @PostMapping("/api/page/wb/findWorkflowInfo")
    Result<WorkflowInfoResp> findWorkflowInfo(@RequestBody WorkflowInfoRequest req);
}
