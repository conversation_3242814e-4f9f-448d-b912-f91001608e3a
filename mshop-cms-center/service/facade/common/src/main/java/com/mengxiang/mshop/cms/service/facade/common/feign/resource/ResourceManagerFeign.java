package com.mengxiang.mshop.cms.service.facade.common.feign.resource;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.resource.*;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.service.facade.common.fallback.ResourceManagerFallBack;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiOperation(value = "资源位查询(后台接口不抗量)")
@FeignClient(name = "mshop-cms-center", path = "/api/mshop/mshop-cms-center/feign", fallback = ResourceManagerFallBack.class)
public interface ResourceManagerFeign {

    @ApiOperation(value = "资源位列表查询")
    @PostMapping(value = "/api/resource/selectPage")
    Result<Pagination<BaseResourceBO>> selectPage(@RequestBody ResourceRequest req);

    @ApiOperation(value = "保存金刚位替换数据")
    @PostMapping(value = "/api/resource/saveDiamondResource")
     Result<Void> saveDiamondResource(@RequestBody DiamondResourceBO req);

    @ApiOperation(value = "查询金刚位替换数据")
    @PostMapping(value = "/api/resource/findDiamondResourceList")
     Result<List<DiamondResourceBO.DiamondResourceConfig>> findDiamondResourceList(@RequestBody DiamondResourceBO req);

    @ApiOperation(value = "保存banner组件")
    @PostMapping(value = "/api/resource/saveBannerResource")
     Result<Void> saveBannerResource(@RequestBody BannerResourceBO req);

    @ApiOperation(value = "查询banner组件")
    @PostMapping(value = "/api/resource/getBannerResourceById")
     Result<BannerResourceBO> getBannerResourceById(@RequestBody BannerResourceBO req);

    @ApiOperation(value = "保存导航组件")
    @PostMapping(value = "/api/resource/saveNavigationResource")
    Result<Void> saveNavigationResource(@RequestBody NavigationResourceBO req);

    @ApiOperation(value = "查询导航组件")
    @PostMapping(value = "/api/resource/getNavigationResourceById")
    Result<NavigationResourceBO> getNavigationResourceById(@RequestBody NavigationResourceBO req);

    @ApiOperation(value = "保存平台页面排序值")
    @PostMapping(value = "/api/resource/savePlatPageData")
    Result<Void> savePlatPageData(@RequestBody NavigationResourceBO req);

    @ApiOperation(value = "查询平台页面排序值")
    @PostMapping(value = "/api/resource/getPlatPageDataByTenantInfo")
    public Result<NavigationResourceBO> getPlatPageDataByTenantInfo(@RequestBody NavigationResourceBO req);

    @ApiOperation(value = "保存开机广告")
    @PostMapping(value = "/api/resource/saveStartupAdvertisement")
    Result<Void> saveStartupAdvertisement(@RequestBody StartupAdvertisementBO req);

    @ApiOperation(value = "查询开机广告")
    @PostMapping(value = "/api/resource/getStartupAdvertisementById")
    Result<StartupAdvertisementBO> getStartupAdvertisementById(@RequestBody StartupAdvertisementBO req);


    @ApiOperation(value = "删除资源位")
    @PostMapping(value = "/api/resource/deleteById")
    Result<Void> deleteById(@RequestBody BaseResourceBO req);
    @ApiOperation(value = "更新状态")
    @PostMapping(value = "/api/resource/updateStatus")
    Result<Void> updateStatus(@RequestBody BaseResourceBO req);
}
