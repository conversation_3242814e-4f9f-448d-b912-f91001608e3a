package com.mengxiang.mshop.cms.service.facade.common.fallback;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.service.facade.common.feign.security.ContentCheckFeign;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Auther: zhangmoxun
 * @Date: 2023/3/27
 * @Description:
 */
@Component
public class ContentCheckFallBack implements ContentCheckFeign {
    @Override
    public Result<List<ContentCheckResponse>> contextTextCheck(ContentCheckRequest req) {
        return Result.error(ErrorEnum.SERVER_TIME_OUT.getCode(),ErrorEnum.SERVER_TIME_OUT.getMsg());
    }
}
