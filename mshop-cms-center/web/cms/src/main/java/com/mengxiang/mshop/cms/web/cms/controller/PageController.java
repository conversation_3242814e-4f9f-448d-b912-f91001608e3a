package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PageShareConfigBO;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.core.service.business.PageQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 页面
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "端上页面")
public class PageController {

    @Autowired
    private PageQueryService pageQueryService;
    
    @ApiOperation(value = "查询页面分享信息")
    @RequestMapping(value = "/api/page/shareConfig")
    public Result<PageShareConfigBO> pageShareConfig(@RequestParam("pageCode")String pageCode) {
        PageBO result = pageQueryService.detailByCache(pageCode,null);
        if (Objects.nonNull(result)) {
            return Result.success(result.getShareConfig());
        }
        return Result.success(null);
    }
    
    @ApiOperation(value = "查询页面详情")
    @RequestMapping(value = "/api/page/detail")
    public Result<PageBO> detail(@RequestParam("pageCode")String pageCode) {
        PageBO result = pageQueryService.detailByCache(pageCode,null);
        return Result.success(result);
    }

    @ApiOperation(value = "页面类型查页面信息")
    @GetMapping(value = "/api/page/detailByType")
    public Result<PageBO> detailByType(@RequestParam("ownerId")String ownerId,@RequestParam("pageType")String pageType) {
        PageBO result = pageQueryService.detailByPageType(ownerId,pageType);
        return Result.success(result);
    }

    @ApiOperation(value = "查询页面详情")
    @GetMapping(value = "/api/page/detailByVersion")
    public Result<PageBO> detailByVersion(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        PageBO result = pageQueryService.detailByCache(pageCode,version);
        return Result.success(result);
    }

    @ApiOperation(value = "预览页面信息 不扛量")
    @PostMapping(value = "/api/page/detailByPreview")
    public Result<PageBO> detailByPreview(@RequestBody PageInfoRequest request) {
        PageBO result = pageQueryService.detailByPreview(request.getPageCode(),request.getVersion(),request.getAggrBaseReqModule());
        return Result.success(result);
    }


    @ApiOperation(value = "批量查询页面信息")
    @PostMapping(value = "/api/page/findDetailByCache")
    public Result<Map<String,PageBO>> findDetailByCache(@RequestBody List<String> pageCodeList) {
        Map<String,PageBO> result = pageQueryService.findDetailByCache(pageCodeList);
        return Result.success(result);
    }

}
