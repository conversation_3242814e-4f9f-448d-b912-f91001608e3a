package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.request.workflow.*;
import com.mengxiang.mshop.cms.core.service.business.WorkbenchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "工作流")
public class WorkbenchController {
    
    @Autowired
    private WorkbenchService workbenchService;

    @ApiOperation(value = "创建工作流工单")
    @PostMapping(value = "/api/page/wb/createProcNo")
    public Result<Void> createProcNo(@RequestBody SaveProcRequest req) {

        return workbenchService.createProcNo(req);
    }
    @ApiOperation(value = "重新发起工作流")
    @PostMapping(value = "/api/page/wb/rejectReStart")
    public Result<Void> rejectReStart(@RequestBody ReStartProcRequest req) {

        return workbenchService.rejectReStart(req);
    }


    @ApiOperation("流程回调通知")
    @PostMapping("/api/page/wb/callBack")
    public Result<Void> callBack(@RequestBody WorkflowCallBackRequest req) {
        workbenchService.callBack(req);
        return Result.success();
    }
    @ApiOperation("查询工作流信息")
    @PostMapping("/api/page/wb/findWorkflowInfo")
    public Result<WorkflowInfoResp> findWorkflowInfo(@RequestBody WorkflowInfoRequest req) {
        WorkflowInfoResp resp = workbenchService.findWorkflowInfo(req);
        return Result.success(resp);
    }

    @ApiOperation("查询工作流状态")
    @PostMapping("/api/page/wb/findProcessStatus")
    public Result<ProcessStatusResp> findProcessStatus(@RequestBody ProcessStatusRequest req) {
        ProcessStatusResp resp = workbenchService.findProcessStatus(req);
        return Result.success(resp);
    }


    @ApiOperation("超时提醒通知")
    @PostMapping("/api/page/wb/notice")
    public Result<Void> notice() {
         workbenchService.notice();
        return Result.success();
    }

    @ApiOperation("超时驳回")
    @PostMapping("/api/page/wb/cancel")
    public Result<Void> cancel() {
        workbenchService.cancel();
        return Result.success();
    }
}
