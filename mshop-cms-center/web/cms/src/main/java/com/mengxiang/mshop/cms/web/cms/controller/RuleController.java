package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.core.service.business.BIDataService;
import com.mengxiang.mshop.cms.core.service.business.PageQueryV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 页面模版
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "规则信息")
public class RuleController {
    
    @Autowired
    private PageQueryV2Service pageQueryV2Service;

    @Autowired
    private BIDataService biDataService;
    
    @ApiOperation(value = "批量获取规则信息")
    @PostMapping(value = "/api/page/rules")
    public Result<List<PageRuleInfoResult>> rules(@RequestBody SellRuleGetBatchRequest request) {
        List<PageRuleInfoResult> list = pageQueryV2Service.getRuleBatch(request.getRuleIds());
        return Result.success(list);
    }

    @ApiOperation(value = "查询选品中心营销标签")
    @GetMapping(value = "/api/page/manager/findSafeModelRuleLabel")
    public Result<List<SafeModelRuleLabelResult>> findSafeModelRuleLabel() {
        List<SafeModelRuleLabelResult> list = biDataService.findSafeModelRuleLabel();
        return Result.success(list);
    }

}
