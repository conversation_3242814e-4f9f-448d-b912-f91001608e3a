package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.mengxiang.mshop.cms.core.service.business.PageQueryService;
import com.mengxiang.mshop.cms.core.service.business.TemplateQueryService;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.util.UrlUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 页面
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "页面")
public class PageManagerController {
    
    @Autowired
    private PageOperateService pageOperateService;

    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private TemplateQueryService templateQueryService;

    @Value("${mer.shop.preview.url:akapp://mengxiang.com/shopDetail}")
    private String merShopPreviewUrl;

    @Value("${mer.shop.micro.preview.url:akapp://mengxiang.com/shopMicro}")
    private String merShopMicroPreviewUrl;

    @ApiOperation(value = "保存页面")
    @PostMapping(value = "/api/page/manager/save")
    public Result<PageBO> save(@RequestBody SavePageRequest request) {
        PageBO page = pageOperateService.save(request);
        Result<PageBO> result = Result.success(page);
        if(CollectionUtils.isNotEmpty(page.getContentCheckResults())) {
            page.setContentCheckResults(page.getContentCheckResults());
            result.setSuccess(false);
            result.setCode(ErrorEnum.VIDEO_CHECK_ERROR.getCode());
            result.setMessage(ErrorEnum.VIDEO_CHECK_ERROR.getMsg());
            result.setData(page);
        }
        return result;
    }

    @ApiOperation(value = "失效页面")
    @GetMapping(value = "/api/page/manager/setPageInvalidation")
    public Result<Boolean> setPageInvalidation(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version,@RequestParam("updateBy")String updateBy,@RequestParam("updateUserId")String updateUserId) {
        boolean res = pageOperateService.setPageInvalidation(pageCode,updateBy,updateUserId);
        return Result.success(res);
    }

    @ApiOperation(value = "查询页面详情")
    @GetMapping(value = "/api/page/manager/detail")
    public Result<PageBO> detail(@RequestParam("pageCode")String pageCode, @RequestParam("version")String version) {
        PageBO result = pageQueryService.detailByDb(pageCode, version);
        return Result.success(result);
    }


    @ApiOperation(value = "页面列表条件分页查询")
    @PostMapping(value = "/api/page/manager/pageSelect")
    public Result<Pagination<PageSelectResult>> pageSelect(@RequestBody PageSearchRequest req) {
        if (StringUtils.isBlank(req.getOwnerType())) {
            return Result.error("页面ownerType不能为空");
        }
        return pageQueryService.pageSelect(req);
    }


    @ApiOperation(value = "页面列表条件分页查询V2")
    @PostMapping(value = "/api/page/manager/pageSelectV2")
    public Result<Pagination<PageSelectResult>> pageSelectV2(@RequestBody PageSearchRequest req) {
        return pageQueryService.pageSelectV2(req);
    }


    @ApiOperation(value = "恢复至上一个发布版本")
    @GetMapping(value = "/api/page/manager/detailToBeforePublished")
    public Result<PageBO> detailToBeforePublished(@RequestParam("pageCode")String pageCode) {
        PageBO pageResult = pageQueryService.detailToBeforePublished(pageCode);
        if (Objects.isNull(pageResult)) {
            return Result.error(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getCode(),CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        return Result.success(pageResult);
    }

    @ApiOperation(value = "复制并创建页面")
    @GetMapping(value = "/api/page/manager/detailToNewPage")
    public Result<PageBO> detailToNewPage(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        PageBO pageResult = pageQueryService.detailToNewPage(pageCode,version);
        if (Objects.isNull(pageResult)) {
            return Result.error(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getCode(),CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        return Result.success(pageResult);
    }

    @ApiOperation(value = "使用模版")
    @GetMapping(value = "/api/page/manager/detailByTemplate")
    public Result<PageBO> detailByTemplate(@RequestParam("templateCode")String templateCode,@RequestParam("ownerId")String ownerId,@RequestParam("ownerType")String ownerType) {
        PageTemplateResult template = templateQueryService.queryByTemplateCode(templateCode);
        if (Objects.isNull(template)) {
            return Result.error(CmsErrorCodeEnum.TEMPLATE_ISNOTEXISTS_ERROR.getCode(),CmsErrorCodeEnum.TEMPLATE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        PageBO result = pageQueryService.queryByTemplateCode(templateCode,ownerId,ownerType);
        if (Objects.isNull(result)) {
            return Result.error(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getCode(),CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        return Result.success(result);
    }

    @ApiOperation(value = "预览")
    @GetMapping(value = "/api/page/manager/preview")
    public Result<String> preview(@RequestParam("ownerType")String ownerType,@RequestParam("pageCode")String pageCode,@RequestParam("role")String role) {
        PageBO result = pageQueryService.detailByDb(pageCode,null);
        if (Objects.isNull(result)) {
            return Result.error(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getCode(),CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        String previewUrl = merShopPreviewUrl;
        if (result.getType().equals(PageType.SHOP_MICRO_PAGE.getType())) {
            previewUrl = merShopMicroPreviewUrl;
        }
        Map<String, Object> params = new HashMap<>(4);
        params.put("shopCode", result.getOwnerId());
        params.put("pageCode", pageCode);
        params.put("version", result.getVersion());
        String url = UrlUtils.buildUrl(previewUrl,params);
        return Result.success(url);
    }


    @ApiOperation(value = "设置成主页")
    @GetMapping(value = "/api/page/manager/setPageIndex")
    public Result<Void> setPageIndex(@RequestParam("pageCode")String pageCode,@RequestParam("ownerId")String ownerId
            ,@RequestParam("pageType")String pageType,@RequestParam("ownerType")String ownerType) {
        pageOperateService.setPageIndex(pageCode,pageType,ownerId,ownerType);
        return Result.success();
    }
}
