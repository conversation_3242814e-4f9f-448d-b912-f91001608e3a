package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.core.service.business.PageQueryV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 页面
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "端上页面")
public class PageV2Controller {

    @Autowired
    private PageQueryV2Service pageQueryV2Service;

    @ApiOperation(value = "查询页面详情")
    @PostMapping(value = "/api/page/v2/detail")
    public Result<PageBO> detailV2(@RequestBody PageInfoRequest request) {
        PageBO result = pageQueryV2Service.detailByCache(request);
        return Result.success(result);
    }

    @ApiOperation(value = "页面类型查页面信息")
    @PostMapping(value = "/api/page/v2/detailByType")
    public Result<PageBO> detailByTypeV2(@RequestBody PageInfoRequest request) {
        PageBO result = pageQueryV2Service.detailByPageType(request);
        return Result.success(result);
    }

    @ApiOperation(value = "查询页面详情")
    @PostMapping(value = "/api/page/v2/detailByVersion")
    public Result<PageBO> detailByVersionV2(@RequestBody PageInfoRequest request) {
        PageBO result = pageQueryV2Service.detailByCache(request);
        return Result.success(result);
    }
}
