package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.BannerResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.ResourceAggBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.StartupAdvertisementBO;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.core.service.business.ResourceQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 页面
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "端上页面")
public class ResourceController {

    @Autowired
    private ResourceQueryService resourceQueryService;

    @ApiOperation(value = "聚合查询资源位信息")
    @PostMapping(value = "/api/resource/query/findResourceAgg")
    public Result<ResourceAggBO> findResourceAgg(@RequestBody ResourceRequest request) {
        ResourceAggBO result =new ResourceAggBO();
        result.setBannerList(resourceQueryService.findBannerList(request));
        result.setDiamondList(resourceQueryService.findDiamond(request));
        return Result.success(result);
    }

    @ApiOperation(value = "查询开机广告")
    @PostMapping(value = "/api/resource/query/findStartupAdvertisement")
    public Result<StartupAdvertisementBO> findStartupAdvertisement(@RequestBody ResourceRequest request) {
        return Result.success(resourceQueryService.findStartupAdvertisement(request));
    }

    @ApiOperation(value = "查询banner")
    @PostMapping(value = "/api/resource/query/findBannerList")
    public Result<List<BannerResourceBO>> findBannerList(@RequestBody ResourceRequest request) {
        return Result.success(resourceQueryService.findBannerList(request));
    }

    @ApiOperation(value = "查询导航")
    @PostMapping(value = "/api/resource/query/findNavigation")
    public Result<List<NavigationResourceBO.ResourceConfig>> findNavigation(@RequestBody ResourceRequest request) {
        return Result.success(resourceQueryService.findNavigation(request));
    }
}
