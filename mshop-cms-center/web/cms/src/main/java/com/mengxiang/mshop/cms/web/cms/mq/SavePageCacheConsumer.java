package com.mengxiang.mshop.cms.web.cms.mq;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2021/9/2 15:12
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "AKC_CMS_CONFERENCE_TOPIC", consumerGroup = "AKC_MSHOP_CMS_CENTER_GROUP", selectorExpression = "AKC_CMS_CONFERENCE_TAG",messageModel = MessageModel.BROADCASTING)
public class SavePageCacheConsumer implements RocketMQListener<String> {


    @Autowired
    private PageCacheService pageCacheService;

    protected static Gson gson = new Gson();

    @Override
    public void onMessage(String message) {
        log.info("SavePageCacheConsumer action msg:{}", message);
        if(Objects.isNull(message)){
            return;
        }
        String msg = gson.fromJson(message, String.class);
        try {
            PageSelectResult jsonObject =  JSON.parseObject(msg, PageSelectResult.class);
            if(Objects.isNull(jsonObject)){
                return;
            }
            if(PageInstanceStatusEnum.PUBLISH.getCode().equals(jsonObject.getStatus())){
                //已发布
                pageCacheService.putPageCaffeineCache(jsonObject.getPageCode(),jsonObject.getVersion());
            }else if(PageInstanceStatusEnum.DISABLED.getCode().equals(jsonObject.getStatus()) || PageInstanceStatusEnum.EXECUTORY.getCode().equals(jsonObject.getStatus())){
                //已失效
                pageCacheService.clearPageCaffeineCache(jsonObject.getPageCode(),jsonObject.getVersion());
            }
        } catch (Exception e) {
            log.error("SavePageCacheConsumer error msg:{}", message, e);
            throw e;
        }
    }


}
