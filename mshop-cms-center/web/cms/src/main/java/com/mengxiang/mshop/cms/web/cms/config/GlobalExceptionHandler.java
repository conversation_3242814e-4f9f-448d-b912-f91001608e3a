package com.mengxiang.mshop.cms.web.cms.config;

import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常捕捉
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
@Order(1)
public class GlobalExceptionHandler {


    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public Result<?> exceptionHandle(IllegalArgumentException e) {
        Logger.info("参数校验异常", e);
        return Result.error(CmsErrorCodeEnum.ILLEGAL_ARGUMENT.getCode(), e.getMessage());
    }
    /**
     * 异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Result errorHandler(Exception ex, HttpServletRequest request) {
        if (null != request) {
            StringBuilder strBuild = new StringBuilder();
            strBuild.append("global catch reqUrl:");
            strBuild.append(request.getRequestURI());
            if (HttpMethod.GET.matches(request.getMethod())) {
                strBuild.append(" queryString:");
                strBuild.append(request.getQueryString());
            }
            log.error(strBuild.toString(), ex);
            ex.printStackTrace();
            return Result.error("系统内部错误,请稍后重试");
        } else {
            log.error("global catch", ex);
            ex.printStackTrace();
            return Result.error("系统内部错误,请稍后重试");
        }
    }
    
    /**
     * 业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public Result businessException(BusinessException exception, HttpServletRequest request) {
        return Result.error(exception.getCode(), exception.getMessage());
    }
}
