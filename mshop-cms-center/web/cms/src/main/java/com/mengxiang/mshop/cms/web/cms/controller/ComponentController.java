package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.service.business.ComponentService;
import com.mengxiang.mshop.cms.core.model.request.ComponetAggregationRequest;
import com.mengxiang.mshop.cms.core.model.request.ComponetCreateRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @menu:组件
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "组件")
public class ComponentController {

    @Autowired
    private ComponentService componentService;

    @ApiOperation(value = "创建组件")
    @PostMapping("/api/component/create")
    public Result<String> create(@RequestBody ComponetCreateRequest req){
        String componentCode = componentService.create(req);
        return Result.success(componentCode);
    }

    @ApiOperation(value = "组件详情")
    @PostMapping("/api/component/detail")
    public Result<String> detail(@RequestBody ComponetAggregationRequest req){
        String content = componentService.detail(req);
        return Result.success(content);
    }
}
