package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.service.business.ComponentDetailService;
import com.mengxiang.mshop.cms.core.model.request.ComponetAggregationRequest;
import com.mengxiang.mshop.cms.core.model.request.ComponetDetailCreateRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @menu 组件详情
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "组件详情")
public class ComponentDetailController {

    @Autowired
    private ComponentDetailService componentDetailService;

    @ApiOperation(value = "创建组件")
    @PostMapping("/api/component/detail/create")
    public Result<String> create(@RequestBody ComponetDetailCreateRequest req){
        String componentDetailCode = componentDetailService.create(req);
        return Result.success(componentDetailCode);
    }
}
