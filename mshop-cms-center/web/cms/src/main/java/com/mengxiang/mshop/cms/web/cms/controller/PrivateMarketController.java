package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketRuleBO;
import com.mengxiang.mshop.cms.core.service.business.PrivateMarketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "端上页面")
public class PrivateMarketController {

    @Autowired
    private PrivateMarketService privateMarketService;

    @ApiOperation(value = "查询所有有效的私密策略")
    @PostMapping(value = "/api/page/privateMarket/queryAllPrivateRule")
    public Result<List<PrivateMarketRuleBO>> queryAllPrivateRule() {
        List<PrivateMarketRuleBO> privateMarketRuleBOS = privateMarketService.queryAllPrivateRule();
        return Result.success(privateMarketRuleBOS);
    }

    @ApiOperation(value = "根据活动id查询有效的私密策略")
    @PostMapping(value = "/api/page/privateMarket/queryPrivateRuleByActivityId")
    public Result<List<PrivateMarketRuleBO>> queryPrivateRuleByActivityId(@RequestParam("activityId") String activityId) {
        List<PrivateMarketRuleBO> privateMarketRuleBOS = privateMarketService.queryPrivateRuleByActivityId(activityId);
        return Result.success(privateMarketRuleBOS);
    }

}
