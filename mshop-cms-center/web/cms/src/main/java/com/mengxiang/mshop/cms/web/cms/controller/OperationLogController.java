package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.service.business.OperationLogService;
import com.mengxiang.mshop.cms.core.model.request.OperationLogRequest;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.result.OperationLogResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作记录
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "操作记录")
public class OperationLogController {


    @Autowired
    private OperationLogService operationLogService;


    @ApiOperation(value = "操作记录分页查询")
    @PostMapping(value = "/api/operationLog/page")
    public Result<Pagination<OperationLogResult>> operationLogPage(@RequestBody OperationLogRequest req) {
        return operationLogService.operationLogPage(req);
    }

    @ApiOperation(value = "保存操作记录")
    @PostMapping(value = "/api/operationLog/save")
    public Result<Boolean> saveOperationLog(@RequestBody OperationLogSaveRequest req) {

        return operationLogService.saveOperationLog(req);
    }



}
