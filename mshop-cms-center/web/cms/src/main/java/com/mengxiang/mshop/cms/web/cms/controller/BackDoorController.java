package com.mengxiang.mshop.cms.web.cms.controller;

import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDetailDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageContentCheckDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.enums.*;
import com.mengxiang.mshop.cms.core.service.business.*;
import com.mengxiang.mshop.cms.core.service.redis.RedisService;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/12
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
public class BackDoorController {

    @Autowired
    private PageCacheService pageCacheService;

    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private PageOperateService pageOperateService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private MshopComponentInstanceDao mshopComponentInstanceDao;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private MshopComponentInstanceDetailDao mshopComponentInstanceDetailDao;

    @Autowired
    private MshopPageContentCheckDao mshopPageContentCheckDao;


    @Autowired
    private ContentCheckService contentCheckService;

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Autowired
    private ResourceService resourceService;

    @RequestMapping(value = "/backdoor/del")
    public void del (@RequestParam("key")String key) {
        redisService.del(key);
    }

    @RequestMapping(value = "/backdoor/get")
    public Result<String> get (@RequestParam("key")String key) {
        return Result.success(redisService.get(key));
    }

    @RequestMapping(value = "/backdoor/getPageMap")
    public Result<Map<Object, Object>> pageMap (@RequestParam("pageCode")String pageCode){
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        Map<Object, Object> pageMap = redisTemplate.opsForHash().entries(pageCodeKey);
        return Result.success(pageMap);
    }

    @RequestMapping(value = "/backdoor/clearPageVersion")
    public Result<Void> clearPageVersion (@RequestParam("pageCode")String pageCode,@RequestParam("versions")List<String> versions){
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        Map<Object, Object> pageMap = redisTemplate.opsForHash().entries(pageCodeKey);
        if (pageMap != null) {
            versions.stream().forEach(version -> {
                String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
                String pageJson = (String) pageMap.get(pageVersionKey);
                if (StringUtils.isNotEmpty(pageJson)) {
                    redisTemplate.opsForHash().delete(pageCodeKey, pageVersionKey);
                }
            });
        }
        return Result.success();
    }



    @RequestMapping(value = "/backdoor/getPageJsonByCode")
    public Result<String> getPageJsonByCode(@RequestParam("pageCode")String pageCode) {
        Optional<String> pageOpt = pageCacheService.getPageJsonByCode(pageCode);
        return Result.success(pageOpt.orElse(""));
    }

    @RequestMapping(value = "/backdoor/getPageJsonByTypeAndOwnerId")
    public Result<String> getPageJsonByTypeAndOwnerId(@RequestParam("ownerId")String ownerId,@RequestParam("pageType")String pageType) {
        Optional<String> pageOpt = pageCacheService.getPageJsonByTypeAndOwnerId(pageType, ownerId);
        return Result.success(pageOpt.orElse(""));
    }

    @RequestMapping(value = "/backdoor/getPageJsonByVersion")
    public Result<String> getPageJsonByVersion(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        Optional<String> pageOpt = pageCacheService.getPageJsonByVersion(pageCode,version);
        return Result.success(pageOpt.orElse(""));
    }

    @RequestMapping(value = "/backdoor/syncPage")
    public Result<PageBO> syncPage(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        try{
            PageBO page = pageQueryService.detailByDb(pageCode,version);
            boolean result = pageCacheService.syncCache(page);
            if (result) {
                return Result.success(page);
            }
        }catch (Exception ex){
            log.error("syncPage error {},{}",pageCode,version,ex);
        }
        return Result.error("syncPage fail");
    }

    @RequestMapping(value = "/backdoor/compensatePageCache")
    public Result<Boolean> compensatePageCache() {
        boolean result = pageCacheService.compensatePageCache();
        return Result.success(result);
    }

    @RequestMapping(value = "/backdoor/setPageInvalidation")
    public Result<Boolean> setPageInvalidation(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        boolean res = pageOperateService.setPageInvalidation(pageCode,"system","system");
        return Result.success(res);
    }
    @RequestMapping(value = "/backdoor/publishPage")
    public Result<Boolean> publishPage(@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        boolean res = pageOperateService.pageCheckPublish(pageCode,version);
        return Result.success(res);
    }

    @PostMapping(value = "/backdoor/component/updateDynamic")
    public Result<Integer> updateDynamic(@RequestBody MshopComponentInstanceDo mshopComponentInstanceDo) {
        int res = mshopComponentInstanceDao.updateDynamic(mshopComponentInstanceDo);
        return Result.success(res);
    }

    @PostMapping(value = "/backdoor/componentDetail/updateDynamic")
    public Result<Integer> updateDynamic(@RequestBody MshopComponentInstanceDetailDo mshopComponentInstanceDetailDo) {
        int res = mshopComponentInstanceDetailDao.updateDynamic(mshopComponentInstanceDetailDo);
        return Result.success(res);
    }

    @RequestMapping(value = "/backdoor/contentCheckResult")
    public void contentCheckResult () {
        List<MshopPageContentCheckDo> checkDos = mshopPageContentCheckDao
                .queryAll(ContentSuggestTypeEnum.PROCESSING.getSuggesst(), 0L, 500);
        if (CollectionUtils.isEmpty(checkDos)) {
            return;
        }
        List<String> dataIdLists = checkDos.stream().map(MshopPageContentCheckDo::getDataId)
                .collect(Collectors.toList());
        List<List<String>> dataIds = Lists.partition(dataIdLists, 20);
        //每次20个查询
        for (List<String> dataId : dataIds) {
            contentCheckService.contentCheckResult(dataId);

        }

    }

    @RequestMapping(value = "/backdoor/getSequence")
    public Result<String> getSequence (@RequestParam("stuff")String stuff) {
        return Result.success(sequenceGeneratorService.getSequence(stuff));
    }

    @RequestMapping(value = "/backdoor/sendPublishPageMQ")
    public Result<Void> savePageStringCache (@RequestParam("pageCode")String pageCode,@RequestParam("publishVersion")String publishVersion,@RequestParam(value = "status",defaultValue = "4") Integer status) {
        pageOperateService.sendPublishPageMQ(pageCode,publishVersion,status);
        return Result.success();
    }

    @RequestMapping(value = "/backdoor/findCaffeineCache")
    public Result<String> findCaffeineCache (@RequestParam("pageCode")String pageCode) {
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        return Result.success(pageCacheService.findCaffeineCacheByKey(pageCodeKey+":"+PageConstant.CURRENT_PAGE_CACHE_KEY));
    }

    @RequestMapping(value = "/backdoor/findCaffeineCacheByVersion ")
    public Result<String> findCaffeineCacheByVersion (@RequestParam("pageCode")String pageCode,@RequestParam("version")String version) {
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
        return Result.success(pageCacheService.findCaffeineCacheByKey(pageCodeKey+":"+pageVersionKey));
    }

    @RequestMapping(value = "/backdoor/buildCaffeineCache")
    public Result<Void> buildCaffeineCache () {
        pageCacheService.buildCaffeineCache();
        return Result.success();
    }


    @RequestMapping(value = "/backdoor/updatePageByTemplate")
    public Result<String> updatePageByTemplate(@RequestParam("pageCodes")String pageCodes) {
        String test1 = "{\"url\": \"https://file-oss.aikucun.com/xd-img/ea3dea7160982e2df9c1a69e2f23404bb07c4a9d_1682320086948_85.png\", \"width\": 351, \"height\": 98, \"paddingTop\": 12, \"paddingLeft\": 12, \"paddingRight\": 12, \"paddingBottom\": 0, \"borderTopLeftRadius\": 12, \"bulletScreenDisplay\": 1, \"borderTopRightRadius\": 12, \"borderBottomLeftRadius\": 12, \"borderBottomRightRadius\": 12}";
        String test2 = "{\"url\": \"https://akim-oss.aikucun.com/da4b9237bacccdf19c0760cab7aec4a8359010b0_1688960773424_92.png\", \"width\": 351, \"height\": 98, \"paddingTop\": 12, \"paddingLeft\": 12, \"paddingRight\": 12, \"paddingBottom\": 0, \"borderTopLeftRadius\": 12, \"bulletScreenDisplay\": 1, \"borderTopRightRadius\": 12, \"borderBottomLeftRadius\": 12, \"borderBottomRightRadius\": 12}";
        String test3 = "{\"newUserType\": \"2,3\", \"directShowType\": 1}";
        String test4 = "{\"newUserType\": \"1\", \"directShowType\": 1}";
        String timeConfig = "{\"timeList\": [], \"timeCycle\": {}, \"effectiveType\": 1}";

        String testDetail1 = "{\"x\": 18.1875, \"y\": 21, \"width\": 80, \"height\": 80, \"targetId\": \"1\", \"targetType\": \"SECKILL_LIST\"}";
        String testDetail2 = "{\"x\": 106.1875, \"y\": 21, \"width\": 80, \"height\": 80, \"targetId\": \"1\", \"targetType\": \"PROFITADDITIONAL_LIST\"}";
        String testDetail3 = "{\"x\": 198, \"y\": 21, \"width\": 80, \"height\": 80, \"targetId\": \"1\", \"targetType\": \"TOPLIST\"}";
        String testDetail4 = "{\"x\": 280.1875, \"y\": 21, \"width\": 80, \"height\": 80, \"targetId\": \"1\", \"targetType\": \"COUPON_LIST\"}";

        List<String> pageList = Lists.newArrayList(pageCodes.split(","));
        for(String pageCode:pageList){
            try {
                //查询page
                MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(pageCode,null);
                if(Objects.isNull(mshopPageInstanceDo)){
                    log.error("updatePageByTemplate mshopPageInstanceDo is null pageCode:{}",pageCode);
                    continue;
                }
                //查询page下的组件并更新
                List<MshopComponentInstanceDo> componentInstanceDoList =
                        mshopComponentInstanceDao.queryByPageCode(mshopPageInstanceDo.getPageCode(),mshopPageInstanceDo.getVersion());
                for(MshopComponentInstanceDo componentInstanceDo:componentInstanceDoList){
                    if("QUERY".equals(componentInstanceDo.getUseRule()) && "IMAGE".equals(componentInstanceDo.getType())){
                        //更新
                        componentInstanceDo.setMetaConfig(test1);
                        componentInstanceDo.setDirectUserGroup(test3);
                        mshopComponentInstanceDao.updateMetaConfig(componentInstanceDo);
                        //插入新的
                        MshopComponentInstanceDo newComponent =  new MshopComponentInstanceDo();
                        String componentCode = sequenceGeneratorService.getSequence(pageCode);
                        newComponent.setComponentCode(componentCode);
                        newComponent.setTimeConfig(timeConfig);
                        newComponent.setDirectUserGroup(test4);
                        newComponent.setPageCode(componentInstanceDo.getPageCode());
                        newComponent.setVersion(componentInstanceDo.getVersion());
                        newComponent.setType(componentInstanceDo.getType());
                        newComponent.setOrderValue(componentInstanceDo.getOrderValue());
                        newComponent.setDeleteFlag(componentInstanceDo.getDeleteFlag());
                        newComponent.setUseRule(componentInstanceDo.getUseRule());
                        newComponent.setMetaConfig(test2);
                        mshopComponentInstanceDao.insert(newComponent);
                        //查询详情
                        List<MshopComponentInstanceDetailDo> detailDoList = Lists.newArrayList();
                        detailDoList.add(buildMshopComponentInstanceDetailDo(componentCode,pageCode,componentInstanceDo.getVersion(),1,testDetail1));
                        detailDoList.add(buildMshopComponentInstanceDetailDo(componentCode,pageCode,componentInstanceDo.getVersion(),2,testDetail2));
                        detailDoList.add(buildMshopComponentInstanceDetailDo(componentCode,pageCode,componentInstanceDo.getVersion(),3,testDetail3));
                        detailDoList.add(buildMshopComponentInstanceDetailDo(componentCode,pageCode,componentInstanceDo.getVersion(),4,testDetail4));

                        mshopComponentInstanceDetailDao.insertBatch(detailDoList);
                        //刷新缓存
                        syncPage(pageCode,null);
                        break;
                    }
                }
                //创建组件详情
                //刷新缓存

            }catch (Exception ex){
                log.error("updatePageByTemplate error pageCode:{}",pageCode,ex);
            }
        }
        return Result.success("");
    }

    private MshopComponentInstanceDetailDo buildMshopComponentInstanceDetailDo(String componentCode
            ,String pageCode,String version,Integer orderValue,String configDetail){
        MshopComponentInstanceDetailDo detailDo =  new MshopComponentInstanceDetailDo();
        detailDo.setConfigDetailCode(sequenceGeneratorService.getSequence(componentCode));
        detailDo.setComponentCode(componentCode);
        detailDo.setComponentType("IMAGE");
        detailDo.setPageCode(pageCode);
        detailDo.setVersion(version);
        detailDo.setOrderValue(orderValue);
        detailDo.setConfigDetail(configDetail);
        detailDo.setComponentDetailType("HOT");
        detailDo.setTimeConfig("{\"timeList\": [], \"timeCycle\": {}, \"effectiveType\": 1}");
        return detailDo;
    }

    @PostMapping(value = "/backdoor/updateResourceNavigation")
    public Result<Void> updateResourceNavigation(@RequestBody List<String> tenantIdList) {
        for(String tenantId:tenantIdList){
            //查询租户的装修页面
            PageBO pageBO = pageQueryService.detailByPageType(tenantId, PageType.SHOP_PAGE.getType());
            if(Objects.isNull(pageBO)){
                continue;
            }
            NavigationResourceBO navigationComponentBO = new NavigationResourceBO();
            navigationComponentBO.setTabConfigDetails(buildResourceConfig(pageBO.getName(),pageBO.getPageCode()));
            navigationComponentBO.setTenantId(tenantId);
            navigationComponentBO.setOwnerId(tenantId);
            navigationComponentBO.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
            navigationComponentBO.setCreateBy(tenantId);
            navigationComponentBO.setResourceType(ResourceTypeEnum.NAVIGATION.getCode());
            navigationComponentBO.setChannelList(Lists.newArrayList(ResourceChannelEnum.SAA_S_APP.getCode(),ResourceChannelEnum.SAA_S_APPLETS.getCode()));
            TimeConfigBO timeConfigBO = new TimeConfigBO();
            timeConfigBO.setEffectiveType(1);
            navigationComponentBO.setTimeConfig(timeConfigBO);
            resourceService.saveNavigationResource(navigationComponentBO);
        }
        return Result.success();
    }

    private List<NavigationResourceBO.ResourceConfig> buildResourceConfig(String pageName,String pageCode){
        List<NavigationResourceBO.ResourceConfig> list = Lists.newArrayList();
        NavigationResourceBO.ResourceConfig config = new NavigationResourceBO.ResourceConfig();
        config.setStatus(ResourceStatusEnum.PUBLISH.getCode());
        config.setTargetId(pageCode);
        config.setTargetType(TargetType.PAGE.getType());
        config.setTabName(pageName);
        list.add(config);
        return list;
    }

}
