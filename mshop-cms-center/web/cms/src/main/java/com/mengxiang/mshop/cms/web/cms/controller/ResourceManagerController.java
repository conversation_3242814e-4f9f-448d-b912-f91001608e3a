package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.resource.*;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.core.service.business.ResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 页面
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "后台查询")
public class ResourceManagerController {

    @Autowired
    private ResourceService resourceService;
    
    @ApiOperation(value = "资源位列表查询")
    @PostMapping(value = "/api/resource/selectPage")
    public Result<Pagination<BaseResourceBO>> selectPage(@RequestBody ResourceRequest req) {
        Pagination<BaseResourceBO> result = resourceService.selectPage(req);
        return Result.success(result);
    }

    @ApiOperation(value = "保存金刚位替换数据")
    @PostMapping(value = "/api/resource/saveDiamondResource")
    public Result<Void> saveDiamondResource(@RequestBody DiamondResourceBO req) {
        resourceService.saveDiamondResource(req);
        return Result.success();
    }

    @ApiOperation(value = "查询金刚位替换数据")
    @PostMapping(value = "/api/resource/findDiamondResourceList")
    public Result<List<DiamondResourceBO.DiamondResourceConfig>> findDiamondResourceList(@RequestBody DiamondResourceBO req) {
        return Result.success(resourceService.findDiamondResourceList(req));
    }

    @ApiOperation(value = "保存banner组件")
    @PostMapping(value = "/api/resource/saveBannerResource")
    public Result<Void> saveBannerResource(@RequestBody BannerResourceBO req) {
        resourceService.saveBannerResource(req);
        return Result.success();
    }

    @ApiOperation(value = "查询banner组件")
    @PostMapping(value = "/api/resource/getBannerResourceById")
    public Result<BannerResourceBO> getBannerResourceById(@RequestBody BannerResourceBO req) {
        return Result.success(resourceService.getBannerResourceById(req));
    }

    @ApiOperation(value = "保存导航组件")
    @PostMapping(value = "/api/resource/saveNavigationResource")
    public Result<Void> saveNavigationResource(@RequestBody NavigationResourceBO req) {
        resourceService.saveNavigationResource(req);
        return Result.success();
    }

    @ApiOperation(value = "保存平台页面排序值")
    @PostMapping(value = "/api/resource/savePlatPageData")
    public Result<Void> savePlatPageData(@RequestBody NavigationResourceBO req) {
        resourceService.savePlatPageData(req);
        return Result.success();
    }

    @ApiOperation(value = "查询导航组件")
    @PostMapping(value = "/api/resource/getNavigationResourceById")
    public Result<NavigationResourceBO> getNavigationResourceById(@RequestBody NavigationResourceBO req) {
        return Result.success(resourceService.getNavigationResourceById(req));
    }

    @ApiOperation(value = "查询平台页面排序值")
    @PostMapping(value = "/api/resource/getPlatPageDataByTenantInfo")
    public Result<NavigationResourceBO> getPlatPageDataByTenantInfo(@RequestBody NavigationResourceBO req) {
        return Result.success(resourceService.getPlatPageDataByTenantInfo(req));
    }

    @ApiOperation(value = "保存开机广告")
    @PostMapping(value = "/api/resource/saveStartupAdvertisement")
    public Result<Void> saveStartupAdvertisement(@RequestBody StartupAdvertisementBO req) {
        resourceService.saveStartupAdvertisement(req);
        return Result.success();
    }

    @ApiOperation(value = "查询开机广告")
    @PostMapping(value = "/api/resource/getStartupAdvertisementById")
    public Result<StartupAdvertisementBO> getStartupAdvertisementById(@RequestBody StartupAdvertisementBO req) {
        return Result.success(resourceService.getStartupAdvertisementById(req));
    }

    @ApiOperation(value = "删除资源位")
    @PostMapping(value = "/api/resource/deleteById")
    public Result<Void> deleteById(@RequestBody BaseResourceBO req) {
        resourceService.deleteById(req);
        return Result.success();
    }

    @ApiOperation(value = "更新状态")
    @PostMapping(value = "/api/resource/updateStatus")
    public Result<Void> updateStatus(@RequestBody BaseResourceBO req) {
        resourceService.updateStatus(req);
        return Result.success();
    }

    @ApiOperation(value = "检查生效失效状态")
    @PostMapping(value = "/api/resource/checkStatus")
    public Result<Void> checkStatus(@RequestBody BaseResourceBO req) {
        resourceService.checkStatus();
        return Result.success();
    }
}
