package com.mengxiang.mshop.cms.web.cms.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.service.business.TemplateQueryService;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 页面模版
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(tags = "页面模版")
public class TemplateController {
    
    @Autowired
    private TemplateQueryService templateQueryService;
    
    @ApiOperation(value = "获取页面模版")
    @GetMapping(value = "/api/page/template/detail")
    public Result<PageTemplateResult> detail(@RequestParam("templateCode")String templateCode) {
        PageTemplateResult pageTemplate = templateQueryService.queryByTemplateCode(templateCode);
        return Result.success(pageTemplate);
    }

    @ApiOperation(value = "页面模版列表")
    @GetMapping(value = "/api/page/template/list")
    public Result<List<PageTemplateResult>> list(@RequestParam("ownerId")String ownerId,@RequestParam("ownerType")String ownerType,@RequestParam("pageUseType")String pageUseType) {
        List<PageTemplateResult> result = templateQueryService.list(ownerId,ownerType,pageUseType);
        return Result.success(result);
    }

}
