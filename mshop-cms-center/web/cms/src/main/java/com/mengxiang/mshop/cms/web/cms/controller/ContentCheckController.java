package com.mengxiang.mshop.cms.web.cms.controller;

import com.aikucun.security.ugc.api.dto.response.VideoScanApiResult2Resp;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.request.content.CheckPageConten;
import com.mengxiang.mshop.cms.core.model.request.content.PageContentCheck;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.model.result.content.ContentQueryResponse;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.request.content.VideoCheckQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(CmsProdConstant.INNER_API_ROOT_URL)
@Api(value = "内容检测", tags = {"内容检测"})
public class ContentCheckController {

    @Autowired
    private ContentCheckService contentCheckService;
    @Autowired
    private PageOperateService pageOperateService;

    @ApiOperation("内容检查接口")
    @PostMapping("/api/content/contextCheck")
    public Result<List<ContentCheckResponse>> contextTextCheck(@RequestBody ContentCheckRequest req) {
        return Result.success(contentCheckService.contextCheck(req));
    }
    
    @ApiOperation("视频审核查询接口")
    @PostMapping("/api/content/videoCheckQuery")
    public Result<List<ContentQueryResponse>> videoCheckQuery(@RequestBody VideoCheckQuery req) {
        return Result.success(contentCheckService.videoContextQuery(req.getDataIds()));
    }
    
    @ApiOperation("视频审核回调接口")
    @PostMapping("/api/content/pageContentCheck")
    public  Result<List<ContentCheckResponse> > pageContentCheck(@RequestBody PageContentCheck msg) {
        return Result.success(contentCheckService.pageContentCheck(msg.getPageCode(),msg.getVersion(),msg.getComponentUrls()));
    }
    
    
    @ApiOperation("视频审核回调接口")
    @PostMapping("/api/content/checkPageContent")
    public  Result<Boolean> checkPageContent(@RequestBody CheckPageConten msg) {
        return Result.success(null);
    }
    
    
    @ApiOperation("视频审核回调接口")
    @PostMapping("/api/content/callBack")
    public  com.aikucun.common2.base.Result<Boolean> callBack(@RequestBody String msg) {
        try{
            com.aikucun.common2.base.Result<Boolean> success = com.aikucun.common2.base.Result
                    .success(contentCheckService.callBack(msg));
            success.setCode(200);
            return success;
        }catch (Exception e){
            log.error("[[ContentCallBack]] 视频检测回调异常 msg:{}",msg);
        }
        return com.aikucun.common2.base.Result.error(500,"系统异常");
    }
}
