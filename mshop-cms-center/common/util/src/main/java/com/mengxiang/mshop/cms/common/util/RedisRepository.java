package com.mengxiang.mshop.cms.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class RedisRepository {

    public static final String REDIS_PREFIX = "MSHOP_CMS_CENTER:";

    public static final long DEFAULT_LOCK_TIMEOUT = 3000;

    public static final String PAGE_OPRATE = "PAGE_OPRATE_";

    public static final String PAGE_SET_INDEX = "PAGE_SETINDEX_";




}
