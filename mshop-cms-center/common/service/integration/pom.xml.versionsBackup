<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.mshop.cms</groupId>
        <artifactId>mshop-cms-center</artifactId>
        <version>1.0.5</version>
        <relativePath>../../../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mshop-cms-center-common-service-integration</artifactId>
    <packaging>jar</packaging>

    <name>mshop-cms-center-common-service-integration</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-center-common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-rpc-scan</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.commodity.product.query</groupId>
            <artifactId>com-product-query-service-facade-common</artifactId>
            <version>1.0.24</version>
            <exclusions>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.com.activity</groupId>
            <artifactId>com-activity-query-service-facade-common</artifactId>
            <version>1.2.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>common-rpc-annotation</artifactId>
                    <groupId>com.mengxiang.base</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aikucun.dc</groupId>
            <artifactId>aikucun-dc-aiward-facade-stub</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.aikucun.dc</groupId>
            <artifactId>aikucun-dc-profile-facade-stub</artifactId>
            <version>1.1.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.aikucun.common2</groupId>
                    <artifactId>common2-log</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- CMS-->
        <dependency>
            <groupId>com.akucun.cms</groupId>
            <artifactId>cms-aggregation-stub</artifactId>
            <version>1.6.6</version>
        </dependency>
        
        <dependency>
            <groupId>com.aikucun.security.ugc</groupId>
            <artifactId>security-ugc-api-stub</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.workbench</groupId>
            <artifactId>workbench-manager-service-facade-common</artifactId>
            <version>1.0.12</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
