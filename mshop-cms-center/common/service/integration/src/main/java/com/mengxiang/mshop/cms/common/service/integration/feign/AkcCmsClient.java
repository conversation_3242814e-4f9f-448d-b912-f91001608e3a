package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.akucun.cms.aggregation.stub.feign.BlocDayConfigClient;
import com.akucun.cms.aggregation.stub.feign.CmsAggregationClient;
import com.akucun.cms.aggregation.stub.feign.ConferenceConfigClient;
import com.akucun.cms.aggregation.stub.feign.ResourceModuleClient;
import com.akucun.cms.aggregation.stub.feign.req.ConferenceConfigReq;
import com.akucun.cms.aggregation.stub.feign.req.ResourceModuleReq;
import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.blocDay.BlocDayChannelRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.akucun.cms.model.vo.resourceModule.ResourceModuleBaseInfoVO;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/5/16
 * @Description: 老会场
 */
@Service
@Slf4j
public class AkcCmsClient {

    @Autowired
    private CmsAggregationClient cmsAggregationClient;

    @Autowired
    private ConferenceConfigClient conferenceConfigClient;

    @Resource
    private BlocDayConfigClient blocDayConfigClient;

    @Resource
    private ResourceModuleClient resourceModuleClient;

    public HotSaleConfigVO queryHotSalecInfo(Integer hotSaleId) {
        Result<HotSaleConfigVO> result = cmsAggregationClient.queryHotSalecInfo(hotSaleId);
        if (Objects.isNull(result)) {
            log.warn("cmsAggregationClient queryHotSalecInfo result is null, hotSaleId:{}", hotSaleId);
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("cmsAggregationClient queryHotSalecInfo result is fail, hotSaleId:{},result:{}", hotSaleId, JSONObject.toJSONString(result));
            return null;
        }
        if (Objects.isNull(result.getData())) {
            log.warn("cmsAggregationClient queryHotSalecInfo data is null, hotSaleId:{},result:{}", hotSaleId, JSONObject.toJSONString(result));
            return null;
        }
        return result.getData();
    }


    public ConferenceConfigRes conferenceBaseInfoAgg(Integer conferenceId) {
        ConferenceConfigReq request = new ConferenceConfigReq();
        request.setType(2);
        request.setConferenceIdList(Arrays.asList(conferenceId));
        Result<List<ConferenceConfigRes>> result = conferenceConfigClient.conferenceBaseInfoAgg(request);
        if (Objects.isNull(result)) {
            log.warn("conferenceConfigClient conferenceBaseInfoAgg result is null, conferenceId:{}", conferenceId);
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("conferenceConfigClient conferenceBaseInfoAgg result is fail, conferenceId:{},result:{}", conferenceId, JSONObject.toJSONString(result));
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("conferenceConfigClient conferenceBaseInfoAgg data is null, conferenceId:{},result:{}", conferenceId, JSONObject.toJSONString(result));
            return null;
        }
        return result.getData().get(0);
    }

    public List<BlocDayChannelRes> queryChannelOnline(Integer channelType) {
        Result<List<BlocDayChannelRes>> result = blocDayConfigClient.queryChannelOnline(channelType);
        if (Objects.isNull(result)) {
            log.warn("blocDayConfigClient queryChannelOnline result is null, conferenceId:{}", channelType);
            return Lists.newArrayList();
        }
        if (!result.isSuccess()) {
            log.warn("blocDayConfigClient queryChannelOnline result is fail, conferenceId:{},result:{}", channelType, JSONObject.toJSONString(result));
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("blocDayConfigClient queryChannelOnline data is null, conferenceId:{},result:{}", channelType, JSONObject.toJSONString(result));
            return Lists.newArrayList();
        }
        return result.getData();
    }

    public ResourceModuleBaseInfoVO queryResourceBaseInfo(ResourceModuleReq resourceModuleReq) {
        Result<ResourceModuleBaseInfoVO> result = resourceModuleClient.queryResourceBaseInfo(resourceModuleReq);
        if (Objects.isNull(result)) {
            log.warn("resourceModuleClient queryResourceBaseInfo result is null, req:{}", resourceModuleReq);
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("resourceModuleClient queryResourceBaseInfo result is fail, req:{},result:{}", JSON.toJSONString(resourceModuleReq), JSONObject.toJSONString(result));
            return null;
        }
        return result.getData();
    }

    public ResourceModuleBaseInfoVO queryResourceBaseInfoByXd(ResourceModuleReq resourceModuleReq) {
        Result<ResourceModuleBaseInfoVO> result = resourceModuleClient.queryResourceBaseInfoByXd(resourceModuleReq);
        if (Objects.isNull(result)) {
            log.warn("resourceModuleClient queryResourceBaseInfoByXd result is null, req:{}", resourceModuleReq);
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("resourceModuleClient queryResourceBaseInfoByXd result is fail, req:{},result:{}", JSON.toJSONString(resourceModuleReq), JSONObject.toJSONString(result));
            return null;
        }
        return result.getData();
    }
}
