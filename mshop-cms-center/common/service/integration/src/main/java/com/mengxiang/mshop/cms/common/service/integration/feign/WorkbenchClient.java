package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.commodity.product.query.service.facade.common.feign.ProductFacade;
import com.mengxiang.commodity.product.query.service.facade.common.feign.SkuFacade;
import com.mengxiang.commodity.product.query.service.facade.common.request.Option;
import com.mengxiang.commodity.product.query.service.facade.common.request.QuerySkuListByParamListRequest;
import com.mengxiang.commodity.product.query.service.facade.common.request.QuerySpuListByParamListRequest;
import com.mengxiang.commodity.product.query.service.facade.common.request.product.SkuParamList;
import com.mengxiang.commodity.product.query.service.facade.common.request.product.SpuParamList;
import com.mengxiang.commodity.product.query.service.facade.common.result.QuerySkuByParamListResponse;
import com.mengxiang.commodity.product.query.service.facade.common.result.QuerySpuByParamListResponse;
import com.mengxiang.commodity.product.query.service.facade.common.result.assembly.SkuAssemblyInfo;
import com.mengxiang.commodity.product.query.service.facade.common.result.assembly.SpuAssemblyInfo;
import com.mengxiang.workbench.service.facade.common.feign.msg.MsgOpenFeign;
import com.mengxiang.workbench.service.facade.common.feign.proc.ProcNoFeign;
import com.mengxiang.workbench.service.facade.common.feign.proc.TaskFeign;
import com.mengxiang.workbench.service.facade.common.request.msg.WxMsgSendReq;
import com.mengxiang.workbench.service.facade.common.request.proc.ProcNoFlowReq;
import com.mengxiang.workbench.service.facade.common.request.proc.TaskOperationBaseReq;
import com.mengxiang.workbench.service.facade.common.response.proc.TaskCompleteResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkbenchClient {

    @Autowired
    private ProcNoFeign procNoFeign;
    @Autowired
    private TaskFeign taskFeign;
    @Autowired
    private MsgOpenFeign msqOpenFeign;

    /**
     * 流程工单-创建工单
     * @param req
     * @return
     */
    public TaskCompleteResp createProcNo(ProcNoFlowReq req){
        log.info("createProcNo,req={}",JSON.toJSONString(req));
        try {
            Result<TaskCompleteResp> result = procNoFeign.createProcNo(req);

            log.info("调用工作流服务 procNoFeign.createProcNo req:{}，result:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            if (result.isSuccess() && Objects.nonNull(result.getData())) {
                return result.getData();
            } else {
                log.info("调用工作流服务 procNoFeign.createProcNo fail,req={}",JSON.toJSONString(req));
                throw new BusinessException("500","工作流创建失败");
            }
        } catch (Exception e) {
            log.error("调用工作流服务 procNoFeign.createProcNo error req:{}", JSON.toJSONString(req));
            e.printStackTrace();
            throw new BusinessException("500","调用工作流服务失败");
        }
    }

    /**
     * 完成任务
     * @param req
     */
    public TaskCompleteResp completeTask(TaskOperationBaseReq req){

        log.info("completeTask,req={}",JSON.toJSONString(req));
        try {
            Result<TaskCompleteResp> result = taskFeign.completeTask(req);

            log.info("调用工作流服务 taskFeign.completeTask req:{}，result:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            if (result.isSuccess() && Objects.nonNull(result.getData())) {
                return result.getData();
            } else {
                log.info("调用工作流服务 taskFeign.completeTask fail,req={}",JSON.toJSONString(req));
                throw new BusinessException("500","重新发起工作流创建失败");
            }
        } catch (Exception e) {
            log.error("调用工作流服务 taskFeign.completeTask error req:{}", JSON.toJSONString(req));
            e.printStackTrace();
            throw new BusinessException("500","重新发起工作流创建失败");
        }

    }


    /**
     * 企业微信通知
     * @param wxMsgSendReq
     */
    public void wxMsgSend(WxMsgSendReq wxMsgSendReq){
        log.info("wxMsgSend,req={}",JSON.toJSONString(wxMsgSendReq));
        try {
            Result<Void> result = msqOpenFeign.wxMsgSend(wxMsgSendReq);

            log.info("企业微信通知 msqOpenFeign.wxMsgSend req:{}，result:{}", JSON.toJSONString(wxMsgSendReq), JSON.toJSONString(result));
            if (result.isSuccess()) {
                log.info("wxMsgSend,req={},{}",JSON.toJSONString(wxMsgSendReq),JSON.toJSONString(result));
            } else {
                log.info("企业微信通知 msqOpenFeign.wxMsgSend fail,req={}",JSON.toJSONString(wxMsgSendReq));
                throw new BusinessException("500","企业微信通知失败");
            }
        } catch (Exception e) {
            log.error("企业微信通知 msqOpenFeign.wxMsgSend error req:{}", JSON.toJSONString(wxMsgSendReq));
            e.printStackTrace();
            throw new BusinessException("500","企业微信通知失败");
        }

    }



}
