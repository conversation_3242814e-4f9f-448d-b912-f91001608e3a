package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.aikucun.common2.base.Result;
import com.aikucun.dc.profile.facade.stub.LabelRequestDto;
import com.aikucun.dc.profile.facade.stub.LabelResponseDto;
import com.aikucun.dc.profile.facade.stub.ProfileFacade;
import com.aikucun.dc.profile.write.common.enums.ModelType;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DcProfileClient {

    @Resource
    private ProfileFacade profileFacade;

    public List<LabelResponseDto> getLabelValues(ModelType modelType, String modelId, List<LabelRequestDto> labelList) {
        Result<List<LabelResponseDto>> result = null;
        try {
            result = profileFacade.getLabelValues(modelType, modelId, labelList);
        } catch (Throwable e) {
           log.error("DcProfileClientImpl#getLabelValues modelType:{},modelId:{},labelList:{}", JSON.toJSON(modelType), modelId, JSON.toJSON(labelList), e);
        }
        if (result == null || !result.getSuccess() || CollectionUtils.isEmpty(result.getData())) {
            return null;
        } else {
            return result.getData();
        }
    }
}
