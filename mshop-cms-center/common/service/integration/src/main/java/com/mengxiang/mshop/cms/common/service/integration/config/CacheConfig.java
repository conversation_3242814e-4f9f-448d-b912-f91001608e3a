//package com.mengxiang.mshop.cms.common.service.integration.config;
//
//import org.springframework.context.annotation.Bean;
//import com.github.benmanes.caffeine.cache.Cache;
//import com.github.benmanes.caffeine.cache.Caffeine;
//import org.springframework.context.annotation.Configuration;
//import java.util.concurrent.TimeUnit;
//
//@Configuration
//public class CacheConfig {
//    @Bean
//    public Cache caffeineCache() {
//        return Caffeine.newBuilder()
//                // 设置最后一次写入或访问后经过固定时间过期
//                .expireAfterWrite(600, TimeUnit.SECONDS)
//                // 初始的缓存空间大小
//                .initialCapacity(100)
//                // 缓存的最大条数
//                .maximumSize(1000)
//                .build();
//    }
//
//    public static void main(String[] args) {
//        Cache<String, String> test = Caffeine.newBuilder()
//                // 设置最后一次写入或访问后经过固定时间过期
//                .expireAfterWrite(600, TimeUnit.SECONDS)
//                // 初始的缓存空间大小
//                .initialCapacity(5)
//                // 缓存的最大条数
//                .maximumSize(10)
//                .build();
////        for(){
////
////        }
//    }
//}
