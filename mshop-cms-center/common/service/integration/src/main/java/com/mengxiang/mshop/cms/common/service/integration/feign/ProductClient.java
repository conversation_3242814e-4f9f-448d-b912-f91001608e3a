package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.commodity.product.query.service.facade.common.feign.ProductFacade;
import com.mengxiang.commodity.product.query.service.facade.common.feign.SkuFacade;
import com.mengxiang.commodity.product.query.service.facade.common.request.Option;
import com.mengxiang.commodity.product.query.service.facade.common.request.QuerySkuListByParamListRequest;
import com.mengxiang.commodity.product.query.service.facade.common.request.QuerySpuListByParamListRequest;
import com.mengxiang.commodity.product.query.service.facade.common.request.product.SkuParamList;
import com.mengxiang.commodity.product.query.service.facade.common.request.product.SpuParamList;
import com.mengxiang.commodity.product.query.service.facade.common.result.QuerySkuByParamListResponse;
import com.mengxiang.commodity.product.query.service.facade.common.result.QuerySpuByParamListResponse;
import com.mengxiang.commodity.product.query.service.facade.common.result.assembly.SkuAssemblyInfo;
import com.mengxiang.commodity.product.query.service.facade.common.result.assembly.SpuAssemblyInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductClient {

    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private SkuFacade skuFacade;

    /**
     * 应用ID
     */
    private static final String APP_ID = "mshop-cms-center";

    public SpuAssemblyInfo querySpuInfo( String activitySpuId, Option option) {
        if (StringUtils.isBlank(activitySpuId)) {
            return null;
        }
        SpuParamList spuParam = new SpuParamList();
        spuParam.setActivitySpuCode(activitySpuId);
        QuerySpuByParamListResponse response = querySpuListByParamList(Sets.newHashSet(spuParam), option);
        if (response != null && CollectionUtils.isNotEmpty(response.getSpuList())) {
            return response.getSpuList().get(0);
        }
        return null;
    }

    public QuerySpuByParamListResponse querySpuListByParamList(Set<SpuParamList> spuParamLists, Option option) {
        if (CollectionUtils.isEmpty(spuParamLists)) {
            return null;
        }
        QuerySpuListByParamListRequest request = new QuerySpuListByParamListRequest();
        request.setSource(APP_ID);
        if (option == null) {
            option = new Option();
            option.isAssembleBase();
            option.isAssembleExtend();
            option.isAssembleDetailUrls();
            option.isAssembleSkus();
            option.isAnnouncement();
            option.setAssembleAggProperty();
            option.setAssemblePropertyImages();
        }
        request.setOption(option.getOption());
        request.setParamList(spuParamLists);
        try {
            Result<QuerySpuByParamListResponse> result = productFacade.querySpuByParamList(request);
            if (result != null && result.isSuccess() && result.getData() != null) {
                return result.getData();
            } else {
                log.warn("productFacade.querySpuListByParamList failed, request:{}, result:{}", JSON.toJSONString(request), JSONObject.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("productFacade.querySpuListByParamList exception, request:{}", JSONObject.toJSONString(request), e);
        }
        return null;
    }


    public SkuAssemblyInfo querySkuInfo(String activityId, String skuId, Option option) {
        if (org.apache.commons.lang.StringUtils.isBlank(activityId) || org.apache.commons.lang.StringUtils.isBlank(skuId)) {
            return null;
        }
        SkuParamList spuParam = new SkuParamList();
        spuParam.setActivityId(activityId);
        spuParam.setSkuId(skuId);
        QuerySkuByParamListResponse response = querySkuListByParamList(Sets.newHashSet(spuParam),option);
        if (response != null && CollectionUtils.isNotEmpty(response.getSkuInfoList())) {
            return response.getSkuInfoList().stream().filter(x -> skuId.equals(x.getSkuId())).findFirst().orElse(null);
        }
        return null;
    }

    /**
     * SKU查询接口——常销项目新版商品查询
     *
     * @return
     */
    public QuerySkuByParamListResponse querySkuListByParamList(Set<SkuParamList> skuParamLists, Option option) {
        QuerySkuListByParamListRequest request = new QuerySkuListByParamListRequest();
        request.setSource(APP_ID);
        option.setAssembleBase();
        option.setAssembleExtend();
        option.setAssembleIsProperty();
        request.setOption(option.getOption());
        request.setParamList(skuParamLists);
        try {
            Result<QuerySkuByParamListResponse> result = skuFacade.querySkuByParamList(request);
            if (result == null) {
                log.warn("skuFacade.querySkuListByParamList result is null, req:{}", JSONObject.toJSONString(request));
                return null;
            }
            if (!result.isSuccess()) {
                log.warn("skuFacade.querySkuListByParamList result is fail, req:{},msg:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(result));
                return null;
            }
            if (Objects.isNull(result.getData())) {
                log.warn("skuFacade.querySkuListByParamList data is null, req:{},msg:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("skuFacade.querySkuListByParamList error, request:{}", JSON.toJSONString(request), e);
        }
        return null;
    }
}
