package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.com.activity.service.facade.common.feign.ActivityQueryFacade;
import com.mengxiang.com.activity.service.facade.common.request.ValueWapper;
import com.mengxiang.com.activity.service.facade.common.result.ActivityConfigDTO;
import com.mengxiang.com.activity.service.facade.common.result.ActivityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivityClient {

    @Autowired
    private ActivityQueryFacade activityQueryFacade;


    public ActivityDTO info(String activityId) {
        Result<ActivityDTO> info = activityQueryFacade.info(ValueWapper.of(Long.valueOf(activityId)));
        if (info == null) {
            log.warn("ActivityQueryFacade info result is null, req:{}", activityId);
            return null;
        }
        if (!info.isSuccess()) {
            log.warn("ActivityQueryFacade info result is fail, req:{},msg:{}", activityId, JSONObject.toJSONString(info));
            return null;
        }
        ActivityDTO activityDTO = info.getData();
        if (Objects.isNull(activityDTO)) {
            log.warn("ActivityQueryFacade info data is null, req:{},msg:{}", activityId, JSONObject.toJSONString(info));
            return null;
        }
        ActivityConfigDTO activityConfigDTO = activityDTO.getConfig();
        if(Objects.isNull(activityConfigDTO)) {
            log.warn("ActivityQueryFacade info config is null , req:{},msg:{}", activityId, JSONObject.toJSONString(info));
            return null;
        }
        return activityDTO;
    }

    public List<ActivityDTO> infos(List<String> liveIds) {
        List<Long> collect = liveIds.stream().map(Long::valueOf).collect(Collectors.toList());
        Result<List<ActivityDTO>> result = activityQueryFacade.infos(collect);
        if (Objects.isNull(result)) {
            log.warn("activityQueryFacade.infos result is null , liveIds:{}", liveIds);
        }
        if (!result.isSuccess()) {
            log.warn("activityQueryFacade.infos result is fail , req:{},msg:{}", liveIds, JSONObject.toJSONString(result));
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("activityQueryFacade.infos data is empty , req:{},msg:{}", liveIds, JSONObject.toJSONString(result));
            return Lists.newArrayList();
        }
        return result.getData();
    }
}
