package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.aikucun.common2.base.Result;
import com.aikucun.dc.aiward.facade.stub.rule.sell.*;
import com.aikucun.dc.aiward.facade.stub.selection.LabelsResponseDto;
import com.aikucun.dc.aiward.facade.stub.selection.SelectionFacade;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/4/18
 * @Description:
 */
@Service
@Slf4j
public class DcAiwardClient {

    @Autowired
    private SellRuleFacade sellRuleFacade;

    @Autowired
    private SelectionFacade selectionFacade;

    public Boolean batchEnable(SellRuleEnableRequestDto request) {
        Result<Boolean> result = sellRuleFacade.batchEnable(request);
        if (Objects.isNull(result)){
            log.error("SellRuleFacade.batchEnable result is null, req:{}", JSONObject.toJSONString(request));
            return false;
        }
        if (!result.getSuccess()) {
            log.error("SellRuleFacade.batchEnable result is fail, req:{},msg:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(result));
            return false;
        }
        return true;
    }

    public List<SellRuleDto> getRuleBatch(SellRuleGetBatchRequestDto request) {
        Result<List<SellRuleDto>> result = sellRuleFacade.getRuleBatch(request);
        if (Objects.isNull(result)){
            log.error("SellRuleFacade.getRuleBatch result is null, req:{}", JSONObject.toJSONString(request));
            return null;
        }
        if (!result.getSuccess()) {
            log.error("SellRuleFacade.getRuleBatch result is fail, req:{},msg:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(result));
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            return null;
        }
        return result.getData();
    }

    public LabelsResponseDto getLabelsByGroup(String labelGroup) {
        Result<LabelsResponseDto> result = selectionFacade.getLabelsByGroup(labelGroup);
        if (Objects.isNull(result)){
            log.error("selectionFacade.getLabelsByGroup result is null, req:{}", labelGroup);
            return null;
        }
        if (!result.getSuccess()) {
            log.error("selectionFacade.getLabelsByGroup result is fail, req:{},msg:{}", labelGroup, JSONObject.toJSONString(result));
            return null;
        }
        return result.getData();
    }

    public Long createRuleByMerchantInfo(SellRuleCreateDto req){
        try {
            Result<Long> query = sellRuleFacade.createRuleByMerchantInfo(req);
            if (query != null && query.getSuccess() && Objects.nonNull(query.getData())) {
                return query.getData();
            } else {
                log.error("createRuleByMerchantInfo queryBIDataString req:{},resp:{}",
                        JSON.toJSONString(req),  JSON.toJSONString(query));
            }
        } catch (Exception ex) {
            log.error("createRuleByMerchantInfo error req:{}", JSON.toJSONString(req), ex);
        }
        return null;
    }
}
