package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopOperationLogDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_OPERATION_LOG.
 * 操作记录
 * <AUTHOR>
 */
public interface MshopOperationLogDoMapper{

    /**
     * desc:插入表:MSHOP_OPERATION_LOG.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_OPERATION_LOG( ID ,ACTION ,REMARK ,BIZ_CODE ,CREATE_BY ,AFTER_DATA ,BEFORE_DATA ,BIZ_TYPE ,CREATE_TIME )VALUES( null , #{action,jdbcType=VARCHAR} , #{remark,jdbcType=VARCHAR} , #{bizCode,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{afterData,jdbcType=LONGVARCHAR} , #{beforeData,jdbcType=LONGVARCHAR} , #{bizType,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopOperationLogDo entity);
    /**
     * desc:批量插入表:MSHOP_OPERATION_LOG.<br/>
     * descSql =  INSERT INTO MSHOP_OPERATION_LOG( ID ,ACTION ,REMARK ,BIZ_CODE ,CREATE_BY ,AFTER_DATA ,BEFORE_DATA ,BIZ_TYPE ,CREATE_TIME )VALUES ( null , #{item.action,jdbcType=VARCHAR} , #{item.remark,jdbcType=VARCHAR} , #{item.bizCode,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.afterData,jdbcType=LONGVARCHAR} , #{item.beforeData,jdbcType=LONGVARCHAR} , #{item.bizType,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopOperationLogDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_OPERATION_LOG.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_OPERATION_LOG WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_OPERATION_LOG.<br/>
     * descSql =  SELECT * FROM MSHOP_OPERATION_LOG WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopOperationLogDo
     */
    MshopOperationLogDo getById(Long id);

    List<MshopOperationLogDo> queryListByBizCode(@Param("pageSize")Integer pageSize, @Param("startIndex")Integer startIndex, @Param("bizCode")String bizCode, @Param("bizType")Integer bizType, @Param("action")String action);

    int countByBizCode(@Param("bizCode")String bizCode, @Param("bizType")Integer bizType, @Param("action")String action);
}
