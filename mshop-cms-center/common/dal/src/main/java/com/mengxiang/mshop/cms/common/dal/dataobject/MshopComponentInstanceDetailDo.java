package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 组件配置详情表
 * <AUTHOR>
 */
public class MshopComponentInstanceDetailDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * version 版本号（时间戳）.
     */
    private String version;
    /**
     * pageCode 页面编号.
     */
    private String pageCode;
    /**
     * timeConfig Json 存time_list或time_cycle只会有一种情况.
     */
    private String timeConfig;
    /**
     * configDetail 组件配置详情数据.
     */
    private String configDetail;
    /**
     * componentCode 组件编号.
     */
    private String componentCode;
    /**
     * componentType 组件类型.
     */
    private String componentType;
    /**
     * directUserGroup 定向分群.
     */
    private String directUserGroup;
    /**
     * configDetailCode 配置详情编号.
     */
    private String configDetailCode;
    /**
     * componentDetailType 组件详情类型.
     */
    private String componentDetailType;
    /**
     * deleteFlag 是否删除 0 否 1是.
     */
    private Integer deleteFlag;
    /**
     * orderValue 排序编号.
     */
    private Integer orderValue;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 修改时间.
     */
    private Date updateTime;

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set version 版本号（时间戳）.
     */
    public void setVersion(String version){
        this.version = version;
    }

    /**
     * Get version 版本号（时间戳）.
     *
     * @return the string
     */
    public String getVersion(){
        return version;
    }

    /**
     * Set pageCode 页面编号.
     */
    public void setPageCode(String pageCode){
        this.pageCode = pageCode;
    }

    /**
     * Get pageCode 页面编号.
     *
     * @return the string
     */
    public String getPageCode(){
        return pageCode;
    }

    /**
     * Set timeConfig Json 存time_list或time_cycle只会有一种情况.
     */
    public void setTimeConfig(String timeConfig){
        this.timeConfig = timeConfig;
    }

    /**
     * Get timeConfig Json 存time_list或time_cycle只会有一种情况.
     *
     * @return the string
     */
    public String getTimeConfig(){
        return timeConfig;
    }

    /**
     * Set configDetail 组件配置详情数据.
     */
    public void setConfigDetail(String configDetail){
        this.configDetail = configDetail;
    }

    /**
     * Get configDetail 组件配置详情数据.
     *
     * @return the string
     */
    public String getConfigDetail(){
        return configDetail;
    }

    /**
     * Set componentCode 组件编号.
     */
    public void setComponentCode(String componentCode){
        this.componentCode = componentCode;
    }

    /**
     * Get componentCode 组件编号.
     *
     * @return the string
     */
    public String getComponentCode(){
        return componentCode;
    }

    /**
     * Set componentType 组件类型.
     */
    public void setComponentType(String componentType){
        this.componentType = componentType;
    }

    /**
     * Get componentType 组件类型.
     *
     * @return the string
     */
    public String getComponentType(){
        return componentType;
    }

    /**
     * Set directUserGroup 定向分群.
     */
    public void setDirectUserGroup(String directUserGroup){
        this.directUserGroup = directUserGroup;
    }

    /**
     * Get directUserGroup 定向分群.
     *
     * @return the string
     */
    public String getDirectUserGroup(){
        return directUserGroup;
    }

    /**
     * Set configDetailCode 配置详情编号.
     */
    public void setConfigDetailCode(String configDetailCode){
        this.configDetailCode = configDetailCode;
    }

    /**
     * Get configDetailCode 配置详情编号.
     *
     * @return the string
     */
    public String getConfigDetailCode(){
        return configDetailCode;
    }

    /**
     * Set componentDetailType 组件详情类型.
     */
    public void setComponentDetailType(String componentDetailType){
        this.componentDetailType = componentDetailType;
    }

    /**
     * Get componentDetailType 组件详情类型.
     *
     * @return the string
     */
    public String getComponentDetailType(){
        return componentDetailType;
    }

    /**
     * Set deleteFlag 是否删除 0 否 1是.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 是否删除 0 否 1是.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set orderValue 排序编号.
     */
    public void setOrderValue(Integer orderValue){
        this.orderValue = orderValue;
    }

    /**
     * Get orderValue 排序编号.
     *
     * @return the string
     */
    public Integer getOrderValue(){
        return orderValue;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }
}
