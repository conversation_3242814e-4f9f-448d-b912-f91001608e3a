package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.dataobject.ResourceQueryVO;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_RESOURCE_COMPONENT.
 * 资源位表
 * <AUTHOR>
 */
public interface MshopResourceComponentDoMapper{

    /**
     * desc:插入表:MSHOP_RESOURCE_COMPONENT.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_RESOURCE_COMPONENT( ID ,NAME ,CHANNEL ,OWNER_ID ,CREATE_BY ,TENANT_ID ,UPDATE_BY ,OWNER_TYPE ,CATEGORY_ID ,TIME_CONFIG ,CONFIG_DETAIL ,RESOURCE_TYPE ,RESOURCE_PAGE_TYPE ,STATUS ,DELETE_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{name,jdbcType=VARCHAR} , #{channel,jdbcType=VARCHAR} , #{ownerId,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{tenantId,jdbcType=VARCHAR} , #{updateBy,jdbcType=VARCHAR} , #{ownerType,jdbcType=VARCHAR} , #{categoryId,jdbcType=VARCHAR} , #{timeConfig,jdbcType=LONGVARCHAR} , #{configDetail,jdbcType=LONGVARCHAR} , #{resourceType,jdbcType=VARCHAR} , #{resourcePageType,jdbcType=VARCHAR} , #{status,jdbcType=TINYINT} , #{deleteFlag,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopResourceComponentDo entity);
    /**
     * desc:批量插入表:MSHOP_RESOURCE_COMPONENT.<br/>
     * descSql =  INSERT INTO MSHOP_RESOURCE_COMPONENT( ID ,NAME ,CHANNEL ,OWNER_ID ,CREATE_BY ,TENANT_ID ,UPDATE_BY ,OWNER_TYPE ,CATEGORY_ID ,TIME_CONFIG ,CONFIG_DETAIL ,RESOURCE_TYPE ,RESOURCE_PAGE_TYPE ,STATUS ,DELETE_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.name,jdbcType=VARCHAR} , #{item.channel,jdbcType=VARCHAR} , #{item.ownerId,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.tenantId,jdbcType=VARCHAR} , #{item.updateBy,jdbcType=VARCHAR} , #{item.ownerType,jdbcType=VARCHAR} , #{item.categoryId,jdbcType=VARCHAR} , #{item.timeConfig,jdbcType=LONGVARCHAR} , #{item.configDetail,jdbcType=LONGVARCHAR} , #{item.resourceType,jdbcType=VARCHAR} , #{item.resourcePageType,jdbcType=VARCHAR} , #{item.status,jdbcType=TINYINT} , #{item.deleteFlag,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopResourceComponentDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_RESOURCE_COMPONENT.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_RESOURCE_COMPONENT WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_RESOURCE_COMPONENT.<br/>
     * descSql =  SELECT * FROM MSHOP_RESOURCE_COMPONENT WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopResourceComponentDo
     */
    MshopResourceComponentDo getById(Long id);

    MshopResourceComponentDo queryByIdxResourceType(@Param("resourceType") String resourceType,@Param("tenantId") String tenantId,@Param("ownerType") String ownerType,@Param("ownerId") String ownerId);

    List<MshopResourceComponentDo> selectList(@Param("pageQueryVO") ResourceQueryVO pageQueryVO);

    int updateById(MshopResourceComponentDo entity);


    int updateStatusById(@Param("status") Integer status,@Param("deleteFlag") Integer deleteFlag,@Param("id") Long id,@Param("timeConfig") String timeConfig);


    List<MshopResourceComponentDo> selectListByStatus(@Param("statusList") List<Integer> statusList);

    List<MshopResourceComponentDo> getPlatPageDataByCategoryIds(@Param("categoryIds") List<String> categoryIds);

    MshopResourceComponentDo getPlatPageDataByTenantInfo(@Param("tenantId") String tenantId,
                                                         @Param("ownerId") String ownerId,
                                                         @Param("ownerType") String ownerType,
                                                         @Param("resourceType") String resourceType);

    int updateStatusByTenantId(MshopResourceComponentDo copy);

}
