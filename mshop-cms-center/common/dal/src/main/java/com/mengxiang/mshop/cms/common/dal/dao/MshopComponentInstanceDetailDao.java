package com.mengxiang.mshop.cms.common.dal.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopComponentInstanceDetailDoMapper;

/**
* The Table MSHOP_COMPONENT_INSTANCE_DETAIL.
* 组件配置详情表
* <AUTHOR>
*/
@Repository
public class MshopComponentInstanceDetailDao{

    @Autowired
    private MshopComponentInstanceDetailDoMapper mshopComponentInstanceDetailDoMapper;

    /**
     * desc:插入表:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopComponentInstanceDetailDo entity){
        return mshopComponentInstanceDetailDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopComponentInstanceDetailDo> list){
        return mshopComponentInstanceDetailDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopComponentInstanceDetailDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * @param id id
     * @return MshopComponentInstanceDetailDo
     */
    public MshopComponentInstanceDetailDo getById(Long id){
        return mshopComponentInstanceDetailDoMapper.getById(id);
    }

    /**
     * desc:查询组件详情:MSHOP_PERSON_INTERVENE.<br/>
     * @param version version
     * @param pageCode pageCode
     * @param componentCode componentCode
     * @return List<MshopComponentInstanceDetailDo>
     */
    public List<MshopComponentInstanceDetailDo> queryByComponent(String pageCode,String componentCode,String version){
        return mshopComponentInstanceDetailDoMapper.queryByComponent(pageCode, componentCode, version);
    }
    /**
     * desc:查询组件详情.<br/>
     * @param version version
     * @param pageCode pageCode
     * @return List<MshopComponentInstanceDetailDo>
     */
    public List<MshopComponentInstanceDetailDo> queryByPageCode(String pageCode,String version){
        return mshopComponentInstanceDetailDoMapper.queryByPageCode(pageCode, version);
    }

    public int updateDynamic (MshopComponentInstanceDetailDo mshopComponentInstanceDetailDo) {
        return mshopComponentInstanceDetailDoMapper.updateDynamic(mshopComponentInstanceDetailDo);
    }
}
