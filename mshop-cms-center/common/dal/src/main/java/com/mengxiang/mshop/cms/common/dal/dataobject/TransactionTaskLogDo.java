package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 一致性事务补偿表
 * <AUTHOR>
 */
public class TransactionTaskLogDo{

    /**
     * id ID.
     */
    private Long id;
    /**
     * status STATUS.
     */
    private String status;
    /**
     * taskId TASK_ID.
     */
    private String taskId;
    /**
     * taskType TASK_TYPE.
     */
    private String taskType;
    /**
     * errorCode ERROR_CODE.
     */
    private String errorCode;
    /**
     * retryStatus RETRY_STATUS.
     */
    private String retryStatus;
    /**
     * errorMessage ERROR_MESSAGE.
     */
    private String errorMessage;
    /**
     * taskClassName TASK_CLASS_NAME.
     */
    private String taskClassName;
    /**
     * reversalStatus REVERSAL_STATUS.
     */
    private String reversalStatus;
    /**
     * transactionType TRANSACTION_TYPE.
     */
    private String transactionType;
    /**
     * resultAdditionalInfo RESULT_ADDITIONAL_INFO.
     */
    private String resultAdditionalInfo;
    /**
     * requestAdditionalInfo REQUEST_ADDITIONAL_INFO.
     */
    private String requestAdditionalInfo;
    /**
     * times TIMES.
     */
    private Integer times;
    /**
     * createTime CREATE_TIME.
     */
    private Date createTime;
    /**
     * updateTime UPDATE_TIME.
     */
    private Date updateTime;
    /**
     * nextExecuteTime NEXT_EXECUTE_TIME.
     */
    private Date nextExecuteTime;

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set status STATUS.
     */
    public void setStatus(String status){
        this.status = status;
    }

    /**
     * Get status STATUS.
     *
     * @return the string
     */
    public String getStatus(){
        return status;
    }

    /**
     * Set taskId TASK_ID.
     */
    public void setTaskId(String taskId){
        this.taskId = taskId;
    }

    /**
     * Get taskId TASK_ID.
     *
     * @return the string
     */
    public String getTaskId(){
        return taskId;
    }

    /**
     * Set taskType TASK_TYPE.
     */
    public void setTaskType(String taskType){
        this.taskType = taskType;
    }

    /**
     * Get taskType TASK_TYPE.
     *
     * @return the string
     */
    public String getTaskType(){
        return taskType;
    }

    /**
     * Set errorCode ERROR_CODE.
     */
    public void setErrorCode(String errorCode){
        this.errorCode = errorCode;
    }

    /**
     * Get errorCode ERROR_CODE.
     *
     * @return the string
     */
    public String getErrorCode(){
        return errorCode;
    }

    /**
     * Set retryStatus RETRY_STATUS.
     */
    public void setRetryStatus(String retryStatus){
        this.retryStatus = retryStatus;
    }

    /**
     * Get retryStatus RETRY_STATUS.
     *
     * @return the string
     */
    public String getRetryStatus(){
        return retryStatus;
    }

    /**
     * Set errorMessage ERROR_MESSAGE.
     */
    public void setErrorMessage(String errorMessage){
        this.errorMessage = errorMessage;
    }

    /**
     * Get errorMessage ERROR_MESSAGE.
     *
     * @return the string
     */
    public String getErrorMessage(){
        return errorMessage;
    }

    /**
     * Set taskClassName TASK_CLASS_NAME.
     */
    public void setTaskClassName(String taskClassName){
        this.taskClassName = taskClassName;
    }

    /**
     * Get taskClassName TASK_CLASS_NAME.
     *
     * @return the string
     */
    public String getTaskClassName(){
        return taskClassName;
    }

    /**
     * Set reversalStatus REVERSAL_STATUS.
     */
    public void setReversalStatus(String reversalStatus){
        this.reversalStatus = reversalStatus;
    }

    /**
     * Get reversalStatus REVERSAL_STATUS.
     *
     * @return the string
     */
    public String getReversalStatus(){
        return reversalStatus;
    }

    /**
     * Set transactionType TRANSACTION_TYPE.
     */
    public void setTransactionType(String transactionType){
        this.transactionType = transactionType;
    }

    /**
     * Get transactionType TRANSACTION_TYPE.
     *
     * @return the string
     */
    public String getTransactionType(){
        return transactionType;
    }

    /**
     * Set resultAdditionalInfo RESULT_ADDITIONAL_INFO.
     */
    public void setResultAdditionalInfo(String resultAdditionalInfo){
        this.resultAdditionalInfo = resultAdditionalInfo;
    }

    /**
     * Get resultAdditionalInfo RESULT_ADDITIONAL_INFO.
     *
     * @return the string
     */
    public String getResultAdditionalInfo(){
        return resultAdditionalInfo;
    }

    /**
     * Set requestAdditionalInfo REQUEST_ADDITIONAL_INFO.
     */
    public void setRequestAdditionalInfo(String requestAdditionalInfo){
        this.requestAdditionalInfo = requestAdditionalInfo;
    }

    /**
     * Get requestAdditionalInfo REQUEST_ADDITIONAL_INFO.
     *
     * @return the string
     */
    public String getRequestAdditionalInfo(){
        return requestAdditionalInfo;
    }

    /**
     * Set times TIMES.
     */
    public void setTimes(Integer times){
        this.times = times;
    }

    /**
     * Get times TIMES.
     *
     * @return the string
     */
    public Integer getTimes(){
        return times;
    }

    /**
     * Set createTime CREATE_TIME.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime CREATE_TIME.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime UPDATE_TIME.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime UPDATE_TIME.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set nextExecuteTime NEXT_EXECUTE_TIME.
     */
    public void setNextExecuteTime(Date nextExecuteTime){
        this.nextExecuteTime = nextExecuteTime;
    }

    /**
     * Get nextExecuteTime NEXT_EXECUTE_TIME.
     *
     * @return the string
     */
    public Date getNextExecuteTime(){
        return nextExecuteTime;
    }
}
