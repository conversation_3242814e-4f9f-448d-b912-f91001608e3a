package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.TransactionTaskLogDo;
import java.util.List;
import java.util.Date;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table TRANSACTION_TASK_LOG.
 * 一致性事务补偿表
 * <AUTHOR>
 */
public interface TransactionTaskLogDoMapper{

    /**
     * desc:插入表:TRANSACTION_TASK_LOG.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO TRANSACTION_TASK_LOG( ID ,STATUS ,TASK_ID ,TASK_TYPE ,ERROR_CODE ,RETRY_STATUS ,ERROR_MESSAGE ,TASK_CLASS_NAME ,REVERSAL_STATUS ,TRANSACTION_TYPE ,RESULT_ADDITIONAL_INFO ,REQUEST_ADDITIONAL_INFO ,TIMES ,CREATE_TIME ,UPDATE_TIME ,NEXT_EXECUTE_TIME )VALUES( null , #{status,jdbcType=VARCHAR} , #{taskId,jdbcType=VARCHAR} , #{taskType,jdbcType=VARCHAR} , #{errorCode,jdbcType=VARCHAR} , #{retryStatus,jdbcType=VARCHAR} , #{errorMessage,jdbcType=VARCHAR} , #{taskClassName,jdbcType=VARCHAR} , #{reversalStatus,jdbcType=VARCHAR} , #{transactionType,jdbcType=VARCHAR} , #{resultAdditionalInfo,jdbcType=VARCHAR} , #{requestAdditionalInfo,jdbcType=VARCHAR} , #{times,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} , #{nextExecuteTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(TransactionTaskLogDo entity);
    /**
     * desc:批量插入表:TRANSACTION_TASK_LOG.<br/>
     * descSql =  INSERT INTO TRANSACTION_TASK_LOG( ID ,STATUS ,TASK_ID ,TASK_TYPE ,ERROR_CODE ,RETRY_STATUS ,ERROR_MESSAGE ,TASK_CLASS_NAME ,REVERSAL_STATUS ,TRANSACTION_TYPE ,RESULT_ADDITIONAL_INFO ,REQUEST_ADDITIONAL_INFO ,TIMES ,CREATE_TIME ,UPDATE_TIME ,NEXT_EXECUTE_TIME )VALUES ( null , #{item.status,jdbcType=VARCHAR} , #{item.taskId,jdbcType=VARCHAR} , #{item.taskType,jdbcType=VARCHAR} , #{item.errorCode,jdbcType=VARCHAR} , #{item.retryStatus,jdbcType=VARCHAR} , #{item.errorMessage,jdbcType=VARCHAR} , #{item.taskClassName,jdbcType=VARCHAR} , #{item.reversalStatus,jdbcType=VARCHAR} , #{item.transactionType,jdbcType=VARCHAR} , #{item.resultAdditionalInfo,jdbcType=VARCHAR} , #{item.requestAdditionalInfo,jdbcType=VARCHAR} , #{item.times,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} , #{item.nextExecuteTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<TransactionTaskLogDo> list);
    /**
     * desc:根据主键删除数据:TRANSACTION_TASK_LOG.<br/>
     * descSql =  <![CDATA[ DELETE FROM TRANSACTION_TASK_LOG WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:TRANSACTION_TASK_LOG.<br/>
     * descSql =  SELECT * FROM TRANSACTION_TASK_LOG WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return TransactionTaskLogDo
     */
    TransactionTaskLogDo getById(Long id);
    /**
     * desc:根据唯一约束UkTaskIdType更新表:TRANSACTION_TASK_LOG.<br/>
     * descSql =  <![CDATA[ UPDATE TRANSACTION_TASK_LOG SET STATUS = #{status,jdbcType=VARCHAR} ,ERROR_CODE = #{errorCode,jdbcType=VARCHAR} ,RETRY_STATUS = #{retryStatus,jdbcType=VARCHAR} ,ERROR_MESSAGE = #{errorMessage,jdbcType=VARCHAR} ,TASK_CLASS_NAME = #{taskClassName,jdbcType=VARCHAR} ,REVERSAL_STATUS = #{reversalStatus,jdbcType=VARCHAR} ,TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR} ,RESULT_ADDITIONAL_INFO = #{resultAdditionalInfo,jdbcType=VARCHAR} ,REQUEST_ADDITIONAL_INFO = #{requestAdditionalInfo,jdbcType=VARCHAR} ,TIMES = #{times,jdbcType=INTEGER} ,CREATE_TIME = #{createTime,jdbcType=TIMESTAMP} ,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP} ,NEXT_EXECUTE_TIME = #{nextExecuteTime,jdbcType=TIMESTAMP} WHERE TASK_ID = #{taskId,jdbcType=VARCHAR} AND TASK_TYPE = #{taskType,jdbcType=VARCHAR} ]]>
     * @param entity entity
     * @return int
     */
    int updateByUkTaskIdType(TransactionTaskLogDo entity);
    /**
     * desc:根据唯一约束UkTaskIdType删除数据:TRANSACTION_TASK_LOG.<br/>
     * descSql =  <![CDATA[ DELETE FROM TRANSACTION_TASK_LOG WHERE TASK_ID = #{taskId,jdbcType=VARCHAR} AND TASK_TYPE = #{taskType,jdbcType=VARCHAR} ]]>
     * @param taskId taskId
     * @param taskType taskType
     * @return int
     */
    int deleteByUkTaskIdType(@Param("taskId")String taskId,@Param("taskType")String taskType);
    /**
     * desc:根据唯一约束UkTaskIdType获取数据:TRANSACTION_TASK_LOG.<br/>
     * descSql =  SELECT * FROM TRANSACTION_TASK_LOG WHERE <![CDATA[ TASK_ID = #{taskId,jdbcType=VARCHAR} AND TASK_TYPE = #{taskType,jdbcType=VARCHAR} ]]>
     * @param taskId taskId
     * @param taskType taskType
     * @return TransactionTaskLogDo
     */
    TransactionTaskLogDo getByUkTaskIdType(@Param("taskId")String taskId,@Param("taskType")String taskType);
    /**
     * desc:根据普通索引IdxNextExecuteTime获取数据:TRANSACTION_TASK_LOG.<br/>
     * descSql =  SELECT * FROM TRANSACTION_TASK_LOG WHERE <![CDATA[ NEXT_EXECUTE_TIME = #{nextExecuteTime,jdbcType=TIMESTAMP} AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR} ]]>
     * @param nextExecuteTime nextExecuteTime
     * @param transactionType transactionType
     * @return List<TransactionTaskLogDo>
     */
    List<TransactionTaskLogDo> queryByIdxNextExecuteTime(@Param("nextExecuteTime")Date nextExecuteTime,@Param("transactionType")String transactionType);
    /**
     * desc:根据普通索引IdxUpdateTime获取数据:TRANSACTION_TASK_LOG.<br/>
     * descSql =  SELECT * FROM TRANSACTION_TASK_LOG WHERE <![CDATA[ UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP} AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR} ]]>
     * @param updateTime updateTime
     * @param transactionType transactionType
     * @return List<TransactionTaskLogDo>
     */
    List<TransactionTaskLogDo> queryByIdxUpdateTime(@Param("updateTime")Date updateTime,@Param("transactionType")String transactionType);
}
