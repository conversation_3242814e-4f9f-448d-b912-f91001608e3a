package com.mengxiang.mshop.cms.common.dal.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopComponentInstanceDoMapper;

/**
* The Table MSHOP_COMPONENT_INSTANCE.
* 页面组件表
* <AUTHOR>
*/
@Repository
public class MshopComponentInstanceDao{

    @Autowired
    private MshopComponentInstanceDoMapper mshopComponentInstanceDoMapper;

    /**
     * desc:插入表:MSHOP_COMPONENT_INSTANCE.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopComponentInstanceDo entity){
        return mshopComponentInstanceDoMapper.insert(entity);
    }

    public int updateDynamic (MshopComponentInstanceDo mshopComponentInstanceDo) {
        return mshopComponentInstanceDoMapper.updateDynamic(mshopComponentInstanceDo);
    }
    /**
     * desc:批量插入表:MSHOP_COMPONENT_INSTANCE.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopComponentInstanceDo> list){
        return mshopComponentInstanceDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_COMPONENT_INSTANCE.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopComponentInstanceDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_COMPONENT_INSTANCE.<br/>
     * @param id id
     * @return MshopComponentInstanceDo
     */
    public MshopComponentInstanceDo getById(Long id){
        return mshopComponentInstanceDoMapper.getById(id);
    }

    /**
     * desc:查询组件.<br/>
     * @param version version
     * @param pageCode pageCode
     * @return List<MshopComponentInstanceDo>
     */
    public List<MshopComponentInstanceDo> queryByPageCode(String pageCode,String version){
        return mshopComponentInstanceDoMapper.queryByPageCode(pageCode, version);
    }

    public MshopComponentInstanceDo queryByComponentCode (String pageCode,String componentCode,String version) {
        return mshopComponentInstanceDoMapper.queryByComponentCode(pageCode,componentCode,version);
    }
    public int updateMetaConfig (MshopComponentInstanceDo mshopComponentInstanceDo) {
        return mshopComponentInstanceDoMapper.updateMetaConfig(mshopComponentInstanceDo);
    }
}
