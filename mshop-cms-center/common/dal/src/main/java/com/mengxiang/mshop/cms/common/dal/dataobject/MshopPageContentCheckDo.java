package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 页面视频检测结果
 * <AUTHOR>
 */
public class MshopPageContentCheckDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * dataId 检测id.
     */
    private String dataId;
    /**
     * content 检测内容.
     */
    private String content;
    /**
     * version 版本号.
     */
    private String version;
    /**
     * pageCode 页面编号.
     */
    private String pageCode;
    /**
     * checkResult 检测结果.
     */
    private String checkResult;
    /**
     * checkStatus UN_KNOWN:未知原因，PASS:通过,REVIEW:审核 BLOCK:阻塞 PROCESSING:处理中.
     */
    private String checkStatus;
    /**
     * componentCode 组件编号.
     */
    private String componentCode;
    /**
     * componentConfigCode 组件配置code.
     */
    private String componentConfigCode;
    /**
     * deleteFlag 状态 0:正常 1:删除.
     */
    private Integer deleteFlag;
    /**
     * contentType 检测类型 0:文本 1:图片 2:视频.
     */
    private Integer contentType;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 最后修改时间.
     */
    private Date updateTime;

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set dataId 检测id.
     */
    public void setDataId(String dataId){
        this.dataId = dataId;
    }

    /**
     * Get dataId 检测id.
     *
     * @return the string
     */
    public String getDataId(){
        return dataId;
    }

    /**
     * Set content 检测内容.
     */
    public void setContent(String content){
        this.content = content;
    }

    /**
     * Get content 检测内容.
     *
     * @return the string
     */
    public String getContent(){
        return content;
    }

    /**
     * Set version 版本号.
     */
    public void setVersion(String version){
        this.version = version;
    }

    /**
     * Get version 版本号.
     *
     * @return the string
     */
    public String getVersion(){
        return version;
    }

    /**
     * Set pageCode 页面编号.
     */
    public void setPageCode(String pageCode){
        this.pageCode = pageCode;
    }

    /**
     * Get pageCode 页面编号.
     *
     * @return the string
     */
    public String getPageCode(){
        return pageCode;
    }

    /**
     * Set checkResult 检测结果.
     */
    public void setCheckResult(String checkResult){
        this.checkResult = checkResult;
    }

    /**
     * Get checkResult 检测结果.
     *
     * @return the string
     */
    public String getCheckResult(){
        return checkResult;
    }

    /**
     * Set checkStatus UN_KNOWN:未知原因，PASS:通过,REVIEW:审核 BLOCK:阻塞 PROCESSING:处理中.
     */
    public void setCheckStatus(String checkStatus){
        this.checkStatus = checkStatus;
    }

    /**
     * Get checkStatus UN_KNOWN:未知原因，PASS:通过,REVIEW:审核 BLOCK:阻塞 PROCESSING:处理中.
     *
     * @return the string
     */
    public String getCheckStatus(){
        return checkStatus;
    }

    /**
     * Set componentCode 组件编号.
     */
    public void setComponentCode(String componentCode){
        this.componentCode = componentCode;
    }

    /**
     * Get componentCode 组件编号.
     *
     * @return the string
     */
    public String getComponentCode(){
        return componentCode;
    }

    /**
     * Set componentConfigCode 组件配置code.
     */
    public void setComponentConfigCode(String componentConfigCode){
        this.componentConfigCode = componentConfigCode;
    }

    /**
     * Get componentConfigCode 组件配置code.
     *
     * @return the string
     */
    public String getComponentConfigCode(){
        return componentConfigCode;
    }

    /**
     * Set deleteFlag 状态 0:正常 1:删除.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 状态 0:正常 1:删除.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set contentType 检测类型 0:文本 1:图片 2:视频.
     */
    public void setContentType(Integer contentType){
        this.contentType = contentType;
    }

    /**
     * Get contentType 检测类型 0:文本 1:图片 2:视频.
     *
     * @return the string
     */
    public Integer getContentType(){
        return contentType;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 最后修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 最后修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }
}
