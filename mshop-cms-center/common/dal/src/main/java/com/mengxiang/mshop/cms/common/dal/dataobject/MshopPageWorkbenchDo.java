package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * mshop_page_workbench
 * <AUTHOR>
@Data
public class MshopPageWorkbenchDo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 页面编号
     */
    private String pageCode;

    /**
     * 生效版本号
     */
    private String version;
    /**
     * 业务key
     */
    private String businessKey;


    /**
     * 流程编码
     */
    private String procInstCode;

    /**
     * 当前任务Key 10:待审核状态 55:整改
     */
    private String currentTaskKey;
    /**
     * 操作名 AGREE(同意)  REJECT(驳回)
     */
    private String buttonControl;
    /**
     * 流程状态
     */
    private Integer processStatus;

    /**
     * 是否删除 0 否 1是
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private String createUserId;

    private static final long serialVersionUID = 1L;

    private String flowInfo;
}