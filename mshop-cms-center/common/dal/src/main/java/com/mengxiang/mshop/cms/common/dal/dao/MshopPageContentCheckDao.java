package com.mengxiang.mshop.cms.common.dal.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopPageContentCheckDoMapper;

/**
* The Table MSHOP_PAGE_CONTENT_CHECK.
* 页面视频检测结果
* <AUTHOR>
*/
@Repository
public class MshopPageContentCheckDao{

    @Autowired
    private MshopPageContentCheckDoMapper mshopPageContentCheckDoMapper;

    /**
     * desc:插入表:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopPageContentCheckDo entity){
        return mshopPageContentCheckDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopPageContentCheckDo> list){
        return mshopPageContentCheckDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopPageContentCheckDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * @param id id
     * @return MshopPageContentCheckDo
     */
    public MshopPageContentCheckDo getById(Long id){
        return mshopPageContentCheckDoMapper.getById(id);
    }
    
    
    public List<MshopPageContentCheckDo> queryByPage(String pageCode, String version) {
        return mshopPageContentCheckDoMapper.queryByPage(pageCode,version);
    }
    
    public int updateByDataId(MshopPageContentCheckDo mshopPageContentCheckDo) {
        return mshopPageContentCheckDoMapper.updateByDataId(mshopPageContentCheckDo);
    }

    public List<MshopPageContentCheckDo> queryByComponentCodes(String pageCode, String version, List<String> componentDetailCodes ) {
        return mshopPageContentCheckDoMapper.queryByComponentCodes(pageCode,version,componentDetailCodes);
    }
    
    public List<MshopPageContentCheckDo> queryAll(String checkStatus, Long idNum, int pickCount) {
        return mshopPageContentCheckDoMapper.queryAll(checkStatus,idNum,pickCount);
    
    }

    /**
     * desc:根据普通索引IdxPageVersionComponent获取数据:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * @param version version
     * @param pageCode pageCode
     * @param componentCode componentCode
     * @return List<MshopPageContentCheckDo>
     */
    public List<MshopPageContentCheckDo> queryByIdxPageVersionComponent(String version,String pageCode,String componentCode){
        return mshopPageContentCheckDoMapper.queryByIdxPageVersionComponent(version, pageCode, componentCode);
    }
}
