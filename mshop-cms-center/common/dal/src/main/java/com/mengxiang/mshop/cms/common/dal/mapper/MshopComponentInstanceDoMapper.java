package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_COMPONENT_INSTANCE.
 * 页面组件表
 * <AUTHOR>
 */
public interface MshopComponentInstanceDoMapper{

    /**
     * desc:插入表:MSHOP_COMPONENT_INSTANCE.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_COMPONENT_INSTANCE( ID ,TYPE ,VERSION ,CREATE_BY ,PAGE_CODE ,UPDATE_BY ,META_CONFIG ,TIME_CONFIG ,COMPONENT_CODE ,STATUS ,TIME_TYPE ,DELETE_FLAG ,ORDER_VALUE ,EFFECTIVE_TYPE ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{type,jdbcType=VARCHAR} , #{version,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{pageCode,jdbcType=VARCHAR} , #{updateBy,jdbcType=VARCHAR} , #{metaConfig,jdbcType=LONGVARCHAR} , #{timeConfig,jdbcType=LONGVARCHAR} , #{componentCode,jdbcType=VARCHAR} , #{status,jdbcType=TINYINT} , #{timeType,jdbcType=INTEGER} , #{deleteFlag,jdbcType=INTEGER} , #{orderValue,jdbcType=INTEGER} , #{effectiveType,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopComponentInstanceDo entity);
    /**
     * desc:批量插入表:MSHOP_COMPONENT_INSTANCE.<br/>
     * descSql =  INSERT INTO MSHOP_COMPONENT_INSTANCE( ID ,TYPE ,VERSION ,CREATE_BY ,PAGE_CODE ,UPDATE_BY ,META_CONFIG ,TIME_CONFIG ,COMPONENT_CODE ,STATUS ,TIME_TYPE ,DELETE_FLAG ,ORDER_VALUE ,EFFECTIVE_TYPE ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.type,jdbcType=VARCHAR} , #{item.version,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.pageCode,jdbcType=VARCHAR} , #{item.updateBy,jdbcType=VARCHAR} , #{item.metaConfig,jdbcType=LONGVARCHAR} , #{item.timeConfig,jdbcType=LONGVARCHAR} , #{item.componentCode,jdbcType=VARCHAR} , #{item.status,jdbcType=TINYINT} , #{item.timeType,jdbcType=INTEGER} , #{item.deleteFlag,jdbcType=INTEGER} , #{item.orderValue,jdbcType=INTEGER} , #{item.effectiveType,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopComponentInstanceDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_COMPONENT_INSTANCE.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_COMPONENT_INSTANCE WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_COMPONENT_INSTANCE.<br/>
     * descSql =  SELECT * FROM MSHOP_COMPONENT_INSTANCE WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopComponentInstanceDo
     */
    MshopComponentInstanceDo getById(Long id);

    int updateDynamic(MshopComponentInstanceDo mshopComponentInstanceDo);

    /**
     * desc:查询组件.<br/>
     * descSql =  SELECT * FROM MSHOP_COMPONENT_INSTANCE WHERE page_code = #{pageCode,jdbcType=VARCHAR} AND version = #{version,jdbcType=VARCHAR} AND delete_flag = 0
     * @param version version
     * @param pageCode pageCode
     * @return List<MshopComponentInstanceDo>
     */
    List<MshopComponentInstanceDo> queryByPageCode(@Param("pageCode")String pageCode,@Param("version")String version);

    MshopComponentInstanceDo queryByComponentCode(@Param("pageCode")String pageCode,@Param("componentCode")String componentCode,@Param("version")String version);


    int updateMetaConfig(MshopComponentInstanceDo mshopComponentInstanceDo);

}
