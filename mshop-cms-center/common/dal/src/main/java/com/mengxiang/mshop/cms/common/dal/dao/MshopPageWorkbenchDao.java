package com.mengxiang.mshop.cms.common.dal.dao;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopPageWorkbenchDoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
* The Table MSHOP_PAGE_TEMPLATE.
* 页面模版表
* <AUTHOR>
*/
@Repository
public class MshopPageWorkbenchDao {

    @Autowired
    private MshopPageWorkbenchDoMapper mshopPageWorkbenchDoMapper;

    public int insert(MshopPageWorkbenchDo record){

        return mshopPageWorkbenchDoMapper.insertSelective(record);
    }

    public List<MshopPageWorkbenchDo> selectList(String pageCode,String version){

        return mshopPageWorkbenchDoMapper.selectList(pageCode,version);
    }
    public List<MshopPageWorkbenchDo> selectListByProcessStatus(String pageCode,String version,Integer processStatus){

        return mshopPageWorkbenchDoMapper.selectListByProcessStatus(pageCode,version,processStatus);
    }
    public List<MshopPageWorkbenchDo> selectListByBusinessKeyAndPageCode(String businessKey,String pageCode){

        return mshopPageWorkbenchDoMapper.selectListByBusinessKeyAndPageCode(businessKey,pageCode);
    }

    public int updateByPrimaryKeySelective(MshopPageWorkbenchDo record){

        return mshopPageWorkbenchDoMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByBusinessKey(String businessKey, String buttonControl, String taskKey,Integer processStatus) {

        return mshopPageWorkbenchDoMapper.updateByBusinessKey(businessKey,buttonControl,taskKey,processStatus);
    }

    public MshopPageWorkbenchDo selectOneByBusinessKey(String businessKey,String pageCode,String version) {

        return mshopPageWorkbenchDoMapper.selectOneByBusinessKey(businessKey,pageCode,version);
    }

    public int updateVersion(String businessKey, String version) {
        return mshopPageWorkbenchDoMapper.updateVersion(businessKey,version);
    }

    public List<MshopPageWorkbenchDo> selectListByBusinessKey(String businessKey) {
        return mshopPageWorkbenchDoMapper.selectListByBusinessKey(businessKey);
    }

    public List<MshopPageWorkbenchDo> selectListByPageCodeAndVersion(String pageCode, String version) {
        return mshopPageWorkbenchDoMapper.selectListByPageCodeAndVersion(pageCode,version);
    }

    public List<MshopPageWorkbenchDo> selectListByTime(Date time) {
        return mshopPageWorkbenchDoMapper.selectListByTime(time);
    }
}
