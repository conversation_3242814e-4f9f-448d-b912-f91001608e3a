package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_COMPONENT_INSTANCE_DETAIL.
 * 组件配置详情表
 * <AUTHOR>
 */
public interface MshopComponentInstanceDetailDoMapper{

    /**
     * desc:插入表:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_COMPONENT_INSTANCE_DETAIL( ID ,VERSION ,PAGE_CODE ,TIME_CONFIG ,CONFIG_DETAIL ,COMPONENT_CODE ,COMPONENT_TYPE ,DIRECT_USER_GROUP ,CONFIG_DETAIL_CODE ,COMPONENT_DETAIL_TYPE ,DELETE_FLAG ,ORDER_VALUE ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{version,jdbcType=VARCHAR} , #{pageCode,jdbcType=VARCHAR} , #{timeConfig,jdbcType=LONGVARCHAR} , #{configDetail,jdbcType=LONGVARCHAR} , #{componentCode,jdbcType=VARCHAR} , #{componentType,jdbcType=VARCHAR} , #{directUserGroup,jdbcType=LONGVARCHAR} , #{configDetailCode,jdbcType=VARCHAR} , #{componentDetailType,jdbcType=VARCHAR} , #{deleteFlag,jdbcType=INTEGER} , #{orderValue,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopComponentInstanceDetailDo entity);
    /**
     * desc:批量插入表:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * descSql =  INSERT INTO MSHOP_COMPONENT_INSTANCE_DETAIL( ID ,VERSION ,PAGE_CODE ,TIME_CONFIG ,CONFIG_DETAIL ,COMPONENT_CODE ,COMPONENT_TYPE ,DIRECT_USER_GROUP ,CONFIG_DETAIL_CODE ,COMPONENT_DETAIL_TYPE ,DELETE_FLAG ,ORDER_VALUE ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.version,jdbcType=VARCHAR} , #{item.pageCode,jdbcType=VARCHAR} , #{item.timeConfig,jdbcType=LONGVARCHAR} , #{item.configDetail,jdbcType=LONGVARCHAR} , #{item.componentCode,jdbcType=VARCHAR} , #{item.componentType,jdbcType=VARCHAR} , #{item.directUserGroup,jdbcType=LONGVARCHAR} , #{item.configDetailCode,jdbcType=VARCHAR} , #{item.componentDetailType,jdbcType=VARCHAR} , #{item.deleteFlag,jdbcType=INTEGER} , #{item.orderValue,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopComponentInstanceDetailDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_COMPONENT_INSTANCE_DETAIL WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_COMPONENT_INSTANCE_DETAIL.<br/>
     * descSql =  SELECT * FROM MSHOP_COMPONENT_INSTANCE_DETAIL WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopComponentInstanceDetailDo
     */
    MshopComponentInstanceDetailDo getById(Long id);

    /**
     * desc:查询组件详情:MSHOP_PERSON_INTERVENE.<br/>
     * descSql =  SELECT * FROM MSHOP_COMPONENT_INSTANCE_DETAIL WHERE page_code = #{pageCode,jdbcType=VARCHAR} AND component_code = #{componentCode,jdbcType=VARCHAR} AND version = #{version,jdbcType=VARCHAR} AND delete_flag = 0
     * @param version version
     * @param pageCode pageCode
     * @param componentCode componentCode
     * @return List<MshopComponentInstanceDetailDo>
     */
    List<MshopComponentInstanceDetailDo> queryByComponent(@Param("pageCode")String pageCode,@Param("componentCode")String componentCode,@Param("version")String version);
    /**
     * desc:查询组件详情.<br/>
     * descSql =  SELECT * FROM MSHOP_COMPONENT_INSTANCE_DETAIL WHERE page_code = #{pageCode,jdbcType=VARCHAR} AND version = #{version,jdbcType=VARCHAR} AND delete_flag = 0
     * @param version version
     * @param pageCode pageCode
     * @return List<MshopComponentInstanceDetailDo>
     */
    List<MshopComponentInstanceDetailDo> queryByPageCode(@Param("pageCode")String pageCode,@Param("version")String version);



    int updateDynamic(MshopComponentInstanceDetailDo mshopComponentInstanceDetail);
}
