package com.mengxiang.mshop.cms.common.dal.dataobject;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 页面分页查询对象
 */
@Data
public class PageQueryVO  {

    /**
     * 页面名称
     */
    private String name;
    /**
     * type 类型：首页、商品详情页、会场、微页面.
     */
    private String type;
    /**
     * type 类型：首页、商品详情页、会场、微页面.
     */
    private List<String> typeList;
    /**
     * channel 端：h5、app 、小程序.
     */
    private String channel;
    /**
     * 页面标题
     */
    private String title;
    /**
     * ownerId 所属者ID.
     */
    private String ownerId;
    /**
     * version 版本号.
     */
    private String version;
    /**
     * createBy 创建人.
     */
    private String createBy;
    /**
     * pageCode 页面编号.
     */
    private List<String> pageCodeList;
    /**
     * tenantId 租户id.
     */
    private String tenantId;
    /**
     * updateBy 修改人.
     */
    private String updateBy;
    /**
     * ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店）.
     */
    private String ownerType;
    /**
     * status 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.
     */
    private Integer status;

    //创建时间-开始
    private Date createTimeStart;

    //创建时间-结束
    private Date createTimeEnd;

    //修改时间-开始
    private Date updateTimeStart;

    //修改时间-结束
    private Date updateTimeEnd;


    private List<String> ownerIdList;


    private String marketType;
}
