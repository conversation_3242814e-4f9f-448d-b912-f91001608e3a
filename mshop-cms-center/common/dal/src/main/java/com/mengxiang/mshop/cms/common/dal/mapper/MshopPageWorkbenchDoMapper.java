package com.mengxiang.mshop.cms.common.dal.mapper;


import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MshopPageWorkbenchDoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MshopPageWorkbenchDo record);

    int insertSelective(MshopPageWorkbenchDo record);

    MshopPageWorkbenchDo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MshopPageWorkbenchDo record);

    int updateByPrimaryKey(MshopPageWorkbenchDo record);

    List<MshopPageWorkbenchDo> selectList(@Param("pageCode") String pageCode,
                                          @Param("version")String version);
    List<MshopPageWorkbenchDo> selectListByProcessStatus(@Param("pageCode") String pageCode,
                                                         @Param("version")String version,
                                                         @Param("processStatus")Integer processStatus);
    int updateByBusinessKey(@Param("businessKey")String businessKey,
                            @Param("buttonControl")String buttonControl,
                            @Param("currentTaskKey")String currentTaskKey,
                            @Param("processStatus")Integer processStatus
                            );

    MshopPageWorkbenchDo selectOneByBusinessKey(@Param("businessKey")String businessKey,
                                                @Param("pageCode")String pageCode,
                                                @Param("version")String version);

    int updateVersion(@Param("businessKey")String businessKey,@Param("version") String version);

    List<MshopPageWorkbenchDo> selectListByBusinessKeyAndPageCode(@Param("businessKey")String businessKey,
                                                                  @Param("pageCode")String pageCode);

    List<MshopPageWorkbenchDo> selectListByBusinessKey(@Param("businessKey")String businessKey);

    List<MshopPageWorkbenchDo> selectListByPageCodeAndVersion(@Param("pageCode")String pageCode,
                                                              @Param("version")String version);

    List<MshopPageWorkbenchDo> selectListByTime(@Param("time") Date time);


}