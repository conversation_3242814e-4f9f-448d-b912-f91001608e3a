package com.mengxiang.mshop.cms.common.dal.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageTemplateDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopPageTemplateDoMapper;

/**
* The Table MSHOP_PAGE_TEMPLATE.
* 页面模版表
* <AUTHOR>
*/
@Repository
public class MshopPageTemplateDao{

    @Autowired
    private MshopPageTemplateDoMapper mshopPageTemplateDoMapper;

    /**
     * desc:插入表:MSHOP_PAGE_TEMPLATE.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopPageTemplateDo entity){
        return mshopPageTemplateDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_PAGE_TEMPLATE.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopPageTemplateDo> list){
        return mshopPageTemplateDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_TEMPLATE.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopPageTemplateDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_TEMPLATE.<br/>
     * @param id id
     * @return MshopPageTemplateDo
     */
    public MshopPageTemplateDo getById(Long id){
        return mshopPageTemplateDoMapper.getById(id);
    }

    public MshopPageTemplateDo getByTemplateCode (String templateCode) {
        return mshopPageTemplateDoMapper.getByTemplateCode(templateCode);
    }

    public List<MshopPageTemplateDo> getByOwnerId (String ownerId,String ownerType) {
        return mshopPageTemplateDoMapper.getByOwnerId(ownerId,ownerType);
    }
}
