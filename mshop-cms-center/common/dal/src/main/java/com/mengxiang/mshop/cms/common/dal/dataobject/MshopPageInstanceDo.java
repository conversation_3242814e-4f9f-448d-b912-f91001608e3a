package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 微页面表
 * <AUTHOR>
 */
public class MshopPageInstanceDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * type 类型：首页、商品详情页、会场、微页面.
     */
    private String type;
    /**
     * channel 端：h5、app 、小程序.
     */
    private String channel;
    /**
     * ownerId 所属者ID.
     */
    private String ownerId;
    /**
     * version 生效版本号.
     */
    private String version;
    /**
     * createBy 创建人.
     */
    private String createBy;
    /**
     * pageCode 页面编号.
     */
    private String pageCode;
    /**
     * tenantId 租户id.
     */
    private String tenantId;
    /**
     * updateBy 修改人.
     */
    private String updateBy;
    /**
     * ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.
     */
    private String ownerType;
    /**
     * marketType 会场类型（type为MARKET有效，私密会场，普通会场）.
     */
    private String marketType;
    /**
     * createUserId 创建人id.
     */
    private String createUserId;
    /**
     * templateCode 模版code.
     */
    private String templateCode;
    /**
     * status 页面状态：0: 临时状态  1 : 待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布.
     */
    private Integer status;
    /**
     * deleteFlag 是否删除 0 否 1是.
     */
    private Integer deleteFlag;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 修改时间.
     */
    private Date updateTime;

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set type 类型：首页、商品详情页、会场、微页面.
     */
    public void setType(String type){
        this.type = type;
    }

    /**
     * Get type 类型：首页、商品详情页、会场、微页面.
     *
     * @return the string
     */
    public String getType(){
        return type;
    }

    /**
     * Set channel 端：h5、app 、小程序.
     */
    public void setChannel(String channel){
        this.channel = channel;
    }

    /**
     * Get channel 端：h5、app 、小程序.
     *
     * @return the string
     */
    public String getChannel(){
        return channel;
    }

    /**
     * Set ownerId 所属者ID.
     */
    public void setOwnerId(String ownerId){
        this.ownerId = ownerId;
    }

    /**
     * Get ownerId 所属者ID.
     *
     * @return the string
     */
    public String getOwnerId(){
        return ownerId;
    }

    /**
     * Set version 生效版本号.
     */
    public void setVersion(String version){
        this.version = version;
    }

    /**
     * Get version 生效版本号.
     *
     * @return the string
     */
    public String getVersion(){
        return version;
    }

    /**
     * Set createBy 创建人.
     */
    public void setCreateBy(String createBy){
        this.createBy = createBy;
    }

    /**
     * Get createBy 创建人.
     *
     * @return the string
     */
    public String getCreateBy(){
        return createBy;
    }

    /**
     * Set pageCode 页面编号.
     */
    public void setPageCode(String pageCode){
        this.pageCode = pageCode;
    }

    /**
     * Get pageCode 页面编号.
     *
     * @return the string
     */
    public String getPageCode(){
        return pageCode;
    }

    /**
     * Set tenantId 租户id.
     */
    public void setTenantId(String tenantId){
        this.tenantId = tenantId;
    }

    /**
     * Get tenantId 租户id.
     *
     * @return the string
     */
    public String getTenantId(){
        return tenantId;
    }

    /**
     * Set updateBy 修改人.
     */
    public void setUpdateBy(String updateBy){
        this.updateBy = updateBy;
    }

    /**
     * Get updateBy 修改人.
     *
     * @return the string
     */
    public String getUpdateBy(){
        return updateBy;
    }

    /**
     * Set ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.
     */
    public void setOwnerType(String ownerType){
        this.ownerType = ownerType;
    }

    /**
     * Get ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.
     *
     * @return the string
     */
    public String getOwnerType(){
        return ownerType;
    }

    /**
     * Set marketType 会场类型（type为MARKET有效，私密会场，普通会场）.
     */
    public void setMarketType(String marketType){
        this.marketType = marketType;
    }

    /**
     * Get marketType 会场类型（type为MARKET有效，私密会场，普通会场）.
     *
     * @return the string
     */
    public String getMarketType(){
        return marketType;
    }

    /**
     * Set createUserId 创建人id.
     */
    public void setCreateUserId(String createUserId){
        this.createUserId = createUserId;
    }

    /**
     * Get createUserId 创建人id.
     *
     * @return the string
     */
    public String getCreateUserId(){
        return createUserId;
    }

    /**
     * Set templateCode 模版code.
     */
    public void setTemplateCode(String templateCode){
        this.templateCode = templateCode;
    }

    /**
     * Get templateCode 模版code.
     *
     * @return the string
     */
    public String getTemplateCode(){
        return templateCode;
    }

    /**
     * Set status 页面状态：0: 临时状态  1 : 待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 页面状态：0: 临时状态  1 : 待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set deleteFlag 是否删除 0 否 1是.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 是否删除 0 否 1是.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }
}
