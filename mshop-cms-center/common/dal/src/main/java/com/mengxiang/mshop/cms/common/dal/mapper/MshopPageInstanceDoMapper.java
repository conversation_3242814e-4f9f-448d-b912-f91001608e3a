package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import java.util.List;

import com.mengxiang.mshop.cms.common.dal.dataobject.PageQueryVO;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_PAGE_INSTANCE.
 * 微页面表
 * <AUTHOR>
 */
public interface MshopPageInstanceDoMapper{

    /**
     * desc:插入表:MSHOP_PAGE_INSTANCE.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_PAGE_INSTANCE( ID ,TYPE ,CHANNEL ,OWNER_ID ,VERSION ,CREATE_BY ,PAGE_CODE ,TENANT_ID ,UPDATE_BY ,OWNER_TYPE ,MARKET_TYPE ,CREATE_USER_ID ,TEMPLATE_CODE ,STATUS ,DELETE_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{type,jdbcType=VARCHAR} , #{channel,jdbcType=VARCHAR} , #{ownerId,jdbcType=VARCHAR} , #{version,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{pageCode,jdbcType=VARCHAR} , #{tenantId,jdbcType=VARCHAR} , #{updateBy,jdbcType=VARCHAR} , #{ownerType,jdbcType=VARCHAR} , #{marketType,jdbcType=VARCHAR} , #{createUserId,jdbcType=VARCHAR} , #{templateCode,jdbcType=VARCHAR} , #{status,jdbcType=TINYINT} , #{deleteFlag,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopPageInstanceDo entity);
    /**
     * desc:批量插入表:MSHOP_PAGE_INSTANCE.<br/>
     * descSql =  INSERT INTO MSHOP_PAGE_INSTANCE( ID ,TYPE ,CHANNEL ,OWNER_ID ,VERSION ,CREATE_BY ,PAGE_CODE ,TENANT_ID ,UPDATE_BY ,OWNER_TYPE ,MARKET_TYPE ,CREATE_USER_ID ,TEMPLATE_CODE ,STATUS ,DELETE_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.type,jdbcType=VARCHAR} , #{item.channel,jdbcType=VARCHAR} , #{item.ownerId,jdbcType=VARCHAR} , #{item.version,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.pageCode,jdbcType=VARCHAR} , #{item.tenantId,jdbcType=VARCHAR} , #{item.updateBy,jdbcType=VARCHAR} , #{item.ownerType,jdbcType=VARCHAR} , #{item.marketType,jdbcType=VARCHAR} , #{item.createUserId,jdbcType=VARCHAR} , #{item.templateCode,jdbcType=VARCHAR} , #{item.status,jdbcType=TINYINT} , #{item.deleteFlag,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopPageInstanceDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_INSTANCE.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_PAGE_INSTANCE WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_INSTANCE.<br/>
     * descSql =  SELECT * FROM MSHOP_PAGE_INSTANCE WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopPageInstanceDo
     */
    MshopPageInstanceDo getById(Long id);


    MshopPageInstanceDo getByPageCode(@Param("pageCode")String pageCode, @Param("version") String version);

    int updateStatusAndVersionByPageCode(@Param("pageCode")String pageCode, @Param("status") Integer status,@Param("version") String version);

    int queryPageCount(@Param("pageQueryVO")PageQueryVO pageQueryVO,@Param("pageCodeList")List<String> pageCodeList,@Param("draftPageCodeList")List<String> draftPageCodeList);

    List<MshopPageInstanceDo> selectPageList(@Param("pageSize")Integer pageSize, @Param("startIndex")Integer startIndex, @Param("pageQueryVO")PageQueryVO pageQueryVO,@Param("pageCodeList")List<String> pageCodeList,@Param("draftPageCodeList")List<String> draftPageCodeList);

    List<MshopPageInstanceDo> selectList(@Param("pageQueryVO")PageQueryVO pageQueryVO);

    MshopPageInstanceDo querySystemPageByTemplateCode(@Param("ownerId")String ownerId, @Param("ownerType") String ownerType,@Param("templateCode") String templateCode);
    
    List<MshopPageInstanceDo> queryByStatus(@Param("status") int status);
    
    int updatePageStatus(@Param("pageCode")String pageCode, @Param("version") String version,@Param("status") Integer status);

    int updatePageType(@Param("pageCode")String pageCode,@Param("type")String type);

    int updatePageTypeByOwnerId(@Param("ownerId")String ownerId,@Param("type")String type,@Param("ownerType")String ownerType,@Param("originalType") String originalType);

    List<MshopPageInstanceDo> selectPagesByTypes(@Param("ownerId")String ownerId, @Param("ownerType") String ownerType,@Param("types")List<String> types);

    List<MshopPageInstanceDo> queryByStatusList(@Param("statusList")List<Integer> statusList);

    List<MshopPageInstanceDo> selectPageListByOwner(@Param("ownerId")String ownerId,@Param("ownerType") String ownerType);

    List<MshopPageInstanceDo> selectPageListByType(@Param("typeList")List<String> typeList);

    List<MshopPageInstanceDo> selectPageByCodes(@Param("pageCodes")List<String> pageCodes);

    List<MshopPageInstanceDo> selectListByOwnerIdList(@Param("pageQueryVO")PageQueryVO pageQueryVO);

}
