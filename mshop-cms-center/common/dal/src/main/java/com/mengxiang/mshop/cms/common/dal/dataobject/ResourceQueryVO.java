package com.mengxiang.mshop.cms.common.dal.dataobject;

import lombok.Data;

import java.util.List;

/**
 * 页面分页查询对象
 */
@Data
public class ResourceQueryVO {

    private String name;

    private Integer status;

    private String resourceType;

    private List<String> resourceTypeList;

    private List<Integer> statusList;

    private String ownerId;

    private String resourcePageType;

    private String ownerType;

    private String tenantId;

    private String resourceChannel;

    private String categoryId;

}
