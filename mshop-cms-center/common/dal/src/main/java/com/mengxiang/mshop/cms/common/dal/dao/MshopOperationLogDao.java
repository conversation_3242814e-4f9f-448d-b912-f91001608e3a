package com.mengxiang.mshop.cms.common.dal.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopOperationLogDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopOperationLogDoMapper;

/**
* The Table MSHOP_OPERATION_LOG.
* 操作记录
* <AUTHOR>
*/
@Repository
public class MshopOperationLogDao{

    @Autowired
    private MshopOperationLogDoMapper mshopOperationLogDoMapper;

    /**
     * desc:插入表:MSHOP_OPERATION_LOG.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopOperationLogDo entity){
        return mshopOperationLogDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_OPERATION_LOG.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopOperationLogDo> list){
        return mshopOperationLogDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_OPERATION_LOG.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopOperationLogDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_OPERATION_LOG.<br/>
     * @param id id
     * @return MshopOperationLogDo
     */
    public MshopOperationLogDo getById(Long id){
        return mshopOperationLogDoMapper.getById(id);
    }


    public List<MshopOperationLogDo> queryListByBizCode(Integer pageSize, Integer startIndex, String bizCode, Integer bizType,String action){
        return mshopOperationLogDoMapper.queryListByBizCode(pageSize,startIndex,bizCode,bizType,action);
    }

    public Long countByBizCode(String bizCode, Integer bizType,String action){
        return Long.valueOf(mshopOperationLogDoMapper.countByBizCode(bizCode,bizType,action));
    }
}
