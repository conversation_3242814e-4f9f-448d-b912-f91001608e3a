package com.mengxiang.mshop.cms.common.dal.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2020-09-22 16:16
 **/
@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = {"com.mengxiang.mshop.cms.common.dal.mapper","com.mengxiang.transaction.framework.mapper,com.mengxiang.transaction.framework.dao"}, sqlSessionTemplateRef = "sqlSessionTemplate")
public class DataSourceConfiguration {
}
