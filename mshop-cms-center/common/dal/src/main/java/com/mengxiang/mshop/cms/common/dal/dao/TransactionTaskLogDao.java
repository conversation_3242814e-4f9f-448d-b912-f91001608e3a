package com.mengxiang.mshop.cms.common.dal.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.TransactionTaskLogDo;
import java.util.List;
import java.util.Date;
import com.mengxiang.mshop.cms.common.dal.mapper.TransactionTaskLogDoMapper;

/**
* The Table TRANSACTION_TASK_LOG.
* 一致性事务补偿表
* <AUTHOR>
*/
@Repository
public class TransactionTaskLogDao{

    @Autowired
    private TransactionTaskLogDoMapper transactionTaskLogDoMapper;

    /**
     * desc:插入表:TRANSACTION_TASK_LOG.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(TransactionTaskLogDo entity){
        return transactionTaskLogDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:TRANSACTION_TASK_LOG.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<TransactionTaskLogDo> list){
        return transactionTaskLogDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:TRANSACTION_TASK_LOG.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return transactionTaskLogDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:TRANSACTION_TASK_LOG.<br/>
     * @param id id
     * @return TransactionTaskLogDo
     */
    public TransactionTaskLogDo getById(Long id){
        return transactionTaskLogDoMapper.getById(id);
    }
    /**
     * desc:根据唯一约束UkTaskIdType更新表:TRANSACTION_TASK_LOG.<br/>
     * @param entity entity
     * @return int
     */
    public int updateByUkTaskIdType(TransactionTaskLogDo entity){
        return transactionTaskLogDoMapper.updateByUkTaskIdType(entity);
    }
    /**
     * desc:根据唯一约束UkTaskIdType删除数据:TRANSACTION_TASK_LOG.<br/>
     * @param taskId taskId
     * @param taskType taskType
     * @return int
     */
    public int deleteByUkTaskIdType(String taskId,String taskType){
        return transactionTaskLogDoMapper.deleteByUkTaskIdType(taskId, taskType);
    }
    /**
     * desc:根据唯一约束UkTaskIdType获取数据:TRANSACTION_TASK_LOG.<br/>
     * @param taskId taskId
     * @param taskType taskType
     * @return TransactionTaskLogDo
     */
    public TransactionTaskLogDo getByUkTaskIdType(String taskId,String taskType){
        return transactionTaskLogDoMapper.getByUkTaskIdType(taskId, taskType);
    }
    /**
     * desc:根据普通索引IdxNextExecuteTime获取数据:TRANSACTION_TASK_LOG.<br/>
     * @param nextExecuteTime nextExecuteTime
     * @param transactionType transactionType
     * @return List<TransactionTaskLogDo>
     */
    public List<TransactionTaskLogDo> queryByIdxNextExecuteTime(Date nextExecuteTime,String transactionType){
        return transactionTaskLogDoMapper.queryByIdxNextExecuteTime(nextExecuteTime, transactionType);
    }
    /**
     * desc:根据普通索引IdxUpdateTime获取数据:TRANSACTION_TASK_LOG.<br/>
     * @param updateTime updateTime
     * @param transactionType transactionType
     * @return List<TransactionTaskLogDo>
     */
    public List<TransactionTaskLogDo> queryByIdxUpdateTime(Date updateTime,String transactionType){
        return transactionTaskLogDoMapper.queryByIdxUpdateTime(updateTime, transactionType);
    }
}
