package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 微页面草稿版本表
 * <AUTHOR>
 */
public class MshopPageDraftDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * name 页面名称.
     */
    private String name;
    /**
     * title 页面标题.
     */
    private String title;
    /**
     * version 版本号.
     */
    private String version;
    /**
     * createBy 创建人.
     */
    private String createBy;
    /**
     * pageCode 页面编号.
     */
    private String pageCode;
    /**
     * subTitle 页面副标题.
     */
    private String subTitle;
    /**
     * updateBy 修改人.
     */
    private String updateBy;
    /**
     * timeConfig 生效方式.
     */
    private String timeConfig;
    /**
     * shareConfig 分享配置信息.
     */
    private String shareConfig;
    /**
     * createUserId 创建人id.
     */
    private String createUserId;
    /**
     * backgroundColor 背景颜色.
     */
    private String backgroundColor;
    /**
     * backgroundImgUrl 背景图片.
     */
    private String backgroundImgUrl;
    /**
     * privateMarketConfig 私密会场配置.
     */
    private String privateMarketConfig;
    /**
     * status 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.
     */
    private Integer status;
    /**
     * bizType 业务类型 1=代发 2=批发.
     */
    private Integer bizType;
    /**
     * searchBox 搜索框 0:收起 1:展开.
     */
    private Integer searchBox;
    /**
     * deleteFlag 是否删除 0 否 1是.
     */
    private Integer deleteFlag;
    /**
     * searchFlag 搜索功能 0:关闭 1:开启.
     */
    private Integer searchFlag;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 修改时间.
     */
    private Date updateTime;

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set name 页面名称.
     */
    public void setName(String name){
        this.name = name;
    }

    /**
     * Get name 页面名称.
     *
     * @return the string
     */
    public String getName(){
        return name;
    }

    /**
     * Set title 页面标题.
     */
    public void setTitle(String title){
        this.title = title;
    }

    /**
     * Get title 页面标题.
     *
     * @return the string
     */
    public String getTitle(){
        return title;
    }

    /**
     * Set version 版本号.
     */
    public void setVersion(String version){
        this.version = version;
    }

    /**
     * Get version 版本号.
     *
     * @return the string
     */
    public String getVersion(){
        return version;
    }

    /**
     * Set createBy 创建人.
     */
    public void setCreateBy(String createBy){
        this.createBy = createBy;
    }

    /**
     * Get createBy 创建人.
     *
     * @return the string
     */
    public String getCreateBy(){
        return createBy;
    }

    /**
     * Set pageCode 页面编号.
     */
    public void setPageCode(String pageCode){
        this.pageCode = pageCode;
    }

    /**
     * Get pageCode 页面编号.
     *
     * @return the string
     */
    public String getPageCode(){
        return pageCode;
    }

    /**
     * Set subTitle 页面副标题.
     */
    public void setSubTitle(String subTitle){
        this.subTitle = subTitle;
    }

    /**
     * Get subTitle 页面副标题.
     *
     * @return the string
     */
    public String getSubTitle(){
        return subTitle;
    }

    /**
     * Set updateBy 修改人.
     */
    public void setUpdateBy(String updateBy){
        this.updateBy = updateBy;
    }

    /**
     * Get updateBy 修改人.
     *
     * @return the string
     */
    public String getUpdateBy(){
        return updateBy;
    }

    /**
     * Set timeConfig 生效方式.
     */
    public void setTimeConfig(String timeConfig){
        this.timeConfig = timeConfig;
    }

    /**
     * Get timeConfig 生效方式.
     *
     * @return the string
     */
    public String getTimeConfig(){
        return timeConfig;
    }

    /**
     * Set shareConfig 分享配置信息.
     */
    public void setShareConfig(String shareConfig){
        this.shareConfig = shareConfig;
    }

    /**
     * Get shareConfig 分享配置信息.
     *
     * @return the string
     */
    public String getShareConfig(){
        return shareConfig;
    }

    /**
     * Set createUserId 创建人id.
     */
    public void setCreateUserId(String createUserId){
        this.createUserId = createUserId;
    }

    /**
     * Get createUserId 创建人id.
     *
     * @return the string
     */
    public String getCreateUserId(){
        return createUserId;
    }

    /**
     * Set backgroundColor 背景颜色.
     */
    public void setBackgroundColor(String backgroundColor){
        this.backgroundColor = backgroundColor;
    }

    /**
     * Get backgroundColor 背景颜色.
     *
     * @return the string
     */
    public String getBackgroundColor(){
        return backgroundColor;
    }

    /**
     * Set backgroundImgUrl 背景图片.
     */
    public void setBackgroundImgUrl(String backgroundImgUrl){
        this.backgroundImgUrl = backgroundImgUrl;
    }

    /**
     * Get backgroundImgUrl 背景图片.
     *
     * @return the string
     */
    public String getBackgroundImgUrl(){
        return backgroundImgUrl;
    }

    /**
     * Set privateMarketConfig 私密会场配置.
     */
    public void setPrivateMarketConfig(String privateMarketConfig){
        this.privateMarketConfig = privateMarketConfig;
    }

    /**
     * Get privateMarketConfig 私密会场配置.
     *
     * @return the string
     */
    public String getPrivateMarketConfig(){
        return privateMarketConfig;
    }

    /**
     * Set status 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set bizType 业务类型 1=代发 2=批发.
     */
    public void setBizType(Integer bizType){
        this.bizType = bizType;
    }

    /**
     * Get bizType 业务类型 1=代发 2=批发.
     *
     * @return the string
     */
    public Integer getBizType(){
        return bizType;
    }

    /**
     * Set searchBox 搜索框 0:收起 1:展开.
     */
    public void setSearchBox(Integer searchBox){
        this.searchBox = searchBox;
    }

    /**
     * Get searchBox 搜索框 0:收起 1:展开.
     *
     * @return the string
     */
    public Integer getSearchBox(){
        return searchBox;
    }

    /**
     * Set deleteFlag 是否删除 0 否 1是.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 是否删除 0 否 1是.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set searchFlag 搜索功能 0:关闭 1:开启.
     */
    public void setSearchFlag(Integer searchFlag){
        this.searchFlag = searchFlag;
    }

    /**
     * Get searchFlag 搜索功能 0:关闭 1:开启.
     *
     * @return the string
     */
    public Integer getSearchFlag(){
        return searchFlag;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }
}
