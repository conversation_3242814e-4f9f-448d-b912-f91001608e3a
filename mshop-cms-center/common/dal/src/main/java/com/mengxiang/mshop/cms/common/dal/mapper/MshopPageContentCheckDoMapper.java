package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_PAGE_CONTENT_CHECK.
 * 页面视频检测结果
 * <AUTHOR>
 */
public interface MshopPageContentCheckDoMapper{

    /**
     * desc:插入表:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_PAGE_CONTENT_CHECK( ID ,DATA_ID ,TASK_ID ,CONTENT ,VERSION ,CREATE_BY ,PAGE_CODE ,UPDATE_BY ,CHECK_RESULT ,COMPONENT_CODE ,DELETE_FLAG ,CHECK_STATUS ,CONTENT_TYPE ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{dataId,jdbcType=VARCHAR} , #{taskId,jdbcType=VARCHAR} , #{content,jdbcType=VARCHAR} , #{version,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{pageCode,jdbcType=VARCHAR} , #{updateBy,jdbcType=VARCHAR} , #{checkResult,jdbcType=VARCHAR} , #{componentCode,jdbcType=VARCHAR} , #{deleteFlag,jdbcType=TINYINT} , #{checkStatus,jdbcType=TINYINT} , #{contentType,jdbcType=TINYINT} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopPageContentCheckDo entity);
    /**
     * desc:批量插入表:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * descSql =  INSERT INTO MSHOP_PAGE_CONTENT_CHECK( ID ,DATA_ID ,TASK_ID ,CONTENT ,VERSION ,CREATE_BY ,PAGE_CODE ,UPDATE_BY ,CHECK_RESULT ,COMPONENT_CODE ,DELETE_FLAG ,CHECK_STATUS ,CONTENT_TYPE ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.dataId,jdbcType=VARCHAR} , #{item.taskId,jdbcType=VARCHAR} , #{item.content,jdbcType=VARCHAR} , #{item.version,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.pageCode,jdbcType=VARCHAR} , #{item.updateBy,jdbcType=VARCHAR} , #{item.checkResult,jdbcType=VARCHAR} , #{item.componentCode,jdbcType=VARCHAR} , #{item.deleteFlag,jdbcType=TINYINT} , #{item.checkStatus,jdbcType=TINYINT} , #{item.contentType,jdbcType=TINYINT} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopPageContentCheckDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_PAGE_CONTENT_CHECK WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * descSql =  SELECT * FROM MSHOP_PAGE_CONTENT_CHECK WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopPageContentCheckDo
     */
    MshopPageContentCheckDo getById(Long id);

    /**
     * desc:根据普通索引IdxPageVersionComponent获取数据:MSHOP_PAGE_CONTENT_CHECK.<br/>
     * descSql =  SELECT * FROM MSHOP_PAGE_CONTENT_CHECK WHERE <![CDATA[ PAGE_CODE = #{pageCode,jdbcType=VARCHAR} AND VERSION = #{version,jdbcType=VARCHAR} AND COMPONENT_CODE = #{componentCode,jdbcType=VARCHAR} ]]>
     * @param version version
     * @param pageCode pageCode
     * @param componentCode componentCode
     * @return List<MshopPageContentCheckDo>
     */
    List<MshopPageContentCheckDo> queryByIdxPageVersionComponent(@Param("version")String version,@Param("pageCode")String pageCode,@Param("componentCode")String componentCode);
    
    /**
     * 查询页面所有检测内容
     */
    List<MshopPageContentCheckDo> queryByPage(@Param("pageCode") String pageCode, @Param("version") String version);
    
    /**
     * 查询页面所有检测内容
     */
    int updateByDataId(MshopPageContentCheckDo mshopPageContentCheckDo);

    List<MshopPageContentCheckDo> queryByComponentCodes(@Param("pageCode") String pageCode, @Param("version") String version,@Param("componentDetailCodes") List<String> componentDetailCodes);
    
    /**
     * 查询全部
     */
    List<MshopPageContentCheckDo> queryAll(@Param("checkStatus") String checkStatus, @Param("startNum") Long startNum, @Param("pickCount") Integer pickCount);
    
}
