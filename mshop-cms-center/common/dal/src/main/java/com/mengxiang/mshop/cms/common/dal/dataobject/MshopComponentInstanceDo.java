package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 页面组件表
 * <AUTHOR>
 */
public class MshopComponentInstanceDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * type 组件类型：
图片组件 ：IMAGE
banner组件：BANNER
商品组件：PRODUCT
档期组件 ：ACTIVITY
优惠券组件 ：COUPON
导航组件： NAVIGATION
定位器组件： ELEVATOR
营销活动组件-秒杀： SECKILL.
     */
    private String type;
    /**
     * useRule 组件使用规则 ALL,QUERY.
     */
    private String useRule;
    /**
     * version 版本号（和页面保持强一致）.
     */
    private String version;
    /**
     * pageCode 关联页面编号.
     */
    private String pageCode;
    /**
     * metaConfig 组件实例数据基础数据，偏前端展示.
     */
    private String metaConfig;
    /**
     * timeConfig Json 存time_list或time_cycle只会有一种情况.
     */
    private String timeConfig;
    /**
     * componentCode 组件编号.
     */
    private String componentCode;
    /**
     * directUserGroup 定向分群.
     */
    private String directUserGroup;
    /**
     * deleteFlag 是否删除 0 否 1是.
     */
    private Integer deleteFlag;
    /**
     * orderValue 排序编号.
     */
    private Integer orderValue;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 修改时间.
     */
    private Date updateTime;

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set type 组件类型：
图片组件 ：IMAGE
banner组件：BANNER
商品组件：PRODUCT
档期组件 ：ACTIVITY
优惠券组件 ：COUPON
导航组件： NAVIGATION
定位器组件： ELEVATOR
营销活动组件-秒杀： SECKILL.
     */
    public void setType(String type){
        this.type = type;
    }

    /**
     * Get type 组件类型：
图片组件 ：IMAGE
banner组件：BANNER
商品组件：PRODUCT
档期组件 ：ACTIVITY
优惠券组件 ：COUPON
导航组件： NAVIGATION
定位器组件： ELEVATOR
营销活动组件-秒杀： SECKILL.
     *
     * @return the string
     */
    public String getType(){
        return type;
    }

    /**
     * Set useRule 组件使用规则 ALL,QUERY.
     */
    public void setUseRule(String useRule){
        this.useRule = useRule;
    }

    /**
     * Get useRule 组件使用规则 ALL,QUERY.
     *
     * @return the string
     */
    public String getUseRule(){
        return useRule;
    }

    /**
     * Set version 版本号（和页面保持强一致）.
     */
    public void setVersion(String version){
        this.version = version;
    }

    /**
     * Get version 版本号（和页面保持强一致）.
     *
     * @return the string
     */
    public String getVersion(){
        return version;
    }

    /**
     * Set pageCode 关联页面编号.
     */
    public void setPageCode(String pageCode){
        this.pageCode = pageCode;
    }

    /**
     * Get pageCode 关联页面编号.
     *
     * @return the string
     */
    public String getPageCode(){
        return pageCode;
    }

    /**
     * Set metaConfig 组件实例数据基础数据，偏前端展示.
     */
    public void setMetaConfig(String metaConfig){
        this.metaConfig = metaConfig;
    }

    /**
     * Get metaConfig 组件实例数据基础数据，偏前端展示.
     *
     * @return the string
     */
    public String getMetaConfig(){
        return metaConfig;
    }

    /**
     * Set timeConfig Json 存time_list或time_cycle只会有一种情况.
     */
    public void setTimeConfig(String timeConfig){
        this.timeConfig = timeConfig;
    }

    /**
     * Get timeConfig Json 存time_list或time_cycle只会有一种情况.
     *
     * @return the string
     */
    public String getTimeConfig(){
        return timeConfig;
    }

    /**
     * Set componentCode 组件编号.
     */
    public void setComponentCode(String componentCode){
        this.componentCode = componentCode;
    }

    /**
     * Get componentCode 组件编号.
     *
     * @return the string
     */
    public String getComponentCode(){
        return componentCode;
    }

    /**
     * Set directUserGroup 定向分群.
     */
    public void setDirectUserGroup(String directUserGroup){
        this.directUserGroup = directUserGroup;
    }

    /**
     * Get directUserGroup 定向分群.
     *
     * @return the string
     */
    public String getDirectUserGroup(){
        return directUserGroup;
    }

    /**
     * Set deleteFlag 是否删除 0 否 1是.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 是否删除 0 否 1是.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set orderValue 排序编号.
     */
    public void setOrderValue(Integer orderValue){
        this.orderValue = orderValue;
    }

    /**
     * Get orderValue 排序编号.
     *
     * @return the string
     */
    public Integer getOrderValue(){
        return orderValue;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }
}
