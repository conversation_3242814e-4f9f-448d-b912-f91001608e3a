package com.mengxiang.mshop.cms.common.dal.dao;


import com.mengxiang.mshop.cms.common.dal.dataobject.ResourceQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo;

import java.util.ArrayList;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopResourceComponentDoMapper;

/**
* The Table MSHOP_RESOURCE_COMPONENT.
* 资源位表
* <AUTHOR>
*/
@Repository
public class MshopResourceComponentDao{

    @Autowired
    private MshopResourceComponentDoMapper mshopResourceComponentDoMapper;

    /**
     * desc:插入表:MSHOP_RESOURCE_COMPONENT.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopResourceComponentDo entity){
        return mshopResourceComponentDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_RESOURCE_COMPONENT.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopResourceComponentDo> list){
        return mshopResourceComponentDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_RESOURCE_COMPONENT.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopResourceComponentDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_RESOURCE_COMPONENT.<br/>
     * @param id id
     * @return MshopResourceComponentDo
     */
    public MshopResourceComponentDo getById(Long id){
        return mshopResourceComponentDoMapper.getById(id);
    }

    public MshopResourceComponentDo queryByIdxResourceType(String resourceType, String tenantId,String ownerType,String ownerId){
        return mshopResourceComponentDoMapper.queryByIdxResourceType(resourceType,tenantId,ownerType,ownerId);
    }
    /**
     * desc:根据普通索引IdxResourceType获取数据:MSHOP_RESOURCE_COMPONENT.<br/>
     * @param ownerId ownerId
     * @param tenantId tenantId
     * @param ownerType ownerType
     * @param resourceType resourceType
     * @return List<MshopResourceComponentDo>
     */
    public List<MshopResourceComponentDo> selectList(ResourceQueryVO pageQueryVO){
        return mshopResourceComponentDoMapper.selectList(pageQueryVO);
    }
    public List<MshopResourceComponentDo> selectListByStatus(List<Integer> statusList){
        return mshopResourceComponentDoMapper.selectListByStatus(statusList);
    }

    public int updateById(MshopResourceComponentDo componentDo){
        return mshopResourceComponentDoMapper.updateById(componentDo);
    }

    public int updateStatusById(Integer status, Integer deleteFlag,Long id,String timeConfig){
        return mshopResourceComponentDoMapper.updateStatusById(status,deleteFlag,id,timeConfig);
    }

    public int savePlatPageData(ArrayList<MshopResourceComponentDo> doObj) {
        return mshopResourceComponentDoMapper.insertBatch(doObj);
    }

    public List<MshopResourceComponentDo> getPlatPageDataByCategoryIds(List<String> categoryIds) {
        return mshopResourceComponentDoMapper.getPlatPageDataByCategoryIds(categoryIds);
    }


    /**
     * 根据tenant_id、owner_id、owner_type查询
     * @param tenantId
     * @param ownerId
     * @param ownerType
     * @param resourceType
     * @return
     */
    public MshopResourceComponentDo getPlatPageDataByTenantInfo(String tenantId, String ownerId, String ownerType, String resourceType) {
        return mshopResourceComponentDoMapper.getPlatPageDataByTenantInfo(tenantId, ownerId, ownerType, resourceType);
    }

    /**
     * 根据tenant_id、owner_id、owner_type更新
     * @param copy
     * @return
     */
    public int updateStatusByTenantId(MshopResourceComponentDo copy) {
        return mshopResourceComponentDoMapper.updateStatusByTenantId(copy);
    }

}
