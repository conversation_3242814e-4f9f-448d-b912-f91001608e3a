package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 页面模版表
 * <AUTHOR>
 */
public class MshopPageTemplateDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * ownerId 所属者ID.
     */
    private String ownerId;
    /**
     * createBy 创建人.
     */
    private String createBy;
    /**
     * tenantId 租户id.
     */
    private String tenantId;
    /**
     * updateBy 修改人.
     */
    private String updateBy;
    /**
     * ownerType 所属者类型（系统:system）.
     */
    private String ownerType;
    /**
     * pageUseRule 页面使用规则.
     */
    private String pageUseRule;
    /**
     * pageUseType 页面使用范围：店铺主页:HOME  分类: CATEGORY  微页面:MICRO.
     */
    private String pageUseType;
    /**
     * useChannels 使用端：h5、app 、miniApp 
可多个逗号分隔.
     */
    private String useChannels;
    /**
     * templateCode 模版编号.
     */
    private String templateCode;
    /**
     * templateDesc 模版描述.
     */
    private String templateDesc;
    /**
     * templateName 模版名称.
     */
    private String templateName;
    /**
     * componentUseRule 组件使用规则.
     */
    private String componentUseRule;
    /**
     * templateImageUrl 模版图片.
     */
    private String templateImageUrl;
    /**
     * status 状态: 0:可用.
     */
    private Integer status;
    /**
     * deleteFlag 是否删除 0 否 1是.
     */
    private Integer deleteFlag;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 修改时间.
     */
    private Date updateTime;

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set ownerId 所属者ID.
     */
    public void setOwnerId(String ownerId){
        this.ownerId = ownerId;
    }

    /**
     * Get ownerId 所属者ID.
     *
     * @return the string
     */
    public String getOwnerId(){
        return ownerId;
    }

    /**
     * Set createBy 创建人.
     */
    public void setCreateBy(String createBy){
        this.createBy = createBy;
    }

    /**
     * Get createBy 创建人.
     *
     * @return the string
     */
    public String getCreateBy(){
        return createBy;
    }

    /**
     * Set tenantId 租户id.
     */
    public void setTenantId(String tenantId){
        this.tenantId = tenantId;
    }

    /**
     * Get tenantId 租户id.
     *
     * @return the string
     */
    public String getTenantId(){
        return tenantId;
    }

    /**
     * Set updateBy 修改人.
     */
    public void setUpdateBy(String updateBy){
        this.updateBy = updateBy;
    }

    /**
     * Get updateBy 修改人.
     *
     * @return the string
     */
    public String getUpdateBy(){
        return updateBy;
    }

    /**
     * Set ownerType 所属者类型（系统:system）.
     */
    public void setOwnerType(String ownerType){
        this.ownerType = ownerType;
    }

    /**
     * Get ownerType 所属者类型（系统:system）.
     *
     * @return the string
     */
    public String getOwnerType(){
        return ownerType;
    }

    /**
     * Set pageUseRule 页面使用规则.
     */
    public void setPageUseRule(String pageUseRule){
        this.pageUseRule = pageUseRule;
    }

    /**
     * Get pageUseRule 页面使用规则.
     *
     * @return the string
     */
    public String getPageUseRule(){
        return pageUseRule;
    }

    /**
     * Set pageUseType 页面使用范围：店铺主页:HOME  分类: CATEGORY  微页面:MICRO.
     */
    public void setPageUseType(String pageUseType){
        this.pageUseType = pageUseType;
    }

    /**
     * Get pageUseType 页面使用范围：店铺主页:HOME  分类: CATEGORY  微页面:MICRO.
     *
     * @return the string
     */
    public String getPageUseType(){
        return pageUseType;
    }

    /**
     * Set useChannels 使用端：h5、app 、miniApp 
可多个逗号分隔.
     */
    public void setUseChannels(String useChannels){
        this.useChannels = useChannels;
    }

    /**
     * Get useChannels 使用端：h5、app 、miniApp 
可多个逗号分隔.
     *
     * @return the string
     */
    public String getUseChannels(){
        return useChannels;
    }

    /**
     * Set templateCode 模版编号.
     */
    public void setTemplateCode(String templateCode){
        this.templateCode = templateCode;
    }

    /**
     * Get templateCode 模版编号.
     *
     * @return the string
     */
    public String getTemplateCode(){
        return templateCode;
    }

    /**
     * Set templateDesc 模版描述.
     */
    public void setTemplateDesc(String templateDesc){
        this.templateDesc = templateDesc;
    }

    /**
     * Get templateDesc 模版描述.
     *
     * @return the string
     */
    public String getTemplateDesc(){
        return templateDesc;
    }

    /**
     * Set templateName 模版名称.
     */
    public void setTemplateName(String templateName){
        this.templateName = templateName;
    }

    /**
     * Get templateName 模版名称.
     *
     * @return the string
     */
    public String getTemplateName(){
        return templateName;
    }

    /**
     * Set componentUseRule 组件使用规则.
     */
    public void setComponentUseRule(String componentUseRule){
        this.componentUseRule = componentUseRule;
    }

    /**
     * Get componentUseRule 组件使用规则.
     *
     * @return the string
     */
    public String getComponentUseRule(){
        return componentUseRule;
    }

    /**
     * Set templateImageUrl 模版图片.
     */
    public void setTemplateImageUrl(String templateImageUrl){
        this.templateImageUrl = templateImageUrl;
    }

    /**
     * Get templateImageUrl 模版图片.
     *
     * @return the string
     */
    public String getTemplateImageUrl(){
        return templateImageUrl;
    }

    /**
     * Set status 状态: 0:可用.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 状态: 0:可用.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set deleteFlag 是否删除 0 否 1是.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 是否删除 0 否 1是.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }
}
