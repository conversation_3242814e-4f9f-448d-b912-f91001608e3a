package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 操作记录
 * <AUTHOR>
 */
public class MshopOperationLogDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * action 行为【创建、编辑、发布】.
     */
    private String action;
    /**
     * remark 备注.
     */
    private String remark;
    /**
     * bizCode 业务编号.
     */
    private String bizCode;
    /**
     * createBy 创建人.
     */
    private String createBy;
    /**
     * afterData 操作后数据.
     */
    private String afterData;
    /**
     * beforeData 操作前数据.
     */
    private String beforeData;
    /**
     * createUserId 创建人Id.
     */
    private String createUserId;
    /**
     * bizType 业务类型.
     */
    private Integer bizType;
    /**
     * createTime 创建时间.
     */
    private Date createTime;

    /**
     * 创建人类型
     */
    private String ownerType;

    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set action 行为【创建、编辑、发布】.
     */
    public void setAction(String action){
        this.action = action;
    }

    /**
     * Get action 行为【创建、编辑、发布】.
     *
     * @return the string
     */
    public String getAction(){
        return action;
    }

    /**
     * Set remark 备注.
     */
    public void setRemark(String remark){
        this.remark = remark;
    }

    /**
     * Get remark 备注.
     *
     * @return the string
     */
    public String getRemark(){
        return remark;
    }

    /**
     * Set bizCode 业务编号.
     */
    public void setBizCode(String bizCode){
        this.bizCode = bizCode;
    }

    /**
     * Get bizCode 业务编号.
     *
     * @return the string
     */
    public String getBizCode(){
        return bizCode;
    }

    /**
     * Set createBy 创建人.
     */
    public void setCreateBy(String createBy){
        this.createBy = createBy;
    }

    /**
     * Get createBy 创建人.
     *
     * @return the string
     */
    public String getCreateBy(){
        return createBy;
    }

    /**
     * Set afterData 操作后数据.
     */
    public void setAfterData(String afterData){
        this.afterData = afterData;
    }

    /**
     * Get afterData 操作后数据.
     *
     * @return the string
     */
    public String getAfterData(){
        return afterData;
    }

    /**
     * Set beforeData 操作前数据.
     */
    public void setBeforeData(String beforeData){
        this.beforeData = beforeData;
    }

    /**
     * Get beforeData 操作前数据.
     *
     * @return the string
     */
    public String getBeforeData(){
        return beforeData;
    }

    /**
     * Set createUserId 创建人Id.
     */
    public void setCreateUserId(String createUserId){
        this.createUserId = createUserId;
    }

    /**
     * Get createUserId 创建人Id.
     *
     * @return the string
     */
    public String getCreateUserId(){
        return createUserId;
    }

    /**
     * Set bizType 业务类型.
     */
    public void setBizType(Integer bizType){
        this.bizType = bizType;
    }

    /**
     * Get bizType 业务类型.
     *
     * @return the string
     */
    public Integer getBizType(){
        return bizType;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }
}
