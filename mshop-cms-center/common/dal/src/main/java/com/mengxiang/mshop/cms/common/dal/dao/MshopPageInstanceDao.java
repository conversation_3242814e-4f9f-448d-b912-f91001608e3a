package com.mengxiang.mshop.cms.common.dal.dao;

import com.mengxiang.mshop.cms.common.dal.dataobject.PageQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopPageInstanceDoMapper;

/**
* The Table MSHOP_PAGE_INSTANCE.
* 微页面表
* <AUTHOR>
*/
@Repository
public class MshopPageInstanceDao{

    @Autowired
    private MshopPageInstanceDoMapper mshopPageInstanceDoMapper;

    /**
     * desc:插入表:MSHOP_PAGE_INSTANCE.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopPageInstanceDo entity){
        return mshopPageInstanceDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_PAGE_INSTANCE.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopPageInstanceDo> list){
        return mshopPageInstanceDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_INSTANCE.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopPageInstanceDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_INSTANCE.<br/>
     * @param id id
     * @return MshopPageInstanceDo
     */
    public MshopPageInstanceDo getById(Long id){
        return mshopPageInstanceDoMapper.getById(id);
    }

    public MshopPageInstanceDo getByPageCode (String pageCode, String version) {
        return mshopPageInstanceDoMapper.getByPageCode(pageCode,version);
    }

    public int updateStatusAndVersionByPageCode(String pageCode, Integer status,String version) {
        return mshopPageInstanceDoMapper.updateStatusAndVersionByPageCode(pageCode,status,version);
    }
    
    public int updatePageStatus(String pageCode,String version, Integer status) {
        return mshopPageInstanceDoMapper.updatePageStatus(pageCode,version,status);
    }


    public int updatePageType(String pageCode,String type) {
        return mshopPageInstanceDoMapper.updatePageType(pageCode,type);
    }

    public int updatePageTypeByOwnerId(String ownerId,String type,String ownerType,String originalType) {
        return mshopPageInstanceDoMapper.updatePageTypeByOwnerId(ownerId,type,ownerType,originalType);
    }

    public Long queryPageCount(PageQueryVO pageQueryVO,List<String> draftPageCodeList) {
        return  Long.valueOf(mshopPageInstanceDoMapper.queryPageCount(pageQueryVO,pageQueryVO.getPageCodeList(),draftPageCodeList));
    }

    public List<MshopPageInstanceDo> selectPageList(Integer pageSize, Integer startIndex, PageQueryVO pageQueryVO,List<String> draftPageCodeList) {

        return mshopPageInstanceDoMapper.selectPageList(pageSize,startIndex,pageQueryVO,pageQueryVO.getPageCodeList(),draftPageCodeList);
    }

    public List<MshopPageInstanceDo> selectList(PageQueryVO pageQueryVO) {

        return mshopPageInstanceDoMapper.selectList(pageQueryVO);
    }

    public List<MshopPageInstanceDo> selectListByOwnerIdList(PageQueryVO pageQueryVO) {

        return mshopPageInstanceDoMapper.selectListByOwnerIdList(pageQueryVO);
    }

    public List<MshopPageInstanceDo> selectPageListByOwner(String ownerId,String ownerType) {

        return mshopPageInstanceDoMapper.selectPageListByOwner(ownerId,ownerType);
    }
    public List<MshopPageInstanceDo> selectPageListByType(List<String> typeList) {

        return mshopPageInstanceDoMapper.selectPageListByType(typeList);
    }

    public MshopPageInstanceDo querySystemPageByTemplateCode(String templateCode,String ownerId,String ownerType) {
        return mshopPageInstanceDoMapper.querySystemPageByTemplateCode(ownerId,ownerType,templateCode);
    }
    
    public List<MshopPageInstanceDo> queryByStatus(int status) {
        return mshopPageInstanceDoMapper.queryByStatus(status);
    }

    public List<MshopPageInstanceDo> selectPagesByTypes(String ownerId, String ownerType,List<String> types){
        return mshopPageInstanceDoMapper.selectPagesByTypes(ownerId,ownerType,types);
    }

    public List<MshopPageInstanceDo> queryByStatusList (List<Integer> statusList) {
        return mshopPageInstanceDoMapper.queryByStatusList(statusList);
    }

    public List<MshopPageInstanceDo> selectPageByCodes (List<String> pageCodes) {
        return mshopPageInstanceDoMapper.selectPageByCodes(pageCodes);
    }

}
