package com.mengxiang.mshop.cms.common.dal.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import java.util.List;
import com.mengxiang.mshop.cms.common.dal.mapper.MshopPageDraftDoMapper;

/**
* The Table MSHOP_PAGE_DRAFT.
* 微页面草稿版本表
* <AUTHOR>
*/
@Repository
public class MshopPageDraftDao{

    @Autowired
    private MshopPageDraftDoMapper mshopPageDraftDoMapper;

    /**
     * desc:插入表:MSHOP_PAGE_DRAFT.<br/>
     * @param entity entity
     * @return int
     */
    public int insert(MshopPageDraftDo entity){
        return mshopPageDraftDoMapper.insert(entity);
    }
    /**
     * desc:批量插入表:MSHOP_PAGE_DRAFT.<br/>
     * @param list list
     * @return int
     */
    public int insertBatch(List<MshopPageDraftDo> list){
        return mshopPageDraftDoMapper.insertBatch(list);
    }
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_DRAFT.<br/>
     * @param id id
     * @return int
     */
    public int deleteById(Long id){
        return mshopPageDraftDoMapper.deleteById(id);
    }
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_DRAFT.<br/>
     * @param id id
     * @return MshopPageDraftDo
     */
    public MshopPageDraftDo getById(Long id){
        return mshopPageDraftDoMapper.getById(id);
    }

    public MshopPageDraftDo getByPageCode (String pageCode,String version) {
        return mshopPageDraftDoMapper.getByPageCode(pageCode,version);
    }

    public MshopPageDraftDo getLastByPageCode(String pageCode,Integer status) {
        return mshopPageDraftDoMapper.getLastByPageCode(pageCode,status);
    }

    public List<MshopPageDraftDo> getByNameTitle(String name,String title,String createBy,List<String> instancePageCodeList) {
        return mshopPageDraftDoMapper.getByNameTitle(name, title,createBy, instancePageCodeList);
    }
    public List<MshopPageDraftDo> getByNameTitleJoin(String name,String title) {
        return mshopPageDraftDoMapper.getByNameTitleJoin(name, title);
    }

    public int updateStatusByPageCode(String pageCode,String version,Integer status) {
        return mshopPageDraftDoMapper.updateStatusByPageCode(pageCode,version,status);
    }

}
