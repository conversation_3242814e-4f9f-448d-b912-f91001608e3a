package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageTemplateDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_PAGE_TEMPLATE.
 * 页面模版表
 * <AUTHOR>
 */
public interface MshopPageTemplateDoMapper{

    /**
     * desc:插入表:MSHOP_PAGE_TEMPLATE.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_PAGE_TEMPLATE( ID ,OWNER_ID ,CREATE_BY ,TENANT_ID ,UPDATE_BY ,OWNER_TYPE ,PAGE_USE_RULE ,PAGE_USE_TYPE ,USE_CHANNELS ,TEMPLATE_CODE ,COMPONENT_USE_RULE ,STATUS ,DELETE_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{ownerId,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{tenantId,jdbcType=VARCHAR} , #{updateBy,jdbcType=VARCHAR} , #{ownerType,jdbcType=VARCHAR} , #{pageUseRule,jdbcType=LONGVARCHAR} , #{pageUseType,jdbcType=VARCHAR} , #{useChannels,jdbcType=VARCHAR} , #{templateCode,jdbcType=VARCHAR} , #{componentUseRule,jdbcType=LONGVARCHAR} , #{status,jdbcType=TINYINT} , #{deleteFlag,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopPageTemplateDo entity);
    /**
     * desc:批量插入表:MSHOP_PAGE_TEMPLATE.<br/>
     * descSql =  INSERT INTO MSHOP_PAGE_TEMPLATE( ID ,OWNER_ID ,CREATE_BY ,TENANT_ID ,UPDATE_BY ,OWNER_TYPE ,PAGE_USE_RULE ,PAGE_USE_TYPE ,USE_CHANNELS ,TEMPLATE_CODE ,COMPONENT_USE_RULE ,STATUS ,DELETE_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.ownerId,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.tenantId,jdbcType=VARCHAR} , #{item.updateBy,jdbcType=VARCHAR} , #{item.ownerType,jdbcType=VARCHAR} , #{item.pageUseRule,jdbcType=LONGVARCHAR} , #{item.pageUseType,jdbcType=VARCHAR} , #{item.useChannels,jdbcType=VARCHAR} , #{item.templateCode,jdbcType=VARCHAR} , #{item.componentUseRule,jdbcType=LONGVARCHAR} , #{item.status,jdbcType=TINYINT} , #{item.deleteFlag,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopPageTemplateDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_TEMPLATE.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_PAGE_TEMPLATE WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_TEMPLATE.<br/>
     * descSql =  SELECT * FROM MSHOP_PAGE_TEMPLATE WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopPageTemplateDo
     */
    MshopPageTemplateDo getById(Long id);

    MshopPageTemplateDo getByTemplateCode (@Param("templateCode")String templateCode);

    List<MshopPageTemplateDo> getByOwnerId (@Param("ownerId")String ownerId,@Param("ownerType")String ownerType);
}
