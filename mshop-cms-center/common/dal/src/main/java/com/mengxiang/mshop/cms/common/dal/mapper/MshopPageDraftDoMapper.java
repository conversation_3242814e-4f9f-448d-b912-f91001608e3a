package com.mengxiang.mshop.cms.common.dal.mapper;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 由于需要对分页支持,请直接使用对应的DAO类
 * The Table MSHOP_PAGE_DRAFT.
 * 微页面草稿版本表
 * <AUTHOR>
 */
public interface MshopPageDraftDoMapper{

    /**
     * desc:插入表:MSHOP_PAGE_DRAFT.<br/>
     * descSql =  SELECT LAST_INSERT_ID() <![CDATA[ INSERT INTO MSHOP_PAGE_DRAFT( ID ,NAME ,TITLE ,VERSION ,CREATE_BY ,PAGE_CODE ,SUB_TITLE ,UPDATE_BY ,TIME_CONFIG ,SHARE_CONFIG ,CREATE_USER_ID ,BACKGROUND_COLOR ,BACKGROUND_IMG_URL ,PRIVATE_MARKET_CONFIG ,STATUS ,BIZ_TYPE ,SEARCH_BOX ,DELETE_FLAG ,SEARCH_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES( null , #{name,jdbcType=VARCHAR} , #{title,jdbcType=VARCHAR} , #{version,jdbcType=VARCHAR} , #{createBy,jdbcType=VARCHAR} , #{pageCode,jdbcType=VARCHAR} , #{subTitle,jdbcType=VARCHAR} , #{updateBy,jdbcType=VARCHAR} , #{timeConfig,jdbcType=LONGVARCHAR} , #{shareConfig,jdbcType=LONGVARCHAR} , #{createUserId,jdbcType=VARCHAR} , #{backgroundColor,jdbcType=VARCHAR} , #{backgroundImgUrl,jdbcType=VARCHAR} , #{privateMarketConfig,jdbcType=LONGVARCHAR} , #{status,jdbcType=TINYINT} , #{bizType,jdbcType=TINYINT} , #{searchBox,jdbcType=INTEGER} , #{deleteFlag,jdbcType=INTEGER} , #{searchFlag,jdbcType=INTEGER} , #{createTime,jdbcType=TIMESTAMP} , #{updateTime,jdbcType=TIMESTAMP} ) ]]>
     * @param entity entity
     * @return int
     */
    int insert(MshopPageDraftDo entity);
    /**
     * desc:批量插入表:MSHOP_PAGE_DRAFT.<br/>
     * descSql =  INSERT INTO MSHOP_PAGE_DRAFT( ID ,NAME ,TITLE ,VERSION ,CREATE_BY ,PAGE_CODE ,SUB_TITLE ,UPDATE_BY ,TIME_CONFIG ,SHARE_CONFIG ,CREATE_USER_ID ,BACKGROUND_COLOR ,BACKGROUND_IMG_URL ,PRIVATE_MARKET_CONFIG ,STATUS ,BIZ_TYPE ,SEARCH_BOX ,DELETE_FLAG ,SEARCH_FLAG ,CREATE_TIME ,UPDATE_TIME )VALUES ( null , #{item.name,jdbcType=VARCHAR} , #{item.title,jdbcType=VARCHAR} , #{item.version,jdbcType=VARCHAR} , #{item.createBy,jdbcType=VARCHAR} , #{item.pageCode,jdbcType=VARCHAR} , #{item.subTitle,jdbcType=VARCHAR} , #{item.updateBy,jdbcType=VARCHAR} , #{item.timeConfig,jdbcType=LONGVARCHAR} , #{item.shareConfig,jdbcType=LONGVARCHAR} , #{item.createUserId,jdbcType=VARCHAR} , #{item.backgroundColor,jdbcType=VARCHAR} , #{item.backgroundImgUrl,jdbcType=VARCHAR} , #{item.privateMarketConfig,jdbcType=LONGVARCHAR} , #{item.status,jdbcType=TINYINT} , #{item.bizType,jdbcType=TINYINT} , #{item.searchBox,jdbcType=INTEGER} , #{item.deleteFlag,jdbcType=INTEGER} , #{item.searchFlag,jdbcType=INTEGER} , #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ) 
     * @param list list
     * @return int
     */
    int insertBatch(List<MshopPageDraftDo> list);
    /**
     * desc:根据主键删除数据:MSHOP_PAGE_DRAFT.<br/>
     * descSql =  <![CDATA[ DELETE FROM MSHOP_PAGE_DRAFT WHERE ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return int
     */
    int deleteById(Long id);
    /**
     * desc:根据主键获取数据:MSHOP_PAGE_DRAFT.<br/>
     * descSql =  SELECT * FROM MSHOP_PAGE_DRAFT WHERE <![CDATA[ ID = #{id,jdbcType=BIGINT} ]]>
     * @param id id
     * @return MshopPageDraftDo
     */
    MshopPageDraftDo getById(Long id);

    MshopPageDraftDo getByPageCode(@Param("pageCode")String pageCode,@Param("version") String version);

    MshopPageDraftDo getLastByPageCode(@Param("pageCode")String pageCode,@Param("status")Integer status);

    List<MshopPageDraftDo> getByNameTitle(@Param("name")String name, @Param("title")String title, @Param("createBy")String createBy,@Param("instancePageCodeList")List<String> instancePageCodeList);
    List<MshopPageDraftDo> getByNameTitleJoin(@Param("name")String name, @Param("title")String title);

    int updateStatusByPageCode(@Param("pageCode")String pageCode, @Param("version") String version,@Param("status") Integer status);
}
