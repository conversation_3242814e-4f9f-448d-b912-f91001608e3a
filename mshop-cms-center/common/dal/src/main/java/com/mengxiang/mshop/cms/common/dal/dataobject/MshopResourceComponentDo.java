package com.mengxiang.mshop.cms.common.dal.dataobject;

import java.util.Date;

/**
 * The table 资源位表
 * <AUTHOR>
 */
public class MshopResourceComponentDo{

    /**
     * id 主键.
     */
    private Long id;
    /**
     * name 页面名称.
     */
    private String name;
    /**
     * channel 渠道.
     */
    private String channel;
    /**
     * ownerId 所属者ID.
     */
    private String ownerId;
    /**
     * createBy 创建人.
     */
    private String createBy;
    /**
     * tenantId 租户id.
     */
    private String tenantId;
    /**
     * updateBy 修改人.
     */
    private String updateBy;
    /**
     * ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.
     */
    private String ownerType;
    /**
     * categoryId 类目.
     */
    private String categoryId;
    /**
     * timeConfig 生效方式.
     */
    private String timeConfig;
    /**
     * configDetail 组件配置详情数据.
     */
    private String configDetail;
    /**
     * resourceType 组件类型.
     */
    private String resourceType;
    /**
     * resourcePageType 页面位置.
     */
    private String resourcePageType;
    /**
     * status 状态.
     */
    private Integer status;
    /**
     * deleteFlag 是否删除 0 否 1是.
     */
    private Integer deleteFlag;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 修改时间.
     */
    private Date updateTime;

    private String orderValue;
    /**
     * sourceType 来源类型.
     */
    private String sourceType;

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getOrderValue() {
        return orderValue;
    }

    public void setOrderValue(String orderValue) {
        this.orderValue = orderValue;
    }

    /**
     * Set id 主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set name 页面名称.
     */
    public void setName(String name){
        this.name = name;
    }

    /**
     * Get name 页面名称.
     *
     * @return the string
     */
    public String getName(){
        return name;
    }

    /**
     * Set channel 渠道.
     */
    public void setChannel(String channel){
        this.channel = channel;
    }

    /**
     * Get channel 渠道.
     *
     * @return the string
     */
    public String getChannel(){
        return channel;
    }

    /**
     * Set ownerId 所属者ID.
     */
    public void setOwnerId(String ownerId){
        this.ownerId = ownerId;
    }

    /**
     * Get ownerId 所属者ID.
     *
     * @return the string
     */
    public String getOwnerId(){
        return ownerId;
    }

    /**
     * Set createBy 创建人.
     */
    public void setCreateBy(String createBy){
        this.createBy = createBy;
    }

    /**
     * Get createBy 创建人.
     *
     * @return the string
     */
    public String getCreateBy(){
        return createBy;
    }

    /**
     * Set tenantId 租户id.
     */
    public void setTenantId(String tenantId){
        this.tenantId = tenantId;
    }

    /**
     * Get tenantId 租户id.
     *
     * @return the string
     */
    public String getTenantId(){
        return tenantId;
    }

    /**
     * Set updateBy 修改人.
     */
    public void setUpdateBy(String updateBy){
        this.updateBy = updateBy;
    }

    /**
     * Get updateBy 修改人.
     *
     * @return the string
     */
    public String getUpdateBy(){
        return updateBy;
    }

    /**
     * Set ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.
     */
    public void setOwnerType(String ownerType){
        this.ownerType = ownerType;
    }

    /**
     * Get ownerType 所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.
     *
     * @return the string
     */
    public String getOwnerType(){
        return ownerType;
    }

    /**
     * Set categoryId 类目.
     */
    public void setCategoryId(String categoryId){
        this.categoryId = categoryId;
    }

    /**
     * Get categoryId 类目.
     *
     * @return the string
     */
    public String getCategoryId(){
        return categoryId;
    }

    /**
     * Set timeConfig 生效方式.
     */
    public void setTimeConfig(String timeConfig){
        this.timeConfig = timeConfig;
    }

    /**
     * Get timeConfig 生效方式.
     *
     * @return the string
     */
    public String getTimeConfig(){
        return timeConfig;
    }

    /**
     * Set configDetail 组件配置详情数据.
     */
    public void setConfigDetail(String configDetail){
        this.configDetail = configDetail;
    }

    /**
     * Get configDetail 组件配置详情数据.
     *
     * @return the string
     */
    public String getConfigDetail(){
        return configDetail;
    }

    /**
     * Set resourceType 组件类型.
     */
    public void setResourceType(String resourceType){
        this.resourceType = resourceType;
    }

    /**
     * Get resourceType 组件类型.
     *
     * @return the string
     */
    public String getResourceType(){
        return resourceType;
    }

    /**
     * Set resourcePageType 页面位置.
     */
    public void setResourcePageType(String resourcePageType){
        this.resourcePageType = resourcePageType;
    }

    /**
     * Get resourcePageType 页面位置.
     *
     * @return the string
     */
    public String getResourcePageType(){
        return resourcePageType;
    }

    /**
     * Set status 状态.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 状态.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set deleteFlag 是否删除 0 否 1是.
     */
    public void setDeleteFlag(Integer deleteFlag){
        this.deleteFlag = deleteFlag;
    }

    /**
     * Get deleteFlag 是否删除 0 否 1是.
     *
     * @return the string
     */
    public Integer getDeleteFlag(){
        return deleteFlag;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 修改时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 修改时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }


}
