<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopPageInstanceDoMapper">
    <!-- 自动生成,请修改 MSHOP_PAGE_INSTANCE.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="TYPE" property="type" jdbcType="VARCHAR" javaType="String"/>
        <result column="CHANNEL" property="channel" jdbcType="VARCHAR" javaType="String"/>
        <result column="OWNER_ID" property="ownerId" jdbcType="VARCHAR" javaType="String"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="PAGE_CODE" property="pageCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" javaType="String"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="OWNER_TYPE" property="ownerType" jdbcType="VARCHAR" javaType="String"/>
        <result column="MARKET_TYPE" property="marketType" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_USER_ID" property="createUserId" jdbcType="VARCHAR" javaType="String"/>
        <result column="TEMPLATE_CODE" property="templateCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="STATUS" property="status" jdbcType="TINYINT" javaType="Integer"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.TYPE,sf.CHANNEL,sf.OWNER_ID,sf.VERSION
        ,sf.CREATE_BY,sf.PAGE_CODE,sf.TENANT_ID,sf.UPDATE_BY,sf.OWNER_TYPE
        ,sf.MARKET_TYPE,sf.CREATE_USER_ID,sf.TEMPLATE_CODE,sf.STATUS,sf.DELETE_FLAG
        ,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>
    <sql id="Base_Column_List">
        ID,TYPE,CHANNEL,OWNER_ID,VERSION 
        ,CREATE_BY,PAGE_CODE,TENANT_ID,UPDATE_BY,OWNER_TYPE 
        ,MARKET_TYPE,CREATE_USER_ID,TEMPLATE_CODE,STATUS,DELETE_FLAG 
        ,CREATE_TIME,UPDATE_TIME
    </sql>


    <!--插入表:MSHOP_PAGE_INSTANCE-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_INSTANCE(
            ID
            ,TYPE
            ,CHANNEL
            ,OWNER_ID
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,MARKET_TYPE
            ,CREATE_USER_ID
            ,TEMPLATE_CODE
            ,STATUS
        )VALUES(
             null
            , #{type,jdbcType=VARCHAR}
            , #{channel,jdbcType=VARCHAR}
            , #{ownerId,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{tenantId,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{ownerType,jdbcType=VARCHAR}
            , #{marketType,jdbcType=VARCHAR}
            , #{createUserId,jdbcType=VARCHAR}
            , #{templateCode,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_PAGE_INSTANCE-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_PAGE_INSTANCE(
            ID
            ,TYPE
            ,CHANNEL
            ,OWNER_ID
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,MARKET_TYPE
            ,CREATE_USER_ID
            ,TEMPLATE_CODE
            ,STATUS
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.type,jdbcType=VARCHAR}
                , #{item.channel,jdbcType=VARCHAR}
                , #{item.ownerId,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.tenantId,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.ownerType,jdbcType=VARCHAR}
                , #{item.marketType,jdbcType=VARCHAR}
                , #{item.createUserId,jdbcType=VARCHAR}
                , #{item.templateCode,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_PAGE_INSTANCE-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-PAGE-INSTANCE-DELETEBYID*/ FROM MSHOP_PAGE_INSTANCE
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_PAGE_INSTANCE-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-INSTANCE-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <select id="getByPageCode" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-DRAFT-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE
        page_code = #{pageCode}
        <if test="version != null and version != ''">
            and version = #{version}
        </if>
        AND status in (1,2,3,4,5,6)
        AND delete_flag = 0
        order by id desc
        LIMIT 1
    </select>

    <update id="updateStatusAndVersionByPageCode" >
        UPDATE MSHOP_PAGE_INSTANCE
        <set>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            STATUS = #{status}
        </set>
        WHERE page_code = #{pageCode} AND delete_flag = 0
    </update>

    <update id="updatePageStatus">
        UPDATE MSHOP_PAGE_INSTANCE set STATUS = #{status}
        WHERE page_code = #{pageCode} AND delete_flag = 0
        <if test="version != null and version != ''">
            AND version = #{version}
        </if>
    </update>

    <update id="updatePageType">
        UPDATE MSHOP_PAGE_INSTANCE set type = #{type}
        WHERE page_code = #{pageCode}
    </update>

    <update id="updatePageTypeByOwnerId">
        UPDATE MSHOP_PAGE_INSTANCE set type = #{type}
        WHERE owner_id = #{ownerId} and owner_type = #{ownerType} and  type = #{originalType} and  type != #{type}
    </update>

    <!--根据条件查询总数:-->
    <select id="queryPageCount" resultType="int">
        SELECT COUNT(*)
        FROM MSHOP_PAGE_INSTANCE
        WHERE 1 = 1
        <include refid="selectPage"></include>
        AND delete_flag = 0
    </select>

    <!--分页查询-->
    <sql id="selectPage">

        <if test="draftPageCodeList != null">
            AND page_code  in
            <foreach collection="draftPageCodeList" separator="," open="(" item="pageCode" index="index" close=")">
                #{pageCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageCodeList != null">
            AND page_code  in
            <foreach collection="pageCodeList" separator="," open="(" item="pageCode" index="index" close=")">
                #{pageCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.typeList != null ">
            AND type in
            <foreach collection="pageQueryVO.typeList" separator="," open="(" item="type" index="index" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.type != null and pageQueryVO.type != ''">
            AND type  = #{pageQueryVO.type,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.channel != null and pageQueryVO.channel != ''">
            AND channel  = #{pageQueryVO.channel,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.tenantId != null and pageQueryVO.tenantId != ''">
            AND tenant_id = #{pageQueryVO.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.status != null ">
            AND status = #{pageQueryVO.status,jdbcType=TINYINT}
        </if>
        <if test="pageQueryVO.createBy != null and pageQueryVO.createBy != ''">
            and create_by like CONCAT('%',#{pageQueryVO.createBy,jdbcType=VARCHAR},'%')
        </if>
        <if test="pageQueryVO.updateBy != null and pageQueryVO.updateBy != ''">
            AND update_by = #{pageQueryVO.updateBy,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.ownerType != null and pageQueryVO.ownerType != ''">
            AND owner_type = #{pageQueryVO.ownerType,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.ownerId != null and pageQueryVO.ownerId != ''">
            AND owner_id = #{pageQueryVO.ownerId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.version != null and pageQueryVO.version != ''">
            AND version = #{pageQueryVO.version,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.createTimeStart != null ">
            <![CDATA[ AND create_time >= #{pageQueryVO.createTimeStart,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.createTimeEnd != null ">
            <![CDATA[ AND create_time <= #{pageQueryVO.createTimeEnd,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.updateTimeStart != null ">
            <![CDATA[ AND update_time >= #{pageQueryVO.updateTimeStart,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.updateTimeEnd != null ">
            <![CDATA[ AND update_time <=  #{pageQueryVO.updateTimeEnd,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.marketType != null and pageQueryVO.marketType !='' ">
            <![CDATA[ AND market_type =  #{pageQueryVO.marketType,jdbcType=VARCHAR} ]]>
        </if>
    </sql>
    <!--根据条件查询数据:CREDIT_EVENT-->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE 1=1
        <include refid="selectPage"></include>
        AND delete_flag = 0
        order by update_time desc
        limit #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!--根据条件查询数据:CREDIT_EVENT-->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE delete_flag = 0
        <if test="pageQueryVO.pageCodeList != null">
            AND page_code  in
            <foreach collection="pageQueryVO.pageCodeList" separator="," open="(" item="pageCode" index="index" close=")">
                #{pageCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.typeList != null ">
            AND type in
            <foreach collection="pageQueryVO.typeList" separator="," open="(" item="type" index="index" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.ownerIdList != null ">
            AND owner_id in
            <foreach collection="pageQueryVO.ownerIdList" separator="," open="(" item="type" index="index" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.type != null and pageQueryVO.type != ''">
            AND type  = #{pageQueryVO.type,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.channel != null and pageQueryVO.channel != ''">
            AND channel  = #{pageQueryVO.channel,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.tenantId != null and pageQueryVO.tenantId != ''">
            AND tenant_id = #{pageQueryVO.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.status != null ">
            AND status = #{pageQueryVO.status,jdbcType=TINYINT}
        </if>
        <if test="pageQueryVO.createBy != null and pageQueryVO.createBy != ''">
            and create_by like CONCAT('%',#{pageQueryVO.createBy,jdbcType=VARCHAR},'%')
        </if>
        <if test="pageQueryVO.updateBy != null and pageQueryVO.updateBy != ''">
            AND update_by = #{pageQueryVO.updateBy,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.ownerType != null and pageQueryVO.ownerType != ''">
            AND owner_type = #{pageQueryVO.ownerType,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.ownerId != null and pageQueryVO.ownerId != ''">
            AND owner_id = #{pageQueryVO.ownerId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.version != null and pageQueryVO.version != ''">
            AND version = #{pageQueryVO.version,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.createTimeStart != null ">
            <![CDATA[ AND create_time >= #{pageQueryVO.createTimeStart,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.createTimeEnd != null ">
            <![CDATA[ AND create_time <= #{pageQueryVO.createTimeEnd,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.updateTimeStart != null ">
            <![CDATA[ AND update_time >= #{pageQueryVO.updateTimeStart,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.updateTimeEnd != null ">
            <![CDATA[ AND update_time <=  #{pageQueryVO.updateTimeEnd,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.marketType != null and pageQueryVO.marketType !='' ">
            <![CDATA[ AND market_type =  #{pageQueryVO.marketType,jdbcType=VARCHAR} ]]>
        </if>
        order by update_time desc,create_time desc ,id desc
    </select>

    <select id="querySystemPageByTemplateCode" resultMap="BaseResultMap" >
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE owner_id = #{ownerId} and owner_type = #{ownerType} and template_code = #{templateCode}
        AND status = 4
        AND delete_flag = 0
        order by id desc
        limit 1
    </select>
    <select id="queryByStatus" resultMap="BaseResultMap" >
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE status = #{status}
        AND delete_flag = 0
    </select>

    <select id="queryByStatusList" resultMap="BaseResultMap" >
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE status in
        <foreach collection="statusList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND delete_flag = 0
    </select>

    <select id="selectPagesByTypes" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE owner_id = #{ownerId} and owner_type = #{ownerType}
        and type in
        <foreach collection="types" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND status in (1,2,3,4,5,6)
        AND delete_flag = 0
    </select>

    <select id="selectPageListByOwner" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE owner_type = #{ownerType}
        <if test="ownerId != null and ownerId != ''">
            and owner_id = #{ownerId}
        </if>
        AND status in (1,2,3,4,5,6)
        AND delete_flag = 0
    </select>

    <select id="selectPageListByType" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE  type in
        <foreach collection="typeList" separator="," open="(" item="type" index="index" close=")">
            #{type,jdbcType=VARCHAR}
        </foreach>
        AND status in (1,2,3,4,5,6)
        AND delete_flag = 0
    </select>

    <select id="selectPageByCodes" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE page_code in
        <foreach collection="pageCodes" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND status in (1,2,3,4,5,6)
        AND delete_flag = 0
    </select>

    <!--根据条件查询数据:CREDIT_EVENT-->
    <select id="selectListByOwnerIdList" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_INSTANCE
        WHERE delete_flag = 0
            AND ( owner_id in
            <foreach collection="pageQueryVO.ownerIdList" separator="," open="(" item="type" index="index" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
            or owner_type = #{pageQueryVO.ownerType,jdbcType=VARCHAR}
        )
        <if test="pageQueryVO.pageCodeList != null">
            AND page_code  in
            <foreach collection="pageQueryVO.pageCodeList" separator="," open="(" item="pageCode" index="index" close=")">
                #{pageCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.typeList != null ">
            AND type in
            <foreach collection="pageQueryVO.typeList" separator="," open="(" item="type" index="index" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="pageQueryVO.type != null and pageQueryVO.type != ''">
            AND type  = #{pageQueryVO.type,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.channel != null and pageQueryVO.channel != ''">
            AND channel  = #{pageQueryVO.channel,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.tenantId != null and pageQueryVO.tenantId != ''">
            AND tenant_id = #{pageQueryVO.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.status != null ">
            AND status = #{pageQueryVO.status,jdbcType=TINYINT}
        </if>
        <if test="pageQueryVO.createBy != null and pageQueryVO.createBy != ''">
            and create_by like CONCAT('%',#{pageQueryVO.createBy,jdbcType=VARCHAR},'%')
        </if>
        <if test="pageQueryVO.updateBy != null and pageQueryVO.updateBy != ''">
            AND update_by = #{pageQueryVO.updateBy,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.version != null and pageQueryVO.version != ''">
            AND version = #{pageQueryVO.version,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.createTimeStart != null ">
            <![CDATA[ AND create_time >= #{pageQueryVO.createTimeStart,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.createTimeEnd != null ">
            <![CDATA[ AND create_time <= #{pageQueryVO.createTimeEnd,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.updateTimeStart != null ">
            <![CDATA[ AND update_time >= #{pageQueryVO.updateTimeStart,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.updateTimeEnd != null ">
            <![CDATA[ AND update_time <=  #{pageQueryVO.updateTimeEnd,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="pageQueryVO.marketType != null and pageQueryVO.marketType !='' ">
            <![CDATA[ AND market_type =  #{pageQueryVO.marketType,jdbcType=VARCHAR} ]]>
        </if>
        order by update_time desc
    </select>
</mapper>
