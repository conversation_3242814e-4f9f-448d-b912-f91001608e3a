<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopPageWorkbenchDoMapper">
  <resultMap id="BaseResultMap" type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="page_code" jdbcType="VARCHAR" property="pageCode" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="proc_inst_code" jdbcType="VARCHAR" property="procInstCode" />
    <result column="current_task_key" jdbcType="VARCHAR" property="currentTaskKey" />
    <result column="button_control" jdbcType="VARCHAR" property="buttonControl" />
    <result column="process_status" jdbcType="INTEGER" property="processStatus" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="flow_info" property="flowInfo" jdbcType="LONGVARCHAR" javaType="String"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, page_code, version, business_key, proc_inst_code,
    current_task_key, button_control, process_status, delete_flag, create_by, create_time,
    update_by, update_time, create_user_id,flow_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mshop_page_workbench
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo" useGeneratedKeys="true">
    insert into mshop_page_workbench (page_code, version, business_key,
                                      proc_inst_code,
                                      current_task_key, button_control, process_status,
                                      delete_flag, create_by, create_time,
                                      update_by, update_time, create_user_id,flow_info
    )
    values (#{pageCode,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{businessKey,jdbcType=VARCHAR},
             #{procInstCode,jdbcType=VARCHAR},
            #{currentTaskKey,jdbcType=VARCHAR}, #{buttonControl,jdbcType=VARCHAR}, #{processStatus,jdbcType=INTEGER},
            #{deleteFlag,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR},#{flowInfo,jdbcType=LONGVARCHAR}
           )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo" useGeneratedKeys="true">
    insert into mshop_page_workbench
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageCode != null">
        page_code,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="procInstCode != null">
        proc_inst_code,
      </if>
      <if test="currentTaskKey != null">
        current_task_key,
      </if>
      <if test="buttonControl != null">
        button_control,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="flowInfo != null">
        flow_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageCode != null">
        #{pageCode,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="procInstCode != null">
        #{procInstCode,jdbcType=VARCHAR},
      </if>
      <if test="currentTaskKey != null">
        #{currentTaskKey,jdbcType=VARCHAR},
      </if>
      <if test="buttonControl != null">
        #{buttonControl,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="flowInfo != null">
        #{flowInfo,jdbcType=LONGVARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo">
    update mshop_page_workbench
    <set>
      <if test="pageCode != null">
        page_code = #{pageCode,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="procInstCode != null">
        proc_inst_code = #{procInstCode,jdbcType=VARCHAR},
      </if>
      <if test="currentTaskKey != null">
        current_task_key = #{currentTaskKey,jdbcType=VARCHAR},
      </if>
      <if test="buttonControl != null">
        button_control = #{buttonControl,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="flowInfo != null">
        flow_info = #{flowInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo">
    update mshop_page_workbench
    set page_code = #{pageCode,jdbcType=VARCHAR},
        version = #{version,jdbcType=VARCHAR},
        business_key = #{businessKey,jdbcType=VARCHAR},
        proc_inst_code = #{procInstCode,jdbcType=VARCHAR},
        current_task_key = #{currentTaskKey,jdbcType=VARCHAR},
        button_control = #{buttonControl,jdbcType=VARCHAR},
        process_status = #{processStatus,jdbcType=INTEGER},
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
        create_by = #{createBy,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_user_id = #{createUserId,jdbcType=VARCHAR},
        flow_info =#{flowInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where page_code = #{pageCode,jdbcType=VARCHAR}
    <if test="version != null and version != ''">
      and version = #{version}
    </if>
    order by id desc
  </select>
  <select id="selectListByProcessStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where page_code = #{pageCode,jdbcType=VARCHAR}
    <if test="version != null and version != ''">
      and version = #{version}
    </if>
    and process_status = #{processStatus,jdbcType=INTEGER}
    order by id desc
  </select>

  <update id="updateByBusinessKey">
    update mshop_page_workbench
    set
        current_task_key = #{currentTaskKey,jdbcType=VARCHAR},
        button_control = #{buttonControl,jdbcType=VARCHAR},
        process_status = #{processStatus,jdbcType=INTEGER},
    where  business_key = #{businessKey,jdbcType=VARCHAR}
      and  page_code = #{pageCode,jdbcType=VARCHAR}
      and version = #{version,jdbcType=VARCHAR}        
      and process_status = 0
  </update>
  
  <select id="selectOneByBusinessKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where business_key = #{businessKey,jdbcType=VARCHAR}
    and  page_code = #{pageCode,jdbcType=VARCHAR}
    and version = #{version,jdbcType=VARCHAR}
    and process_status = 0
    and delete_flag = 0
  </select>

  <update id="updateVersion">
    update mshop_page_workbench
    set
      current_task_key = '',
      button_control = '',
      version = #{version}
      where  business_key = #{businessKey,jdbcType=VARCHAR}
  </update>

  <select id="selectListByBusinessKeyAndPageCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where business_key = #{businessKey,jdbcType=VARCHAR}
    and  page_code = #{pageCode,jdbcType=VARCHAR}
    and delete_flag = 0
    order by id desc
  </select>

  <select id="selectListByBusinessKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where business_key = #{businessKey,jdbcType=VARCHAR}
    and delete_flag = 0
    order by id desc
  </select>

  <select id="selectListByPageCodeAndVersion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where page_code = #{pageCode,jdbcType=VARCHAR}
    and version = #{version,jdbcType=VARCHAR}
    and delete_flag = 0
    order by id desc
  </select>

  <select id="selectListByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mshop_page_workbench
    where process_status = 0
    and delete_flag = 0
    and flow_info -> '$.workbenchType'= '2'
    AND create_time &lt;= #{time,jdbcType=TIMESTAMP}
  </select>


</mapper>