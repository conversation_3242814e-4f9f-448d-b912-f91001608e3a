<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopPageTemplateDoMapper">
    <!-- 自动生成,请修改 MSHOP_PAGE_TEMPLATE.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageTemplateDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="OWNER_ID" property="ownerId" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" javaType="String"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="OWNER_TYPE" property="ownerType" jdbcType="VARCHAR" javaType="String"/>
        <result column="PAGE_USE_RULE" property="pageUseRule" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="PAGE_USE_TYPE" property="pageUseType" jdbcType="VARCHAR" javaType="String"/>
        <result column="USE_CHANNELS" property="useChannels" jdbcType="VARCHAR" javaType="String"/>
        <result column="TEMPLATE_CODE" property="templateCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="TEMPLATE_DESC" property="templateDesc" jdbcType="VARCHAR" javaType="String"/>
        <result column="TEMPLATE_NAME" property="templateName" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPONENT_USE_RULE" property="componentUseRule" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="TEMPLATE_IMAGE_URL" property="templateImageUrl" jdbcType="VARCHAR" javaType="String"/>
        <result column="STATUS" property="status" jdbcType="TINYINT" javaType="Integer"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.OWNER_ID,sf.CREATE_BY,sf.TENANT_ID,sf.UPDATE_BY
        ,sf.OWNER_TYPE,sf.PAGE_USE_RULE,sf.PAGE_USE_TYPE,sf.USE_CHANNELS,sf.TEMPLATE_CODE
        ,sf.TEMPLATE_DESC,sf.TEMPLATE_NAME,sf.COMPONENT_USE_RULE,sf.TEMPLATE_IMAGE_URL,sf.STATUS
        ,sf.DELETE_FLAG,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>
    <sql id="Base_Column_List">
        ID,OWNER_ID,CREATE_BY,TENANT_ID,UPDATE_BY 
        ,OWNER_TYPE,PAGE_USE_RULE,PAGE_USE_TYPE,USE_CHANNELS,TEMPLATE_CODE 
        ,TEMPLATE_DESC,TEMPLATE_NAME,COMPONENT_USE_RULE,TEMPLATE_IMAGE_URL,STATUS 
        ,DELETE_FLAG,CREATE_TIME,UPDATE_TIME
    </sql>


    <!--插入表:MSHOP_PAGE_TEMPLATE-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_TEMPLATE(
            ID
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,PAGE_USE_RULE
            ,PAGE_USE_TYPE
            ,USE_CHANNELS
            ,TEMPLATE_CODE
            ,TEMPLATE_DESC
            ,TEMPLATE_NAME
            ,COMPONENT_USE_RULE
            ,TEMPLATE_IMAGE_URL
            ,STATUS
        )VALUES(
             null
            , #{ownerId,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{tenantId,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{ownerType,jdbcType=VARCHAR}
            , #{pageUseRule,jdbcType=LONGVARCHAR}
            , #{pageUseType,jdbcType=VARCHAR}
            , #{useChannels,jdbcType=VARCHAR}
            , #{templateCode,jdbcType=VARCHAR}
            , #{templateDesc,jdbcType=VARCHAR}
            , #{templateName,jdbcType=VARCHAR}
            , #{componentUseRule,jdbcType=LONGVARCHAR}
            , #{templateImageUrl,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_PAGE_TEMPLATE-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_PAGE_TEMPLATE(
            ID
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,PAGE_USE_RULE
            ,PAGE_USE_TYPE
            ,USE_CHANNELS
            ,TEMPLATE_CODE
            ,TEMPLATE_DESC
            ,TEMPLATE_NAME
            ,COMPONENT_USE_RULE
            ,TEMPLATE_IMAGE_URL
            ,STATUS
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.ownerId,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.tenantId,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.ownerType,jdbcType=VARCHAR}
                , #{item.pageUseRule,jdbcType=LONGVARCHAR}
                , #{item.pageUseType,jdbcType=VARCHAR}
                , #{item.useChannels,jdbcType=VARCHAR}
                , #{item.templateCode,jdbcType=VARCHAR}
                , #{item.templateDesc,jdbcType=VARCHAR}
                , #{item.templateName,jdbcType=VARCHAR}
                , #{item.componentUseRule,jdbcType=LONGVARCHAR}
                , #{item.templateImageUrl,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_PAGE_TEMPLATE-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-PAGE-TEMPLATE-DELETEBYID*/ FROM MSHOP_PAGE_TEMPLATE
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_PAGE_TEMPLATE-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-TEMPLATE-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_TEMPLATE
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <select id="getByTemplateCode" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_TEMPLATE
        WHERE template_code = #{templateCode} and delete_flag = 0
    </select>

    <select id="getByOwnerId" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_TEMPLATE
        WHERE owner_id = #{ownerId} and owner_type = #{ownerType} and delete_flag = 0
    </select>
</mapper>
