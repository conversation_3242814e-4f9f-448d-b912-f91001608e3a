<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopPageDraftDoMapper">
    <!-- 自动生成,请修改 MSHOP_PAGE_DRAFT.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="NAME" property="name" jdbcType="VARCHAR" javaType="String"/>
        <result column="TITLE" property="title" jdbcType="VARCHAR" javaType="String"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="PAGE_CODE" property="pageCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="SUB_TITLE" property="subTitle" jdbcType="VARCHAR" javaType="String"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="TIME_CONFIG" property="timeConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="SHARE_CONFIG" property="shareConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="CREATE_USER_ID" property="createUserId" jdbcType="VARCHAR" javaType="String"/>
        <result column="BACKGROUND_COLOR" property="backgroundColor" jdbcType="VARCHAR" javaType="String"/>
        <result column="BACKGROUND_IMG_URL" property="backgroundImgUrl" jdbcType="VARCHAR" javaType="String"/>
        <result column="PRIVATE_MARKET_CONFIG" property="privateMarketConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="STATUS" property="status" jdbcType="TINYINT" javaType="Integer"/>
        <result column="BIZ_TYPE" property="bizType" jdbcType="TINYINT" javaType="Integer"/>
        <result column="SEARCH_BOX" property="searchBox" jdbcType="INTEGER" javaType="Integer"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="SEARCH_FLAG" property="searchFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.NAME,sf.TITLE,sf.VERSION,sf.CREATE_BY
        ,sf.PAGE_CODE,sf.SUB_TITLE,sf.UPDATE_BY,sf.TIME_CONFIG,sf.SHARE_CONFIG
        ,sf.CREATE_USER_ID,sf.BACKGROUND_COLOR,sf.BACKGROUND_IMG_URL,sf.PRIVATE_MARKET_CONFIG,sf.STATUS
        ,sf.BIZ_TYPE,sf.SEARCH_BOX,sf.DELETE_FLAG,sf.SEARCH_FLAG,sf.CREATE_TIME
        ,sf.UPDATE_TIME
    </sql>
    <sql id="Base_Column_List">
        ID,NAME,TITLE,VERSION,CREATE_BY 
        ,PAGE_CODE,SUB_TITLE,UPDATE_BY,TIME_CONFIG,SHARE_CONFIG 
        ,CREATE_USER_ID,BACKGROUND_COLOR,BACKGROUND_IMG_URL,PRIVATE_MARKET_CONFIG,STATUS 
        ,BIZ_TYPE,SEARCH_BOX,DELETE_FLAG,SEARCH_FLAG,CREATE_TIME 
        ,UPDATE_TIME
    </sql>


    <!--插入表:MSHOP_PAGE_DRAFT-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_DRAFT(
            ID
            ,NAME
            ,TITLE
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,SUB_TITLE
            ,UPDATE_BY
            ,TIME_CONFIG
            ,SHARE_CONFIG
            ,CREATE_USER_ID
            ,BACKGROUND_COLOR
            ,BACKGROUND_IMG_URL
            ,PRIVATE_MARKET_CONFIG
            ,STATUS
            ,BIZ_TYPE
            ,SEARCH_BOX
            ,SEARCH_FLAG
        )VALUES(
             null
            , #{name,jdbcType=VARCHAR}
            , #{title,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{subTitle,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{shareConfig,jdbcType=LONGVARCHAR}
            , #{createUserId,jdbcType=VARCHAR}
            , #{backgroundColor,jdbcType=VARCHAR}
            , #{backgroundImgUrl,jdbcType=VARCHAR}
            , #{privateMarketConfig,jdbcType=LONGVARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{bizType,jdbcType=TINYINT}
            , #{searchBox,jdbcType=INTEGER}
            , #{searchFlag,jdbcType=INTEGER}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_PAGE_DRAFT-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_PAGE_DRAFT(
            ID
            ,NAME
            ,TITLE
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,SUB_TITLE
            ,UPDATE_BY
            ,TIME_CONFIG
            ,SHARE_CONFIG
            ,CREATE_USER_ID
            ,BACKGROUND_COLOR
            ,BACKGROUND_IMG_URL
            ,PRIVATE_MARKET_CONFIG
            ,STATUS
            ,BIZ_TYPE
            ,SEARCH_BOX
            ,SEARCH_FLAG
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.name,jdbcType=VARCHAR}
                , #{item.title,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.subTitle,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.shareConfig,jdbcType=LONGVARCHAR}
                , #{item.createUserId,jdbcType=VARCHAR}
                , #{item.backgroundColor,jdbcType=VARCHAR}
                , #{item.backgroundImgUrl,jdbcType=VARCHAR}
                , #{item.privateMarketConfig,jdbcType=LONGVARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.bizType,jdbcType=TINYINT}
                , #{item.searchBox,jdbcType=INTEGER}
                , #{item.searchFlag,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_PAGE_DRAFT-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-PAGE-DRAFT-DELETEBYID*/ FROM MSHOP_PAGE_DRAFT
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_PAGE_DRAFT-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-DRAFT-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_DRAFT
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <select id="getByPageCode" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-DRAFT-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_DRAFT
        WHERE
        page_code = #{pageCode}
        <if test="version != null and version != ''">
            and version = #{version}
        </if>
        AND delete_flag = 0
        order by id desc
        LIMIT 1
    </select>

    <select id="getLastByPageCode" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-DRAFT-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_DRAFT
        WHERE page_code = #{pageCode}
        <if test="status != null">
            and STATUS = #{status}
        </if>
        AND delete_flag = 0
        order by id desc LIMIT 1
    </select>

    <select id="getByNameTitle" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-DRAFT-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_DRAFT
        WHERE id in (
        SELECT max(id)
        FROM MSHOP_PAGE_DRAFT
        WHERE 1 = 1
        <if test="instancePageCodeList != null">
            AND page_code  in
            <foreach collection="instancePageCodeList" separator="," open="(" item="pageCode" index="index" close=")">
                #{pageCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        AND delete_flag = 0
        group by page_code
        )
        <if test="name != null and name != ''">
            and name like CONCAT('%',#{name,jdbcType=VARCHAR},'%')
        </if>
        <if test="title != null and title != ''">
            and title like CONCAT('%',#{title,jdbcType=VARCHAR},'%')
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by like CONCAT('%',#{createBy,jdbcType=VARCHAR},'%')
        </if>
        order by id desc
    </select>

    <select id="getByNameTitleJoin" resultMap="BaseResultMap">
        SELECT m1.page_code,m1.id
        FROM mshop_page_draft m1
                 JOIN (
            SELECT page_code, MAX(id) as max_id
            FROM mshop_page_draft where delete_flag = 0
            GROUP BY page_code
        ) m2  ON m1.page_code = m2.page_code AND m1.id = m2.max_id
        WHERE  m1.delete_flag = 0
        <if test="name != null and name != ''">
            and m1.name like CONCAT('%',#{name,jdbcType=VARCHAR},'%')
        </if>
        <if test="title != null and title != ''">
            and m1.title like CONCAT('%',#{title,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <update id="updateStatusByPageCode" >
        UPDATE mshop_page_draft set STATUS = #{status}
        WHERE page_code = #{pageCode} AND delete_flag = 0
        <if test="version != null and version != ''">
            and version = #{version}
        </if>

    </update>
</mapper>
