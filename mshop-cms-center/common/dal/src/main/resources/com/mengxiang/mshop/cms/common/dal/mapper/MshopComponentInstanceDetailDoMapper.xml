<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopComponentInstanceDetailDoMapper">
    <!-- 自动生成,请修改 MSHOP_COMPONENT_INSTANCE_DETAIL.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR" javaType="String"/>
        <result column="PAGE_CODE" property="pageCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="TIME_CONFIG" property="timeConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="CONFIG_DETAIL" property="configDetail" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="COMPONENT_CODE" property="componentCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPONENT_TYPE" property="componentType" jdbcType="VARCHAR" javaType="String"/>
        <result column="DIRECT_USER_GROUP" property="directUserGroup" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="CONFIG_DETAIL_CODE" property="configDetailCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPONENT_DETAIL_TYPE" property="componentDetailType" jdbcType="VARCHAR" javaType="String"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="ORDER_VALUE" property="orderValue" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.VERSION,sf.PAGE_CODE,sf.TIME_CONFIG,sf.CONFIG_DETAIL
        ,sf.COMPONENT_CODE,sf.COMPONENT_TYPE,sf.DIRECT_USER_GROUP,sf.CONFIG_DETAIL_CODE,sf.COMPONENT_DETAIL_TYPE
        ,sf.DELETE_FLAG,sf.ORDER_VALUE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>
    <sql id="Base_Column_List">
        ID,VERSION,PAGE_CODE,TIME_CONFIG,CONFIG_DETAIL 
        ,COMPONENT_CODE,COMPONENT_TYPE,DIRECT_USER_GROUP,CONFIG_DETAIL_CODE,COMPONENT_DETAIL_TYPE 
        ,DELETE_FLAG,ORDER_VALUE,CREATE_TIME,UPDATE_TIME
    </sql>


    <!--插入表:MSHOP_COMPONENT_INSTANCE_DETAIL-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_COMPONENT_INSTANCE_DETAIL(
            ID
            ,VERSION
            ,PAGE_CODE
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,COMPONENT_CODE
            ,COMPONENT_TYPE
            ,DIRECT_USER_GROUP
            ,CONFIG_DETAIL_CODE
            ,COMPONENT_DETAIL_TYPE
            ,ORDER_VALUE
        )VALUES(
             null
            , #{version,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{configDetail,jdbcType=LONGVARCHAR}
            , #{componentCode,jdbcType=VARCHAR}
            , #{componentType,jdbcType=VARCHAR}
            , #{directUserGroup,jdbcType=LONGVARCHAR}
            , #{configDetailCode,jdbcType=VARCHAR}
            , #{componentDetailType,jdbcType=VARCHAR}
            , #{orderValue,jdbcType=INTEGER}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_COMPONENT_INSTANCE_DETAIL-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_COMPONENT_INSTANCE_DETAIL(
            ID
            ,VERSION
            ,PAGE_CODE
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,COMPONENT_CODE
            ,COMPONENT_TYPE
            ,DIRECT_USER_GROUP
            ,CONFIG_DETAIL_CODE
            ,COMPONENT_DETAIL_TYPE
            ,ORDER_VALUE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.version,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.configDetail,jdbcType=LONGVARCHAR}
                , #{item.componentCode,jdbcType=VARCHAR}
                , #{item.componentType,jdbcType=VARCHAR}
                , #{item.directUserGroup,jdbcType=LONGVARCHAR}
                , #{item.configDetailCode,jdbcType=VARCHAR}
                , #{item.componentDetailType,jdbcType=VARCHAR}
                , #{item.orderValue,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_COMPONENT_INSTANCE_DETAIL-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-COMPONENT-INSTANCE-DETAIL-DELETEBYID*/ FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_COMPONENT_INSTANCE_DETAIL-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-COMPONENT-INSTANCE-DETAIL-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <update id="updateDynamic" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo">
        update MSHOP_COMPONENT_INSTANCE_DETAIL
        <set>
            <if test="configDetail != null and configDetail != ''">
                CONFIG_DETAIL = #{configDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="timeConfig != null and timeConfig != ''">
                TIME_CONFIG = #{timeConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="deleteFlag != null">
                DELETE_FLAG = #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="orderValue != null">
                ORDER_VALUE = #{orderValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--查询组件详情:MSHOP_PERSON_INTERVENE-->
    <select id="queryByComponent" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-COMPONENT-INSTANCE-DETAIL-QUERYBYCOMPONENT*/  <include refid="Base_Column_List" />
        FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
        page_code = #{pageCode,jdbcType=VARCHAR}
        AND component_code = #{componentCode,jdbcType=VARCHAR}
        AND version = #{version,jdbcType=VARCHAR}
        AND delete_flag = 0
    </select>

    <!--查询组件详情-->
    <select id="queryByPageCode" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-COMPONENT-INSTANCE-DETAIL-QUERYBYPAGECODE*/  <include refid="Base_Column_List" />
        FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
        page_code = #{pageCode,jdbcType=VARCHAR}
        AND version = #{version,jdbcType=VARCHAR}
        AND delete_flag = 0
    </select>
</mapper>
