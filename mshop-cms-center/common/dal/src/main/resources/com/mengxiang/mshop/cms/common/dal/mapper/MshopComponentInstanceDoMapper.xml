<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopComponentInstanceDoMapper">
    <!-- 自动生成,请修改 MSHOP_COMPONENT_INSTANCE.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="TYPE" property="type" jdbcType="VARCHAR" javaType="String"/>
        <result column="USE_RULE" property="useRule" jdbcType="VARCHAR" javaType="String"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR" javaType="String"/>
        <result column="PAGE_CODE" property="pageCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="META_CONFIG" property="metaConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="TIME_CONFIG" property="timeConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="COMPONENT_CODE" property="componentCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="DIRECT_USER_GROUP" property="directUserGroup" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="ORDER_VALUE" property="orderValue" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.TYPE,sf.USE_RULE,sf.VERSION,sf.PAGE_CODE
        ,sf.META_CONFIG,sf.TIME_CONFIG,sf.COMPONENT_CODE,sf.DIRECT_USER_GROUP,sf.DELETE_FLAG
        ,sf.ORDER_VALUE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>
    <sql id="Base_Column_List">
        ID,TYPE,USE_RULE,VERSION,PAGE_CODE 
        ,META_CONFIG,TIME_CONFIG,COMPONENT_CODE,DIRECT_USER_GROUP,DELETE_FLAG 
        ,ORDER_VALUE,CREATE_TIME,UPDATE_TIME
    </sql>


    <!--插入表:MSHOP_COMPONENT_INSTANCE-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_COMPONENT_INSTANCE(
            ID
            ,TYPE
            ,USE_RULE
            ,VERSION
            ,PAGE_CODE
            ,META_CONFIG
            ,TIME_CONFIG
            ,COMPONENT_CODE
            ,DIRECT_USER_GROUP
            ,ORDER_VALUE
        )VALUES(
             null
            , #{type,jdbcType=VARCHAR}
            , #{useRule,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{metaConfig,jdbcType=LONGVARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{componentCode,jdbcType=VARCHAR}
            , #{directUserGroup,jdbcType=LONGVARCHAR}
            , #{orderValue,jdbcType=INTEGER}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_COMPONENT_INSTANCE-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_COMPONENT_INSTANCE(
            ID
            ,TYPE
            ,USE_RULE
            ,VERSION
            ,PAGE_CODE
            ,META_CONFIG
            ,TIME_CONFIG
            ,COMPONENT_CODE
            ,DIRECT_USER_GROUP
            ,ORDER_VALUE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.type,jdbcType=VARCHAR}
                , #{item.useRule,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.metaConfig,jdbcType=LONGVARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.componentCode,jdbcType=VARCHAR}
                , #{item.directUserGroup,jdbcType=LONGVARCHAR}
                , #{item.orderValue,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="updateDynamic" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo">
        update MSHOP_COMPONENT_INSTANCE
        <set>
            <if test="type != null and type != ''">
                TYPE = #{type,jdbcType=VARCHAR},
            </if>
            <if test="useRule != null and useRule != ''">
                USE_RULE = #{useRule,jdbcType=VARCHAR},
            </if>
            <if test="metaConfig != null and metaConfig !=''">
                META_CONFIG = #{metaConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="timeConfig != null">
                TIME_CONFIG = #{timeConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="deleteFlag != null">
                DELETE_FLAG = #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="orderValue != null">
                ORDER_VALUE = #{orderValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--根据主键删除数据:MSHOP_COMPONENT_INSTANCE-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-COMPONENT-INSTANCE-DELETEBYID*/ FROM MSHOP_COMPONENT_INSTANCE
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_COMPONENT_INSTANCE-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-COMPONENT-INSTANCE-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_COMPONENT_INSTANCE
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <!--查询组件-->
    <select id="queryByPageCode" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-COMPONENT-INSTANCE-QUERYBYPAGECODE*/  <include refid="Base_Column_List" />
        FROM MSHOP_COMPONENT_INSTANCE
        WHERE
        page_code = #{pageCode,jdbcType=VARCHAR}
        AND version = #{version,jdbcType=VARCHAR}
        AND delete_flag = 0
    </select>


    <select id="queryByComponentCode" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-COMPONENT-INSTANCE-QUERYBYPAGECODE*/  <include refid="Base_Column_List" />
        FROM MSHOP_COMPONENT_INSTANCE
        WHERE page_code = #{pageCode,jdbcType=VARCHAR}
        AND component_code = #{componentCode}
        AND version = #{version,jdbcType=VARCHAR}
        AND delete_flag = 0
    </select>

    <update id="updateMetaConfig" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo">
        update MSHOP_COMPONENT_INSTANCE
        <set>
            <if test="metaConfig != null and metaConfig !=''">
                META_CONFIG = #{metaConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="directUserGroup != null and directUserGroup !=''">
                DIRECT_USER_GROUP = #{directUserGroup,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
