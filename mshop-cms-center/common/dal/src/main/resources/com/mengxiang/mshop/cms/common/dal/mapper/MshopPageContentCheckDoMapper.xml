<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopPageContentCheckDoMapper">
    <!-- 自动生成,请修改 MSHOP_PAGE_CONTENT_CHECK.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="DATA_ID" property="dataId" jdbcType="VARCHAR" javaType="String"/>
        <result column="CONTENT" property="content" jdbcType="VARCHAR" javaType="String"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR" javaType="String"/>
        <result column="PAGE_CODE" property="pageCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="CHECK_RESULT" property="checkResult" jdbcType="VARCHAR" javaType="String"/>
        <result column="CHECK_STATUS" property="checkStatus" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPONENT_CODE" property="componentCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPONENT_CONFIG_CODE" property="componentConfigCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="TINYINT" javaType="Integer"/>
        <result column="CONTENT_TYPE" property="contentType" jdbcType="TINYINT" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.DATA_ID,sf.CONTENT,sf.VERSION,sf.PAGE_CODE
        ,sf.CHECK_RESULT,sf.CHECK_STATUS,sf.COMPONENT_CODE,sf.COMPONENT_CONFIG_CODE,sf.DELETE_FLAG
        ,sf.CONTENT_TYPE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>
    <sql id="Base_Column_List">
        ID,DATA_ID,CONTENT,VERSION,PAGE_CODE 
        ,CHECK_RESULT,CHECK_STATUS,COMPONENT_CODE,COMPONENT_CONFIG_CODE,DELETE_FLAG 
        ,CONTENT_TYPE,CREATE_TIME,UPDATE_TIME
    </sql>


    <!--插入表:MSHOP_PAGE_CONTENT_CHECK-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_CONTENT_CHECK(
            ID
            ,DATA_ID
            ,CONTENT
            ,VERSION
            ,PAGE_CODE
            ,CHECK_RESULT
            ,CHECK_STATUS
            ,COMPONENT_CODE
            ,COMPONENT_CONFIG_CODE
            ,CONTENT_TYPE
        )VALUES(
             null
            , #{dataId,jdbcType=VARCHAR}
            , #{content,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{checkResult,jdbcType=VARCHAR}
            , #{checkStatus,jdbcType=VARCHAR}
            , #{componentCode,jdbcType=VARCHAR}
            , #{componentConfigCode,jdbcType=VARCHAR}
            , #{contentType,jdbcType=TINYINT}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_PAGE_CONTENT_CHECK-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_PAGE_CONTENT_CHECK(
            ID
            ,DATA_ID
            ,CONTENT
            ,VERSION
            ,PAGE_CODE
            ,CHECK_RESULT
            ,CHECK_STATUS
            ,COMPONENT_CODE
            ,COMPONENT_CONFIG_CODE
            ,CONTENT_TYPE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.dataId,jdbcType=VARCHAR}
                , #{item.content,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.checkResult,jdbcType=VARCHAR}
                , #{item.checkStatus,jdbcType=VARCHAR}
                , #{item.componentCode,jdbcType=VARCHAR}
                , #{item.componentConfigCode,jdbcType=VARCHAR}
                , #{item.contentType,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_PAGE_CONTENT_CHECK-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-PAGE-CONTENT-CHECK-DELETEBYID*/ FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_PAGE_CONTENT_CHECK-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-CONTENT-CHECK-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <!--根据普通索引IdxPageVersionComponent获取数据:MSHOP_PAGE_CONTENT_CHECK-->
    <select id="queryByIdxPageVersionComponent" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-PAGE-CONTENT-CHECK-QUERYBYIDXPAGEVERSIONCOMPONENT*/  <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
        <![CDATA[
            PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            AND VERSION         = #{version,jdbcType=VARCHAR}
            AND COMPONENT_CODE  = #{componentCode,jdbcType=VARCHAR}
        ]]>
    </select>

    <select id="queryByPage" resultMap="BaseResultMap">
        SELECT   <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
        page_code = #{pageCode}
        AND  version  =  #{version}
        AND  delete_flag = 0
    </select>

    <update id="updateByDataId" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo">
        update MSHOP_PAGE_CONTENT_CHECK
        <set>
            <if test="checkStatus != null and checkStatus != ''">
                check_status = #{checkStatus},
            </if>
            <if test="checkResult != null and checkResult != ''">
                check_result = #{checkResult}
            </if>
        </set>
        where data_id = #{dataId}
    </update>

    <select id="queryByComponentCodes" resultMap="BaseResultMap">
        SELECT   <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE page_code = #{pageCode}
        AND component_config_code in
        <foreach collection="componentDetailCodes" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND version = #{version}
        AND delete_flag = 0
    </select>


    <select id="queryAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE id &gt;= #{startNum}
        and check_status = #{checkStatus}
        AND delete_flag = 0
        order by id ASC
        limit #{pickCount}
    </select>
</mapper>
