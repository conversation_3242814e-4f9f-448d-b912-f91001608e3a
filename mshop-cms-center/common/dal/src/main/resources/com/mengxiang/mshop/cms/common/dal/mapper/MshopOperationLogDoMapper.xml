<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopOperationLogDoMapper">
    <!-- 自动生成,请修改 MSHOP_OPERATION_LOG.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopOperationLogDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="ACTION" property="action" jdbcType="VARCHAR" javaType="String"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR" javaType="String"/>
        <result column="BIZ_CODE" property="bizCode" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="AFTER_DATA" property="afterData" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="BEFORE_DATA" property="beforeData" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="CREATE_USER_ID" property="createUserId" jdbcType="VARCHAR" javaType="String"/>
        <result column="BIZ_TYPE" property="bizType" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="OWNER_TYPE" property="ownerType" jdbcType="VARCHAR" javaType="String"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.ACTION,sf.REMARK,sf.BIZ_CODE,sf.CREATE_BY
        ,sf.AFTER_DATA,sf.BEFORE_DATA,sf.CREATE_USER_ID,sf.BIZ_TYPE,sf.CREATE_TIME.sf.OWNER_TYPE
    </sql>
    <sql id="Base_Column_List">
        ID,ACTION,REMARK,BIZ_CODE,CREATE_BY 
        ,AFTER_DATA,BEFORE_DATA,CREATE_USER_ID,BIZ_TYPE,CREATE_TIME,OWNER_TYPE
    </sql>


    <!--插入表:MSHOP_OPERATION_LOG-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_OPERATION_LOG(
            ID
            ,ACTION
            ,REMARK
            ,BIZ_CODE
            ,CREATE_BY
            ,AFTER_DATA
            ,BEFORE_DATA
            ,CREATE_USER_ID
            ,BIZ_TYPE
            ,OWNER_TYPE
        )VALUES(
             null
            , #{action,jdbcType=VARCHAR}
            , #{remark,jdbcType=VARCHAR}
            , #{bizCode,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{afterData,jdbcType=LONGVARCHAR}
            , #{beforeData,jdbcType=LONGVARCHAR}
            , #{createUserId,jdbcType=VARCHAR}
            , #{bizType,jdbcType=INTEGER}
            , #{ownerType,jdbcType=VARCHAR}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_OPERATION_LOG-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_OPERATION_LOG(
            ID
            ,ACTION
            ,REMARK
            ,BIZ_CODE
            ,CREATE_BY
            ,AFTER_DATA
            ,BEFORE_DATA
            ,CREATE_USER_ID
            ,BIZ_TYPE
            ,OWNER_TYPE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.action,jdbcType=VARCHAR}
                , #{item.remark,jdbcType=VARCHAR}
                , #{item.bizCode,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.afterData,jdbcType=LONGVARCHAR}
                , #{item.beforeData,jdbcType=LONGVARCHAR}
                , #{item.createUserId,jdbcType=VARCHAR}
                , #{item.bizType,jdbcType=INTEGER}
                , #{ownerType,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_OPERATION_LOG-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-OPERATION-LOG-DELETEBYID*/ FROM MSHOP_OPERATION_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_OPERATION_LOG-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-OPERATION-LOG-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_OPERATION_LOG
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <!-- 根据业务code和类型查询数据 -->
    <select id="queryListByBizCode" resultMap="BaseResultMap" >
        SELECT *
        FROM MSHOP_OPERATION_LOG
        WHERE
            BIZ_CODE = #{bizCode,jdbcType=VARCHAR}
          AND BIZ_TYPE = #{bizType,jdbcType=INTEGER}
        <if test="action != null  and action !=''">
            and action = #{action,jdbcType=VARCHAR}
        </if>
        order by id desc
            limit #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 根据code查询总数 -->
    <select id="countByBizCode" resultType="int" >
        SELECT COUNT(*)
        FROM MSHOP_OPERATION_LOG
        WHERE
            BIZ_CODE = #{bizCode,jdbcType=VARCHAR}
          AND BIZ_TYPE = #{bizType,jdbcType=INTEGER}
        <if test="action != null  and action !=''">
          and  action = #{action,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
