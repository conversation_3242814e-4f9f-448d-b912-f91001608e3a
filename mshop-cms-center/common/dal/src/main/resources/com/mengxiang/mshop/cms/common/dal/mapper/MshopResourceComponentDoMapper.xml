<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.MshopResourceComponentDoMapper">
    <!-- 自动生成,请修改 MSHOP_RESOURCE_COMPONENT.xml -->
    <resultMap id="BaseResultMap"  type="com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>
        <result column="NAME" property="name" jdbcType="VARCHAR" javaType="String"/>
        <result column="CHANNEL" property="channel" jdbcType="VARCHAR" javaType="String"/>
        <result column="OWNER_ID" property="ownerId" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" javaType="String"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" javaType="String"/>
        <result column="OWNER_TYPE" property="ownerType" jdbcType="VARCHAR" javaType="String"/>
        <result column="CATEGORY_ID" property="categoryId" jdbcType="VARCHAR" javaType="String"/>
        <result column="TIME_CONFIG" property="timeConfig" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="CONFIG_DETAIL" property="configDetail" jdbcType="LONGVARCHAR" javaType="String"/>
        <result column="RESOURCE_TYPE" property="resourceType" jdbcType="VARCHAR" javaType="String"/>
        <result column="RESOURCE_PAGE_TYPE" property="resourcePageType" jdbcType="VARCHAR" javaType="String"/>
        <result column="STATUS" property="status" jdbcType="TINYINT" javaType="Integer"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <!-- 新增 order_value 字段映射 -->
        <result column="ORDER_VALUE" property="orderValue" jdbcType="INTEGER" javaType="Integer"/>
        <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR" javaType="String"/>
    </resultMap>


    <sql id="Base_SF_Column_List">
        sf.ID,sf.NAME,sf.CHANNEL,sf.OWNER_ID,sf.CREATE_BY
        ,sf.TENANT_ID,sf.UPDATE_BY,sf.OWNER_TYPE,sf.CATEGORY_ID,sf.TIME_CONFIG
        ,sf.CONFIG_DETAIL,sf.RESOURCE_TYPE,sf.RESOURCE_PAGE_TYPE,sf.STATUS,sf.DELETE_FLAG
        ,sf.CREATE_TIME,sf.UPDATE_TIME,sf.ORDER_VALUE
    </sql>
    <sql id="Base_Column_List">
        ID,NAME,CHANNEL,OWNER_ID,CREATE_BY
        ,TENANT_ID,UPDATE_BY,OWNER_TYPE,CATEGORY_ID,TIME_CONFIG
        ,CONFIG_DETAIL,RESOURCE_TYPE,RESOURCE_PAGE_TYPE,STATUS,DELETE_FLAG
        ,CREATE_TIME,UPDATE_TIME,ORDER_VALUE
    </sql>


    <!--插入表:MSHOP_RESOURCE_COMPONENT-->
    <insert id="insert" >
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_RESOURCE_COMPONENT(
            ID
            ,NAME
            ,CHANNEL
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,CATEGORY_ID
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,RESOURCE_TYPE
            ,RESOURCE_PAGE_TYPE
            ,STATUS
            ,ORDER_VALUE
        )VALUES(
             null
            , #{name,jdbcType=VARCHAR}
            , #{channel,jdbcType=VARCHAR}
            , #{ownerId,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{tenantId,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{ownerType,jdbcType=VARCHAR}
            , #{categoryId,jdbcType=VARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{configDetail,jdbcType=LONGVARCHAR}
            , #{resourceType,jdbcType=VARCHAR}
            , #{resourcePageType,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{orderValue,jdbcType=INTEGER}
        )
        ]]>
    </insert>

    <!--批量插入表:MSHOP_RESOURCE_COMPONENT-->
    <insert id="insertBatch" >
        INSERT INTO MSHOP_RESOURCE_COMPONENT(
            ID
            ,NAME
            ,CHANNEL
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,CATEGORY_ID
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,RESOURCE_TYPE
            ,RESOURCE_PAGE_TYPE
            ,STATUS
            ,DELETE_FLAG
            ,CREATE_TIME
            ,UPDATE_TIME
            ,ORDER_VALUE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
                 null
                , #{item.name,jdbcType=VARCHAR}
                , #{item.channel,jdbcType=VARCHAR}
                , #{item.ownerId,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.tenantId,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.ownerType,jdbcType=VARCHAR}
                , #{item.categoryId,jdbcType=VARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.configDetail,jdbcType=LONGVARCHAR}
                , #{item.resourceType,jdbcType=VARCHAR}
                , #{item.resourcePageType,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.deleteFlag,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
                , #{item.orderValue,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!--根据主键删除数据:MSHOP_RESOURCE_COMPONENT-->
    <delete id="deleteById" >
        <![CDATA[
        DELETE /*MS-MSHOP-RESOURCE-COMPONENT-DELETEBYID*/ FROM MSHOP_RESOURCE_COMPONENT
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

    <!--根据主键获取数据:MSHOP_RESOURCE_COMPONENT-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-RESOURCE-COMPONENT-GETBYID*/  <include refid="Base_Column_List" />
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </select>

    <!--根据普通索引IdxResourceType获取数据:MSHOP_RESOURCE_COMPONENT-->
    <select id="queryByIdxResourceType" resultMap="BaseResultMap">
        SELECT /*MS-MSHOP-RESOURCE-COMPONENT-QUERYBYIDXRESOURCETYPE*/  <include refid="Base_Column_List" />
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE
        <![CDATA[
            RESOURCE_TYPE   = #{resourceType,jdbcType=VARCHAR}
            AND TENANT_ID       = #{tenantId,jdbcType=VARCHAR}
            AND OWNER_TYPE      = #{ownerType,jdbcType=VARCHAR}
            AND OWNER_ID        = #{ownerId,jdbcType=VARCHAR}
        ]]>
    </select>

    <!--根据条件查询数据:CREDIT_EVENT-->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE delete_flag = 0

        <if test="pageQueryVO.resourceTypeList != null">
            AND resource_type  in
            <foreach collection="pageQueryVO.resourceTypeList" separator="," open="(" item="type" index="index" close=")">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pageQueryVO.resourceType != null and pageQueryVO.resourceType != ''">
            AND resource_type  = #{pageQueryVO.resourceType,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.resourceChannel != null and pageQueryVO.resourceChannel != ''">
            and FIND_IN_SET(#{pageQueryVO.resourceChannel,jdbcType=VARCHAR},channel)
        </if>
        <if test="pageQueryVO.tenantId != null and pageQueryVO.tenantId != ''">
            AND tenant_id = #{pageQueryVO.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.status != null ">
            AND status = #{pageQueryVO.status,jdbcType=TINYINT}
        </if>
        <if test="pageQueryVO.resourcePageType != null ">
            AND resource_page_type = #{pageQueryVO.resourcePageType,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.name != null and pageQueryVO.name != ''">
            and name like CONCAT('%',#{pageQueryVO.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="pageQueryVO.ownerType != null and pageQueryVO.ownerType != ''">
            AND owner_type = #{pageQueryVO.ownerType,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.ownerId != null and pageQueryVO.ownerId != ''">
            AND owner_id = #{pageQueryVO.ownerId,jdbcType=VARCHAR}
        </if>
        <if test="pageQueryVO.categoryId != null and pageQueryVO.categoryId != ''">
            AND category_id = #{pageQueryVO.categoryId,jdbcType=VARCHAR}
        </if>
        order by CASE WHEN STATUS = 4 THEN 0 ELSE 1 END ,update_time desc,id desc
    </select>

    <update id="updateById">
        UPDATE MSHOP_RESOURCE_COMPONENT
        <trim prefix="set" suffixOverrides=",">
            <if test="configDetail!=null">
                CONFIG_DETAIL  = #{configDetail,jdbcType=VARCHAR},
            </if>
            <if test="configDetail!=null">
                CONFIG_DETAIL  = #{configDetail,jdbcType=VARCHAR},
            </if>
            <if test="timeConfig!=null">
                time_config  = #{timeConfig,jdbcType=VARCHAR},
            </if>
            <if test="name!=null">
                name  = #{name,jdbcType=VARCHAR},
            </if>
            <if test="channel!=null">
                channel  = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="resourcePageType != null ">
                resource_page_type = #{resourcePageType,jdbcType=VARCHAR},
            </if>
            <if test="status!=null">
                status  = #{status,jdbcType=TINYINT},
            </if>

        </trim>
        WHERE   ID   = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateStatusById">
        UPDATE MSHOP_RESOURCE_COMPONENT
        <trim prefix="set" suffixOverrides=",">
            <if test="status!=null">
                status  = #{status,jdbcType=TINYINT},
            </if>
            <if test="deleteFlag!=null">
                DELETE_FLAG  = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="timeConfig!=null">
                time_config  = #{timeConfig,jdbcType=VARCHAR},
            </if>
        </trim>
        WHERE   ID   = #{id,jdbcType=BIGINT}
    </update>

    <!--根据条件查询数据:CREDIT_EVENT-->
    <select id="selectListByStatus" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE delete_flag = 0
            AND status  in
            <foreach collection="statusList" separator="," open="(" item="status" index="index" close=")">
                #{status,jdbcType=TINYINT}
            </foreach>
        and  time_config -> '$.effectiveType'=2
        order by id desc
    </select>

    <select id="getPlatPageDataByCategoryIds"
            resultType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo">
        SELECT <include refid="Base_Column_List" />
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE category_id in
        <foreach collection="categoryIds" item="categoryId" separator="," open="(" close=")">
            #{categoryId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getPlatPageDataByTenantInfo" resultType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo">
        SELECT <include refid="Base_Column_List" />
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND owner_id = #{ownerId,jdbcType=VARCHAR}
        AND owner_type = #{ownerType,jdbcType=VARCHAR}
        AND resource_type = #{resourceType,jdbcType=VARCHAR}
    </select>

    <update id="updateStatusByTenantId" parameterType="com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo">
        UPDATE MSHOP_RESOURCE_COMPONENT
        SET config_detail = #{configDetail}
        WHERE tenant_id = #{tenantId}
          AND owner_id = #{ownerId}
          AND owner_type = #{ownerType}
          AND resource_type = #{resourceType}
    </update>
</mapper>
