<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mengxiang.mshop.cms.common.dal.mapper.DbPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
        SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
        FROM (
        SELECT
            COUNT(*)= 11 as fg,'MSHOP_OPERATION_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTION,REMARK,BIZ_CODE,CREATE_BY,AFTER_DATA,OWNER_TYPE,BEFORE_DATA,CREATE_USER_ID,BIZ_TYPE,CREATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_OPERATION_LOG'
            AND COLUMN_NAME in('ID','ACTION','REMARK','BIZ_CODE','CREATE_BY','AFTER_DATA','OWNER_TYPE','BEFORE_DATA','CREATE_USER_ID','BIZ_TYPE','CREATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 14 as fg,'MSHOP_COMPONENT_INSTANCE_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,VERSION,PAGE_CODE,TIME_CONFIG,CONFIG_DETAIL,COMPONENT_CODE,COMPONENT_TYPE,DIRECT_USER_GROUP,CONFIG_DETAIL_CODE,COMPONENT_DETAIL_TYPE,DELETE_FLAG,ORDER_VALUE,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_COMPONENT_INSTANCE_DETAIL'
            AND COLUMN_NAME in('ID','VERSION','PAGE_CODE','TIME_CONFIG','CONFIG_DETAIL','COMPONENT_CODE','COMPONENT_TYPE','DIRECT_USER_GROUP','CONFIG_DETAIL_CODE','COMPONENT_DETAIL_TYPE','DELETE_FLAG','ORDER_VALUE','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 17 as fg,'MSHOP_PAGE_INSTANCE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,TYPE,CHANNEL,OWNER_ID,VERSION,CREATE_BY,PAGE_CODE,TENANT_ID,UPDATE_BY,OWNER_TYPE,MARKET_TYPE,CREATE_USER_ID,TEMPLATE_CODE,STATUS,DELETE_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_PAGE_INSTANCE'
            AND COLUMN_NAME in('ID','TYPE','CHANNEL','OWNER_ID','VERSION','CREATE_BY','PAGE_CODE','TENANT_ID','UPDATE_BY','OWNER_TYPE','MARKET_TYPE','CREATE_USER_ID','TEMPLATE_CODE','STATUS','DELETE_FLAG','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 16 as fg,'TRANSACTION_TASK_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,STATUS,TASK_ID,TASK_TYPE,ERROR_CODE,RETRY_STATUS,ERROR_MESSAGE,TASK_CLASS_NAME,REVERSAL_STATUS,TRANSACTION_TYPE,RESULT_ADDITIONAL_INFO,REQUEST_ADDITIONAL_INFO,TIMES,CREATE_TIME,UPDATE_TIME,NEXT_EXECUTE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'TRANSACTION_TASK_LOG'
            AND COLUMN_NAME in('ID','STATUS','TASK_ID','TASK_TYPE','ERROR_CODE','RETRY_STATUS','ERROR_MESSAGE','TASK_CLASS_NAME','REVERSAL_STATUS','TRANSACTION_TYPE','RESULT_ADDITIONAL_INFO','REQUEST_ADDITIONAL_INFO','TIMES','CREATE_TIME','UPDATE_TIME','NEXT_EXECUTE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 21 as fg,'MSHOP_PAGE_DRAFT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,TITLE,VERSION,CREATE_BY,PAGE_CODE,SUB_TITLE,UPDATE_BY,TIME_CONFIG,SHARE_CONFIG,CREATE_USER_ID,BACKGROUND_COLOR,BACKGROUND_IMG_URL,PRIVATE_MARKET_CONFIG,STATUS,BIZ_TYPE,SEARCH_BOX,DELETE_FLAG,SEARCH_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_PAGE_DRAFT'
            AND COLUMN_NAME in('ID','NAME','TITLE','VERSION','CREATE_BY','PAGE_CODE','SUB_TITLE','UPDATE_BY','TIME_CONFIG','SHARE_CONFIG','CREATE_USER_ID','BACKGROUND_COLOR','BACKGROUND_IMG_URL','PRIVATE_MARKET_CONFIG','STATUS','BIZ_TYPE','SEARCH_BOX','DELETE_FLAG','SEARCH_FLAG','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 19 as fg,'MSHOP_RESOURCE_COMPONENT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,CHANNEL,OWNER_ID,CREATE_BY,TENANT_ID,UPDATE_BY,OWNER_TYPE,CATEGORY_ID,SOURCE_TYPE,TIME_CONFIG,CONFIG_DETAIL,RESOURCE_TYPE,RESOURCE_PAGE_TYPE,STATUS,DELETE_FLAG,ORDER_VALUE,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_RESOURCE_COMPONENT'
            AND COLUMN_NAME in('ID','NAME','CHANNEL','OWNER_ID','CREATE_BY','TENANT_ID','UPDATE_BY','OWNER_TYPE','CATEGORY_ID','SOURCE_TYPE','TIME_CONFIG','CONFIG_DETAIL','RESOURCE_TYPE','RESOURCE_PAGE_TYPE','STATUS','DELETE_FLAG','ORDER_VALUE','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 13 as fg,'MSHOP_PAGE_CONTENT_CHECK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,DATA_ID,CONTENT,VERSION,PAGE_CODE,CHECK_RESULT,CHECK_STATUS,COMPONENT_CODE,COMPONENT_CONFIG_CODE,DELETE_FLAG,CONTENT_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_PAGE_CONTENT_CHECK'
            AND COLUMN_NAME in('ID','DATA_ID','CONTENT','VERSION','PAGE_CODE','CHECK_RESULT','CHECK_STATUS','COMPONENT_CODE','COMPONENT_CONFIG_CODE','DELETE_FLAG','CONTENT_TYPE','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 18 as fg,'MSHOP_PAGE_TEMPLATE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,OWNER_ID,CREATE_BY,TENANT_ID,UPDATE_BY,OWNER_TYPE,PAGE_USE_RULE,PAGE_USE_TYPE,USE_CHANNELS,TEMPLATE_CODE,TEMPLATE_DESC,TEMPLATE_NAME,COMPONENT_USE_RULE,TEMPLATE_IMAGE_URL,STATUS,DELETE_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_PAGE_TEMPLATE'
            AND COLUMN_NAME in('ID','OWNER_ID','CREATE_BY','TENANT_ID','UPDATE_BY','OWNER_TYPE','PAGE_USE_RULE','PAGE_USE_TYPE','USE_CHANNELS','TEMPLATE_CODE','TEMPLATE_DESC','TEMPLATE_NAME','COMPONENT_USE_RULE','TEMPLATE_IMAGE_URL','STATUS','DELETE_FLAG','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
    UNION ALL
        SELECT
            COUNT(*)= 13 as fg,'MSHOP_COMPONENT_INSTANCE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,TYPE,USE_RULE,VERSION,PAGE_CODE,META_CONFIG,TIME_CONFIG,COMPONENT_CODE,DIRECT_USER_GROUP,DELETE_FLAG,ORDER_VALUE,CREATE_TIME,UPDATE_TIME' exp_columns
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = 'MSHOP_COMPONENT_INSTANCE'
            AND COLUMN_NAME in('ID','TYPE','USE_RULE','VERSION','PAGE_CODE','META_CONFIG','TIME_CONFIG','COMPONENT_CODE','DIRECT_USER_GROUP','DELETE_FLAG','ORDER_VALUE','CREATE_TIME','UPDATE_TIME')
        GROUP BY TABLE_NAME
        )a
        WHERE fg=0
    </select>
</mapper>
