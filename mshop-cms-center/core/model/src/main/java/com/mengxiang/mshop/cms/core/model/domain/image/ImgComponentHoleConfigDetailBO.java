package com.mengxiang.mshop.cms.core.model.domain.image;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2024/12/12
 * @Description:
 */
@Data
public class ImgComponentHoleConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.HOLE.getCode();
    @ApiModelProperty(value = "目标类型：导师二维码 MENTORQR")
    private String targetType;
    @ApiModelProperty(value = "区域x点(相对左上角)")
    private Double x;
    @ApiModelProperty(value = "区域y点(相对左上角)")
    private Double y;
    @ApiModelProperty(value = "宽度")
    private Double width;
    @ApiModelProperty(value = "高度")
    private Double height;
}
