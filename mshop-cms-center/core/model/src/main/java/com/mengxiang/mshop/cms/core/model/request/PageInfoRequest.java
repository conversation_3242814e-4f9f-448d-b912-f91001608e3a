package com.mengxiang.mshop.cms.core.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * <AUTHOR>
 */
@ApiModel("页面查询支持用户分群")
@Data
public class PageInfoRequest {

    @ApiModelProperty(value = "页面ownerId")
    private String ownerId;

    @ApiModelProperty(value = "页面类型")
    private String pageType;

    @ApiModelProperty(value = "页面code")
    private String pageCode;

    @ApiModelProperty(value = "页面version")
    private String version;

    @ApiModelProperty(value = "聚合用户登录信息")
    private AggrBaseReqModule aggrBaseReqModule;

}
