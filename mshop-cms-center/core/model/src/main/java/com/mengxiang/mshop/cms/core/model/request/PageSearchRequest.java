package com.mengxiang.mshop.cms.core.model.request;

import com.mengxiang.base.common.model.request.PagingRequest;
import com.mengxiang.mshop.cms.core.model.enums.MarketTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 页面分页条件查询对象
 * <AUTHOR>
 */
@Data
public class PageSearchRequest extends PagingRequest {

    @ApiModelProperty("页面编号")
    private List<String> pageCodeList;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("页面标题")
    private String title;

    @ApiModelProperty("租户id.")
    private String tenantId;

    @ApiModelProperty("属者ID")
    private String ownerId;

    @ApiModelProperty("属者类型（tenant:租户 shop:店铺 mengxiang:饷店）.")
    private String ownerType;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("页面副标题")
    private String subTitle;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("PageInstanceStatusEnum 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.")
    private Integer status;

    @ApiModelProperty("端：h5、app 、小程序")
    private String channel;

    @ApiModelProperty("创建时间-开始")
    private Date createTimeStart;

    @ApiModelProperty("创建时间-结束")
    private Date createTimeEnd;

    @ApiModelProperty("修改时间-开始")
    private Date updateTimeStart;

    @ApiModelProperty("修改时间-结束")
    private Date updateTimeEnd;

    @ApiModelProperty(" type 类型：首页、商品详情页、会场、微页面.")
    private String type;

    @ApiModelProperty(" type 类型：首页、商品详情页、会场、微页面.")
    private List<String> typeList;

    @ApiModelProperty("属者ID列表")
    private List<String> ownerIdList;

    @ApiModelProperty("会场类型 不传默认普通会场")
    private MarketTypeEnum marketType=MarketTypeEnum.NORMAL;
}
