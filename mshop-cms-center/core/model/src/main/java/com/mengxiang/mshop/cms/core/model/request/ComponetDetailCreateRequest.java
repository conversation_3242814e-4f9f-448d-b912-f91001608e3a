package com.mengxiang.mshop.cms.core.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 组件详情创建
 */
@Data
public class ComponetDetailCreateRequest {

    @ApiModelProperty("页面编号")
    private String pageCode;
    
    @ApiModelProperty("页面编号")
    private String componentCode;
    
    @ApiModelProperty("组件类型")
    private String componentType;
    
    @ApiModelProperty("创建人")
    private String createBy;
}
