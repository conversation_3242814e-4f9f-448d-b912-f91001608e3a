package com.mengxiang.mshop.cms.core.model.domain.toplist;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description:
 */
@Data
public class TopListComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.TOPLIST.getCode();

    @ApiModelProperty(value = "默认展示商品数")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "查看更多商品数")
    private Integer morePageSize = 10;

    @ApiModelProperty(value = "榜单名称")
    private String topName;

    @ApiModelProperty(value = "榜单类型 全部:ALL 佣金: PROFIT 销售: SALE 转发: FORWARDINGAMOUNT ")
    private String topType;
}
