package com.mengxiang.mshop.cms.core.model.enums;

/**
 * <AUTHOR>
 * @Date: 2023/3/24
 * @Description:
 */
public enum DataRuleTypeEnum {
    /**
     * 自主选品
     */
    SELF("SELF", "自主选品"),
    /**
     * 自主选品
     */
    CONDITION("CO<PERSON>IT<PERSON>", "多条件"),
    /**
     * 规则选品
     */
    RULE("RULE", "规则选品");

    private String code;
    private String desc;


    DataRuleTypeEnum(String code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static DataRuleTypeEnum getEnumByCode(String code) {
        for (DataRuleTypeEnum item : DataRuleTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
