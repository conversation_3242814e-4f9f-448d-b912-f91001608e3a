package com.mengxiang.mshop.cms.core.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AggrBaseReqModule {

    @ApiModelProperty(value = "店主子等级")
    private String userSubLevel;

    @ApiModelProperty("渠道涞源")
    private String channel;

    @ApiModelProperty("店长")
    private Long distributorId;

    @ApiModelProperty("当前角色")
    private Integer currentRoleType;

    @ApiModelProperty("等级")
    private Integer level;

    @ApiModelProperty("爱豆编号")
    private String reSellerId;

    @ApiModelProperty("用户ID")
    private Long userId;
}
