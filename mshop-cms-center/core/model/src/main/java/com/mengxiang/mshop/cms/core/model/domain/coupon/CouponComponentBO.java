package com.mengxiang.mshop.cms.core.model.domain.coupon;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description: 优惠券组件
 */
@Data
public class CouponComponentBO extends ComponentBO {

   @ApiModelProperty(value = "布局方式 11: 一行一 10:一行多 20:二行多")
   private String layoutType;

   @ApiModelProperty(value = "带图样式  top 、left")
   private String imgStyle;

   @ApiModelProperty(value = "背景色")
   private String backgroundColor;

   @ApiModelProperty(value = "背景图")
   private String backgroundImgUrl;

   @ApiModelProperty(value = "背景图 0:无图 1:有图")
   private Integer hasBackgroundImg;

   @ApiModelProperty(value = "生效方式")
   private TimeConfigBO timeConfig;

   @ApiModelProperty(value = "优惠券配置")
   @IgnorePropertyJson
   private List<CouponComponentConfigDetailBO> couponConfigDetails;
}
