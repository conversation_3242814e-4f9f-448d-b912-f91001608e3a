package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 链接跳转类型枚举
 * <AUTHOR>
 */
public enum LinkTypeEnum {
    /**
     * 链接跳转类型
     * 1=页面
     */
    PAGE(1,"PAGE", "页面"),
    /**
     * 链接跳转类型
     * 2=链接
     */
    LINK(2,"LINK", "链接"),
    /**
     * 链接跳转类型
     * 3=商品
     */
    PRODUCT(3,"PRODUCT", "商品"),
    /**
     * 链接跳转类型
     * 4=档期
     */
    ACTIVITY(4,"ACTIVITY", "档期"),

    ;


    private Integer code;
    private String name;

    private String desc;

    LinkTypeEnum(Integer code , String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }
    
    public static LinkTypeEnum getEnumByName(String name) {
        for (LinkTypeEnum item : LinkTypeEnum.values()) {
            if (item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }
    
    public String getName() {
        return name;
    }
    
}
