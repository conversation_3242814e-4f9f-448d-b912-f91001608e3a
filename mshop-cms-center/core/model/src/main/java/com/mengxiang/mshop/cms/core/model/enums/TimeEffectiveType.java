package com.mengxiang.mshop.cms.core.model.enums;



/**
 * 时间生效方式
 * <AUTHOR>
 */
public enum TimeEffectiveType {

    /**
     * 立即生效
     */
    RIGHT_NOW(1, "RIGHT_NOW", "立即生效"),

    /**
     * 定时生效
     */
    REGULAR_TIME(2, "REGULAR_TIME", "定时生效");
    
    private Integer code;
    
    private String name;
    
    private String desc;
    
    TimeEffectiveType(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
    
    public static TimeEffectiveType getEnumByCode(Integer code) {
        for (TimeEffectiveType item : TimeEffectiveType.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDesc() {
        return desc;
    }
}
