package com.mengxiang.mshop.cms.core.model.request;

import com.mengxiang.base.common.model.request.BaseRequest;
import com.mengxiang.mshop.cms.core.model.domain.base.PageShareConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.MarketTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 */
@Data
public class SavePageRequest extends BaseRequest {

    @ApiModelProperty("操作类型 1: 保存和预览 2: 保存和发布")
    private Integer operateType;

    @ApiModelProperty("模版编号")
    private String templateCode;

    @ApiModelProperty("页面编号")
    private String pageCode;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("页面标题")
    private String title;

    @ApiModelProperty("页面子标题")
    private String subTitle;

    @ApiModelProperty("页面背景颜色")
    private String backgroundColor;

    @ApiModelProperty("页面背景图")
    private String backgroundImgUrl;

    @ApiModelProperty("业务类型 1:代发 2:批发")
    private Integer bizType;

    @ApiModelProperty("搜索功能 0:关闭 1:开启")
    private Integer searchFlag = 1;

    @ApiModelProperty("搜索框 0:收起 1:展开")
    private Integer searchBox = 1;

    @ApiModelProperty("所属者ID")
    private String ownerId;

    @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店）")
    private String ownerType;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("端：h5、app 、小程序")
    private String channel;

    @ApiModelProperty("首页: HOME  店铺主页:SHOP 会场:MARKET 分类页:CATEGORY 微页面:MICRO 积分会场：POINTS")
    private String type;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("生效方式")
    private TimeConfigBO timeConfig;

    @ApiModelProperty("页面分享信息")
    private PageShareConfigBO shareConfig;

    @ApiModelProperty("组件列表JSON")
    private String components;

    @ApiModelProperty("直接上级工号")
    private String managerNoDirect;

    @ApiModelProperty("用户编号")
    private String createUserCode;

    @ApiModelProperty("审批人")
    private List<String> auditList;

    private String merchantCode;

    private String shopName;

    /**
     * 类目长
     */
    private List<String> categoryLeaderList;

    @ApiModelProperty("私密会场配置 （会场类型为私密会场时有效）")
    private PrivateMarketConfigBO privateMarketConfig;

    @ApiModelProperty("会场类型 （不传默认普通会场）")
    private MarketTypeEnum marketType;

}
