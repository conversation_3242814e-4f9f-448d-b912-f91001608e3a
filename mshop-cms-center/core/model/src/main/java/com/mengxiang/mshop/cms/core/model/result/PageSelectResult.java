package com.mengxiang.mshop.cms.core.model.result;

import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 分页查询返回页面信息
 */
@Data
public class PageSelectResult {

    @ApiModelProperty("页面编号")
    private String pageCode;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("页面标题")
    private String title;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("页面副标题")
    private String subTitle;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.")
    private Integer status;

    @ApiModelProperty("页面发布失败原因")
    private String failMessage;

    @ApiModelProperty("业务类型 1=代发 2=批发.")
    private Integer bizType;

    @ApiModelProperty("是否删除 0 否 1是.")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("是否有待发布版本 0 否 1是.")
    private Integer publishFlag;

    @ApiModelProperty("页面类型PageType")
    private String pageType;

    @ApiModelProperty("支持转发")
    private Integer shareSwitch;
    @ApiModelProperty("转发标题")
    private String forwardTitle;
    @ApiModelProperty("转发副标题")
    private String forwardSubTitle;
    @ApiModelProperty("分享卡片图片")
    private String shareCardImg;

    @ApiModelProperty("生效配置")
    private TimeConfigBO timeConfig;


    @ApiModelProperty("所属者ID")
    private String ownerId;


    @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.")
    private String ownerType;
}
