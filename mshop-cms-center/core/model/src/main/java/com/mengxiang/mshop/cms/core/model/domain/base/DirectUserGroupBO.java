package com.mengxiang.mshop.cms.core.model.domain.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/5/9
 * @Description: 定向分群
 */
@Data
public class DirectUserGroupBO {

    @ApiModelProperty("定向用户展示类型[0:全部 1:用户类型 2:用户分群]")
    private Integer directShowType;

    @ApiModelProperty("用户分群(分群以逗号进行分隔)")
    private String userGroups;

    @ApiModelProperty("新用户类型[1:普通用户 2 ：店主 3：店长]字符串逗号分隔")
    private String newUserType;

    @ApiModelProperty("用户等级(1-0,1-1,1-2,1-3,2~6)，多个以逗号分割")
    private String userLevels;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        DirectUserGroupBO other = (DirectUserGroupBO) obj;

        // 检查属性值是否一致
        if (!Objects.equals(this.directShowType,other.directShowType) ||
                !isFieldConsistent(this.userGroups, other.userGroups) ||
                !isFieldConsistent(this.newUserType, other.newUserType) ||
                !isFieldConsistent(this.userLevels, other.userLevels)) {
            return false;
        }

        // 拆分属性值为数组
        String[] thisUserGroups = StringUtils.isNotBlank(this.userGroups) ? this.userGroups.split(",") : new String[0];
        String[] otherUserGroups = StringUtils.isNotBlank(other.userGroups) ? other.userGroups.split(",") : new String[0];
        String[] thisNewUserTypes = StringUtils.isNotBlank(this.newUserType) ? this.newUserType.split(",") : new String[0];
        String[] otherNewUserTypes = StringUtils.isNotBlank(other.newUserType) ? other.newUserType.split(",") : new String[0];
        String[] thisUserLevels = StringUtils.isNotBlank(this.userLevels) ? this.userLevels.split(",") : new String[0];
        String[] otherUserLevels = StringUtils.isNotBlank(other.userLevels) ? other.userLevels.split(",") : new String[0];

        // 比较数组的内容是否包含
        return  containsAllNonNullElements(thisUserGroups, otherUserGroups) &&
                containsAllNonNullElements(thisNewUserTypes, otherNewUserTypes) &&
                containsAllNonNullElements(thisUserLevels, otherUserLevels);
    }

    private boolean isFieldConsistent(String thisField, String otherField) {
        return (StringUtils.isBlank(thisField) && StringUtils.isBlank(otherField)) ||
                (StringUtils.isNotBlank(thisField) && StringUtils.isNotBlank(otherField));
    }

    private boolean containsAllNonNullElements(String[] arr1, String[] arr2) {
        for (String str : arr2) {
            if (StringUtils.isNotBlank(str) && !containsIgnoreCase(arr1, str)) {
                return false;
            }
        }
        return true;
    }

    private boolean containsIgnoreCase(String[] arr, String element) {
        for (String str : arr) {
            if (StringUtils.equalsIgnoreCase(str, element)) {
                return true;
            }
        }
        return false;
    }
}
