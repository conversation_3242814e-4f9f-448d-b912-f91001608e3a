package com.mengxiang.mshop.cms.core.model.request.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("page内容检测")
public class PageContentRequest {

    @ApiModelProperty(notes = "组件code", required = true)
    private String componentCode;
    
    @ApiModelProperty(notes = "组件详情code", required = true)
    private String componentDetailCode;
    
    @ApiModelProperty(notes = "视频url", required = true)
    private String url;
}
