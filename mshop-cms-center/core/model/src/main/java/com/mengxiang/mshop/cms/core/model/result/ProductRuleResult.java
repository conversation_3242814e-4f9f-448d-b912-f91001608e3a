package com.mengxiang.mshop.cms.core.model.result;

import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
public class ProductRuleResult {
    private String ruleCode;

    /**
     * 关联id souceType=1传 普通会场频道的channelId souceType=2传 超品会场频道的hotSaleChannelDetailId
     */
    private Integer sourceId;

    /**
     * 关联类型 1=普通会场频道 2=超级会场频道
     */
    private Integer sourceType;

    /**
     * 选品中心的规则id
     */
    private String originalRuleCode;

    /**
     * 选品中心的规则名称
     */
    private String originalRuleName;

    private String ruleName;
    /**
     * 规则状态 0=未生效 1=已生效 2=已失效
     */
    private Byte ruleStatus;

    /**
     * 规则类型 1=人工选品 2=规则选品
     */
    private Integer ruleType;

    private Byte sortType;

    private String creator;

    private Date createTime;

    @ApiModelProperty("生效方式")
    private TimeConfigBO timeConfig;

    private RuleFilterResult filterResult;
}
