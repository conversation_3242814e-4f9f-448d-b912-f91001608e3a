package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 定时类型
 * <AUTHOR>
 */
public enum TimeType {

    /**
     * 多时段
     */
    MULTIPLE(1, "MULTIPLE", "多时段"),

    /**
     * 循环时段
     */
    CIRCLE(2, "CIRCL<PERSON>", "循环时段");
    
    private Integer code;
    
    private String name;
    
    private String desc;
    
    TimeType(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
    
    public static TimeType getEnumByCode(Integer code) {
        for (TimeType item : TimeType.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDesc() {
        return desc;
    }
}