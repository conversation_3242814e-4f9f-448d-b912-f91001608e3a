package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 组件类型枚举
 * <AUTHOR>
 */
public enum ResourcePageTypeEnum {

    /**
     首页：HOME
     */
    HOME("HOME", "首页"),
    /**
     个人中心：PERSONALCENTER
     */
    PERSONALCENTER("PERSONALCENTER", "个人中心"),
    ;


    private String code;
    private String desc;


    ResourcePageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getEnumNameByCode(String code) {
        for (ResourcePageTypeEnum item : ResourcePageTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
