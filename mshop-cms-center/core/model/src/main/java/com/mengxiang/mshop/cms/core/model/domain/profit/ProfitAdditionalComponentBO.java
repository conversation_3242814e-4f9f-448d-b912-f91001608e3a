package com.mengxiang.mshop.cms.core.model.domain.profit;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description:
 */
@Data
public class ProfitAdditionalComponentBO extends ComponentBO {

    @ApiModelProperty(value = "默认展示商品数")
    private Integer pageSize;

    @ApiModelProperty(value = "查看更多商品数")
    private Integer morePageSize;

    @ApiModelProperty(value = "高佣商品配置")
    @IgnorePropertyJson
    private List<ProfitAdditionalComponentConfigDetailBO> productRuleConfigDetails;
}