package com.mengxiang.mshop.cms.core.model.request;

import com.mengxiang.base.common.model.request.PagingRequest;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.ResourceChannelEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 */
@Data
public class ResourceRequest extends PagingRequest {

    @ApiModelProperty("名称")
    private String name;

    /**
     * {@link ResourceStatusEnum}
     */
    @ApiModelProperty("组件状态：4=已生效,5=已失效,6=待生效")
    private Integer status;
    /**
     * {@link ResourceTypeEnum}
     */
    @ApiModelProperty(value = "组件类型 BANNER=banner组件，DIAMOND=金位位组件 ，STARTUPADVERTISEMENT=开机广告")
    private String resourceType;

    @ApiModelProperty(value = "多个组件类型")
    private List<String> resourceTypeList;

    @ApiModelProperty("所属者ID")
    private String ownerId;

    @ApiModelProperty("类型: 首页=home,个人中心=personalCenter ")
    private String resourcePageType;

    @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店）")
    private String ownerType;

    @ApiModelProperty("租户id")
    private String tenantId;
    /**
     * {@link ResourceChannelEnum}
     */
    @ApiModelProperty("端：SaaSApplets=小程序 ，SaaSApp=app")
    private String resourceChannel;

    @ApiModelProperty("类目")
    private String categoryId;


}
