package com.mengxiang.mshop.cms.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 2023/3/21
 * @Description:
 */
@Getter
public enum TemplateUseChannel {

    /**
     * h5
     */
    H5("h5", "h5"),
    /**
     * app
     */
    APP("app", "app"),
    /**
     * 小程序
     */
    MINIAPP("miniApp","小程序"),
    /**
     * pc端
     */
    PC("pc","pc端");


    private String channel;
    private String desc;

    TemplateUseChannel(String channel, String desc) {
        this.channel = channel;
        this.desc = desc;
    }

    public static TemplateUseChannel translate(String channel) {
        for (TemplateUseChannel templateUseChannel : TemplateUseChannel.values()) {
            if (templateUseChannel.getChannel().equals(channel)) {
                return templateUseChannel;
            }
        }
        return null;
    }
}
