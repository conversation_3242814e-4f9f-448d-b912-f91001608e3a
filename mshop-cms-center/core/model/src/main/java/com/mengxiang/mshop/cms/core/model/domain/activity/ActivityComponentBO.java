package com.mengxiang.mshop.cms.core.model.domain.activity;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class ActivityComponentBO extends ComponentBO {

   @ApiModelProperty(value = "默认展示商品数")
   private Integer pageSize = 10;

   @ApiModelProperty(value = "查看更多商品数")
   private Integer morePageSize = 10;

   @ApiModelProperty(value = "规则类型 自主: SELF 规则中心: RULE")
   private String ruleType;

   @ApiModelProperty(value = "配套标题 : true = 启用 false=不启用")
   private Boolean showTitleImg = Boolean.FALSE;

   @ApiModelProperty(value = "配套标题图片地址")
   private String titleImgUrl;


   @ApiModelProperty(value = "档期活动配置")
   @IgnorePropertyJson
   private List<ActivityComponentConfigDetailBO> activityRuleConfigDetails;
}
