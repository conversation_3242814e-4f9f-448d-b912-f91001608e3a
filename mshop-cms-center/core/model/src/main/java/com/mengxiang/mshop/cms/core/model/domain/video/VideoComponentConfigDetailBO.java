package com.mengxiang.mshop.cms.core.model.domain.video;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ContentCheckResultBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VideoComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.VIDEO.getCode();

    @ApiModelProperty(value = "视频宽度")
    private Double width;

    @ApiModelProperty(value = "视频高度")
    private Double height;

    @ApiModelProperty(value = "视频名称")
    private String name;

    @ApiModelProperty(value = "视频地址")
    private String url;

    @ApiModelProperty(value = "占位图地址")
    private String imageUrl;

    @ApiModelProperty(value = "视频封面")
    private String coverImg;

    @ApiModelProperty(value = "视频检测结果")
    @IgnorePropertyJson
    private ContentCheckResultBO checkResult;

}
