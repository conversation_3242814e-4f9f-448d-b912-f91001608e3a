package com.mengxiang.mshop.cms.core.model.domain.banner;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BannerComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.CAROUSEL.getCode();
    @ApiModelProperty(value = "图片url")
    private String img;
    @ApiModelProperty(value = "图片宽度")
    private Double width;
    @ApiModelProperty(value = "图片高度")
    private Double height;
    @ApiModelProperty(value = "跳转目标类型：页面:PAGE、链接:LINK、PRODUCT:商品、ACTIVITY:档期 ")
    private String targetType;
    @ApiModelProperty(value = "业务单号id、链接地址值、url")
    private String targetId;
    @ApiModelProperty(value = "档期ID")
    private String activityId;
    @ApiModelProperty(value = "跳转小程序id")
    private String miniAppId;
}
