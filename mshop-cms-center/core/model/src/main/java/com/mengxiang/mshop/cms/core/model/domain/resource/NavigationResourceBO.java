package com.mengxiang.mshop.cms.core.model.domain.resource;

import com.mengxiang.mshop.cms.core.model.enums.ResourceStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class NavigationResourceBO extends BaseResourceBO{



   @ApiModelProperty(value = "tab的配置")
   private List<ResourceConfig> tabConfigDetails;

   @Data
   public static class ResourceConfig{
      @ApiModelProperty(value = "导航名称")
      private String tabName;

      @ApiModelProperty(value = "跳转目标名称 ")
      private String targetName;

      @ApiModelProperty(value = "跳转目标类型：页面:PAGE、链接:LINK、PRODUCT:商品、ACTIVITY:档期 ")
      private String targetType;
      @ApiModelProperty(value = "业务单号id、链接地址值、url")
      private String targetId;
      @ApiModelProperty(value = "档期ID")
      private String activityId;
      @ApiModelProperty(value = "跳转小程序id")
      private String miniAppId;
      @ApiModelProperty(value = "跳转小程序链接")
      private String miniLinkUrl;
      /**
       * {@link ResourceStatusEnum}
       */
      @ApiModelProperty(value = "状态")
      private Integer status;

      @ApiModelProperty(value = "状态名")
      private String statusName;

      @ApiModelProperty("排序")
      private String orderValue;

      @ApiModelProperty("来源类型")
      private String sourceType;

      @ApiModelProperty("类目ID")
      private String categoryId;
   }
}
