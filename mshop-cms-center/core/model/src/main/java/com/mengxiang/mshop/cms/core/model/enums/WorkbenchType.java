package com.mengxiang.mshop.cms.core.model.enums;

import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Getter
public enum WorkbenchType {


    MENGXIANG_MARKET("1", "风火轮提交会场3.0审批"),

    SUPPLIER_MARKET("2", "商家提交会场3.0审批");

    private String code;
    private String desc;

    WorkbenchType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String checkWorkBenchByPage(SavePageRequest savePageRequest){
        return checkWorkBenchByPage(savePageRequest.getOwnerType(),savePageRequest.getType());
    }

    public static String checkWorkBenchByPage(String ownerType,String pageType){
        if (ownerType.equals(PageOwnerType.MENGXIANG.getOwnerType())) {
            //风火轮提交的需要审批
            return MENGXIANG_MARKET.getCode();
        }
        if (ownerType.equals(PageOwnerType.SUPPLIER.getOwnerType())
                && pageType.equals(PageType.MARKET_PAGE.getType())) {
            //商家提交的 会场3.0页面需要审批
            return SUPPLIER_MARKET.getCode();
        }

        return null;
    }
}
