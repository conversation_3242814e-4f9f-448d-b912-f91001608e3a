package com.mengxiang.mshop.cms.core.model.domain.navigation;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/5/11
 * @Description: 选品/选档规则
 */
@Data
public class NavigationRulesBO {

    @ApiModelProperty(value = "规则中心Code")
    private String ruleCode;

    @ApiModelProperty(value = "生效方式")
    private TimeConfigBO timeConfig;

    @ApiModelProperty(value = "规则状态 1:未生效 2:已生效 3:已失效")
    @IgnorePropertyJson
    private Integer ruleStatus;

    @ApiModelProperty(value = "规则创建时间")
    @IgnorePropertyJson
    private String ruleCreateTime;
}
