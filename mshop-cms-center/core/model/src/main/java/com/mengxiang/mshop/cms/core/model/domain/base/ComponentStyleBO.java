package com.mengxiang.mshop.cms.core.model.domain.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/4/19
 * @Description:
 */
@Data
public class ComponentStyleBO {

    @ApiModelProperty(value = "上边距")
    private Integer paddingTop;

    @ApiModelProperty(value = "下边距")
    private Integer paddingBottom;

    @ApiModelProperty(value = "左边距")
    private Integer paddingLeft;

    @ApiModelProperty(value = "右边距")
    private Integer paddingRight;

    @ApiModelProperty(value = "左上圆角")
    private Integer borderTopLeftRadius;
    @ApiModelProperty(value = "右上圆角")
    private Integer borderTopRightRadius;
    @ApiModelProperty(value = "左下圆角")
    private Integer borderBottomLeftRadius;
    @ApiModelProperty(value = "右下圆角")
    private Integer borderBottomRightRadius;
}
