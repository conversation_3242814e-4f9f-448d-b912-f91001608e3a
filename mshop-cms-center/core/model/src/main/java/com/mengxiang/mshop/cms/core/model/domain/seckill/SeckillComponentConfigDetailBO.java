package com.mengxiang.mshop.cms.core.model.domain.seckill;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SeckillComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.SECKILL.getCode();

    @ApiModelProperty(value = "时间 HH:mm")
    private String timeSlot;

    @ApiModelProperty(value = "状态 秒杀中:INSECKILL 预告: FEATURE")
    private String status;

}
