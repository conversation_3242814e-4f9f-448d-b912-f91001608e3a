package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 组件类型枚举
 * <AUTHOR>
 */
public enum ComponentDetailTypeEnum {
    /**
     * 图片热区配置
     */
    HOT("HOT", "图片热区配置"),

    /**
     * 图片挖孔区域配置
     */
    HOLE("HOLE", "图片挖孔区域配置"),
    /**
     * 档期活动配置
     */
    ACTIVITYRULE("ACTIVITYRULE", "档期活动配置"),
    /**
     * 轮播页面配置
     */
    CAROUSEL("CAROUSEL", "轮播页面配置"),
    /**
     * 优惠券配置
     */
    COUPON("COUPON", "优惠券配置"),
    /**
     * 导航栏配置
     */
    NAVIGATION("NAVIGATION", "导航栏配置"),
    /**
     * 商品配置
     */
    PRODUCTRULE("PRODUCTRULE", "商品配置"),
    /**
     * 高佣商品配置
     */
    PROFIT("PROFIT", "高佣商品配置"),
    /**
     * 视频配置
     */
    VIDEO("VIDEO", "视频配置"),
    /**
     * 榜单配置
     */
    TOPLIST("TOPLIST", "榜单配置"),
    /**
     * 秒杀配置
     */
    SECKILL("SECKILL", "秒杀配置"),
    /**
     * 图片配置
     */
    IMG("IMG", "图片配置"),
    /**
     * 营销活动配置
     */
    PROMOTIONACTIVITY("PROMOTIONACTIVITY", "营销活动配置"),
    ;


    private String code;
    private String desc;


    ComponentDetailTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ComponentDetailTypeEnum getEnumByCode(String code) {
        for (ComponentDetailTypeEnum item : ComponentDetailTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
