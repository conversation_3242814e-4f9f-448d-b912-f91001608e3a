package com.mengxiang.mshop.cms.core.model.domain.base;

import com.mengxiang.base.common.process.model.BusinessModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/21
 * @Description:
 */
@Data
@Builder
public class PageTemplateBO extends BusinessModel {

    @ApiModelProperty("模版编号")
    private String templateCode;

    @ApiModelProperty("模版主图")
    private String templateImageUrl;

    @ApiModelProperty("使用端：h5、app 、miniApp 、pc 可多个逗号分隔 TemplateUseChannel")
    private List<String> useChannels;

    @ApiModelProperty("页面使用规则")
    private TemplatePageUseRuleBO pageUseRule;

    @ApiModelProperty("页面使用范围：店铺主页:HOME  分类: CATEGORY  微页面:MICRO")
    private String pageUseType;

    @ApiModelProperty("组件使用规则")
    private List<TemplateComponentUseRuleBO> componentUseRule;


}
