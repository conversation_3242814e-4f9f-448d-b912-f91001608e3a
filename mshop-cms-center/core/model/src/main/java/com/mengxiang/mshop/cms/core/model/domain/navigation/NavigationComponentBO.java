package com.mengxiang.mshop.cms.core.model.domain.navigation;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description: 导航组件
 */
@Data
public class NavigationComponentBO extends ComponentBO {

    @ApiModelProperty(value = "普通导航类型 横向导航: HORIZONTAL 纵向导航: VERTICAL")
    private String navigationType;

    @ApiModelProperty(value = "导航类型 普通导航: 0 电梯导航: 1")
    private String styleType;

    @ApiModelProperty("背景颜色")
    private String backgroundColor;

    @ApiModelProperty(value = "背景图")
    private String backgroundImgUrl;

    @ApiModelProperty(value = "背景图 0:无图 1:有图")
    private Integer hasBackgroundImg;

    @ApiModelProperty("选中背景颜色")
    private String activeBackgroundColor;

    @ApiModelProperty(value = "字体选中色")
    private String activeFontColor;

    @ApiModelProperty(value = "选中飘块颜色")
    private String activeColor;

    @ApiModelProperty(value = "字体颜色")
    private String fontColor;

    @ApiModelProperty(value = "导航栏配置")
    @IgnorePropertyJson
    private List<NavigationComponentConfigDetailBO> navigationConfigDetails;

    @ApiModelProperty(value = "包含组件集合")
    private List<ComponentCodesBO> componentCodes;


}
