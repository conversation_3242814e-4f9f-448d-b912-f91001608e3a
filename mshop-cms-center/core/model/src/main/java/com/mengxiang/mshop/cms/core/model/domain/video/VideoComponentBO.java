package com.mengxiang.mshop.cms.core.model.domain.video;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description: 视频组件
 */
@Data
public class VideoComponentBO extends ComponentBO {

   @ApiModelProperty(value = "布局方式 11: 一行一  12:一行二")
   private String layoutType;

   @ApiModelProperty(value = "视频配置")
   @IgnorePropertyJson
   private List<VideoComponentConfigDetailBO> videoConfigDetails;
}
