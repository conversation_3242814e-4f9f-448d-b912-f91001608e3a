package com.mengxiang.mshop.cms.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Getter
public enum CubeLayoutType {
//    布局类型 1=双图并列 2=三图左一右2 3=四图上一下三 4=四宫格 5=三图并列 6=六宫格 7=九宫格
    /**
     * 双图并列
     */
    TYPE_1("1", "双图并列",2),
    /**
     * 三图左一右2
     */
    TYPE_2("2", "三图左一右2",3),
    /**
     * 四图上一下三
     */
    TYPE_3("3", "四图上一下三",4),
    /**
     * 四宫格
     */
    TYPE_4("4", "四宫格",4),
    /**
     * 三图并列
     */
    TYPE_5("5", "三图并列",3),
    /**
     * 六宫格
     */
    TYPE_6("6", "六宫格",6),
    /**
     * 九宫格
     */
    TYPE_7("7", "九宫格",9);

    private String code;
    private String desc;
    private Integer imgSize;

    CubeLayoutType(String code, String desc,Integer imgSize) {
        this.code = code;
        this.desc = desc;
        this.imgSize = imgSize;
    }

    public static Boolean checkImgSize(String code,Integer imgSize) {
        for (CubeLayoutType typeEnum : CubeLayoutType.values()) {
            if (typeEnum.getCode().equals(code)) {
                if(typeEnum.getImgSize().equals(imgSize)){
                    return Boolean.TRUE;
                }else{
                    return Boolean.FALSE;
                }
            }
        }
        return Boolean.FALSE;
    }

}
