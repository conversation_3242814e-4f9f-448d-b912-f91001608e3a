package com.mengxiang.mshop.cms.core.model.domain.product;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.PRODUCTRULE.getCode();

    @ApiModelProperty(value = "规则类型 自主: SELF 规则中心: RULE ,条件：CONDITION")
    private String ruleType;

    @ApiModelProperty(value = "规则中心Code")
    private String ruleCode;

    @ApiModelProperty(value = "规则状态 1:未生效 2:已生效 3:已失效")
    @IgnorePropertyJson
    private Integer ruleStatus;

    @ApiModelProperty(value = "规则创建时间")
    @IgnorePropertyJson
    private String ruleCreateTime;

    @ApiModelProperty(value = "档期ID")
    private String activityId;

    @ApiModelProperty(value = "自主选择的业务ID , 活动商品ID")
    private String businessId;

    @ApiModelProperty(value = "营销类型")
    private String  promotionType;

}
