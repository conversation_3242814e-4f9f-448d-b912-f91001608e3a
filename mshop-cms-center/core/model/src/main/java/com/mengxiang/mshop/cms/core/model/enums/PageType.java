package com.mengxiang.mshop.cms.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Getter
public enum PageType {

    /**
     * 首页
     */
    HOME_PAGE("HOME", "首页"),
    /**
     * 店铺主页
     */
    SHOP_PAGE("SHOP", "店铺主页"),
    /**
     * 会场
     */
    MARKET_PAGE("MARKET", "会场"),


    POINTS_PAGE("POINTS", "积分会场"),
    /**
     * 分类页
     */
    CATEGORY_PAGE("CATEGORY","分类页"),

    /**
     * 店铺页面
     */
    SHOP_MICRO_PAGE("SHOPMICRO","店铺页面"),
    /**
     * 微页面
     */
    MICRO_PAGE("MICRO","微页面");


    private String type;
    private String desc;

    PageType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PageType translate(String type) {
        for (PageType typeEnum : PageType.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static boolean isOnlyPage(String type) {
        if (type.equals(PageType.HOME_PAGE.getType()) ||
                type.equals(PageType.SHOP_PAGE.getType()) ||
                type.equals(PageType.POINTS_PAGE.getType()) ||
                type.equals(PageType.CATEGORY_PAGE.getType())) {
            return true;
        }
        return false;
    }
}
