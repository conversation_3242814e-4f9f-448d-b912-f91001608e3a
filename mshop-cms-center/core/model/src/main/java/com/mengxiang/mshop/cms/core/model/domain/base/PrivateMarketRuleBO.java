package com.mengxiang.mshop.cms.core.model.domain.base;

import com.mengxiang.mshop.cms.core.model.enums.ChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
public class PrivateMarketRuleBO {

    @ApiModelProperty("私密会场id")
    private String marketId;

    @ApiModelProperty("渠道类型")
    private ChannelTypeEnum channelType;

    @ApiModelProperty("用户群组id列表")
    private List<String> userGroupIds;

    @ApiModelProperty("用户群组列表")
    private List<UserGroupBO> userGroups;

    @ApiModelProperty("活动id列表")
    private List<String> activityIds;

}
