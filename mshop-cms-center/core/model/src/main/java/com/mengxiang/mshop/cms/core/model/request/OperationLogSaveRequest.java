package com.mengxiang.mshop.cms.core.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 保存操作日志
 */
@Data
public class OperationLogSaveRequest {

    @ApiModelProperty(value = "行为")
    private String action;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "业务编号")
    private String bizCode;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty(value = "操作后数据")
    private String afterData;

    @ApiModelProperty(value = "操作前数据")
    private String beforeData;

    @ApiModelProperty(value = "业务类型 OperationLogBizTypeEnum")
    private Integer bizType;

    @ApiModelProperty(value = "创建人类型")
    private String ownerType;
}
