package com.mengxiang.mshop.cms.core.model.request;

import com.mengxiang.base.common.model.request.BaseRequest;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 */
@Data
public class CreatePageRequest extends BaseRequest {

    @ApiModelProperty("模版编号")
    private String templateCode;

    @ApiModelProperty("所属者ID")
    private String ownerId;

    @ApiModelProperty("类型:首页 PageType")
    private String pageType;

    @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店）")
    private String ownerType = PageOwnerType.SUPPLIER.getOwnerType();

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("端：h5、app 、小程序")
    private String channel;


}

