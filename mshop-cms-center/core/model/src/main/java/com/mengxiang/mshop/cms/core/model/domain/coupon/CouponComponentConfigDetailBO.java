package com.mengxiang.mshop.cms.core.model.domain.coupon;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CouponComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.COUPON.getCode();

    @ApiModelProperty(value = "营销活动ID")
    private String promoActivityId;

    @ApiModelProperty(value = "优惠券ID")
    private String awdId;

    @ApiModelProperty(value = "档期ID")
    private String activityId;

}
