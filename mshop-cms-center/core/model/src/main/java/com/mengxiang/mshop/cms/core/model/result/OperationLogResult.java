package com.mengxiang.mshop.cms.core.model.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 操作日志返回对象
 */
@Data
public class OperationLogResult {

    @ApiModelProperty("行为【创建、编辑、发布】")
    private String action;

    @ApiModelProperty("remark 备注")
    private String remark;

    @ApiModelProperty("业务编号")
    private String bizCode;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("操作后数据")
    private String afterData;

    @ApiModelProperty("操作前数据")
    private String beforeData;

    @ApiModelProperty("业务类型")
    private Integer bizType;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人类型")
    private String ownerType;
}
