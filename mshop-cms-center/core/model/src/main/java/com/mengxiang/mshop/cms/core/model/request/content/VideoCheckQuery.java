package com.mengxiang.mshop.cms.core.model.request.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 视频检查查询
 */
@Data
@ApiModel
public class VideoCheckQuery {

    @ApiModelProperty(notes = "dataIds", required = true)
    private List<String> dataIds;

    @ApiModelProperty(notes = "操作人", required = true)
    private String operateBy;
}
