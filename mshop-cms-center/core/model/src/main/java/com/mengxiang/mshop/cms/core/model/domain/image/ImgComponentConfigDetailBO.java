package com.mengxiang.mshop.cms.core.model.domain.image;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class ImgComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.HOT.getCode();
    @ApiModelProperty(value = "跳转目标类型：页面:PAGE、链接:LINK、PRODUCT:商品、ACTIVITY:档期 优惠券:COUPON 秒杀:SECKILL 榜单:TOPLIST 高佣:PROFITADDITIONAL")
    private String targetType;
    @ApiModelProperty(value = "目标参数 商品ID、档期ID、页面Code、url、业务二级页跳转地址")
    private String targetId;
    @ApiModelProperty(value = "档期ID")
    private String activityId;
    @ApiModelProperty(value = "热点区域x点(相对左上角)")
    private Double x;
    @ApiModelProperty(value = "热点区域y点(相对左上角)")
    private Double y;
    @ApiModelProperty(value = "热点宽度")
    private Double width;
    @ApiModelProperty(value = "热点高度")
    private Double height;
    @ApiModelProperty(value = "跳转小程序id")
    private String miniAppId;
}
