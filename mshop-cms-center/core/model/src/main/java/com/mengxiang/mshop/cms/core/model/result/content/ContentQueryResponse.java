package com.mengxiang.mshop.cms.core.model.result.content;

import com.mengxiang.mshop.cms.core.model.enums.ContentSuggestTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class ContentQueryResponse {
    
    @ApiModelProperty(value = "检测对象的数据ID")
    private String dataId;
    
    @ApiModelProperty(value = "检测任务ID")
    private String taskId;
    
    @ApiModelProperty(value = "检测对象的URL")
    private String url;
    
    @ApiModelProperty(value = "建议操作", example = "pass：结果正常,review：结果不确定，需要进行人工审核,block：结果违规，建议直接删除或者限制公开")
    private ContentSuggestTypeEnum suggestion;
    
    @ApiModelProperty(value = "建议操作")
    private List<String> checkDesc;
}
