package com.mengxiang.mshop.cms.core.model.domain.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/3/29
 * @Description:
 */
@Data
public class TimeCycleBO {
    @ApiModelProperty(value = "开始时间HH:mm")
    private String startTime;
    @ApiModelProperty(value = "结束时间HH:mm")
    private String endTime;
    @ApiModelProperty(value = "循环周期 1=每周 2=每月")
    private Integer timeType;
    @ApiModelProperty(value = "每周1-7")
    private Integer timeWeekValue;
    @ApiModelProperty(value = "每月1-31")
    private Integer timeMonthValue;
}
