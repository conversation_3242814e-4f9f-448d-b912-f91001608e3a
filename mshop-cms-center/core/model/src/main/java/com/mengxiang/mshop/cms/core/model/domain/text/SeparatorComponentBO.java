package com.mengxiang.mshop.cms.core.model.domain.text;

import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description: 分隔符组件
 */
@Data
public class SeparatorComponentBO extends ComponentBO {
   @ApiModelProperty(value = "分隔符文本")
   private String text;

   @ApiModelProperty(value = "文本字号")
   private Double fontSize;

   @ApiModelProperty(value = "文本颜色")
   private String fontColor;
}
