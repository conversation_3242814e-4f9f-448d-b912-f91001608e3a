package com.mengxiang.mshop.cms.core.model.request.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 保存工作流对象
 */
@Data
public class SaveProcRequest {

    /**
     * 页面编号
     */
    @ApiModelProperty("页面编号")
    private String pageCode;

    @ApiModelProperty("页面名称")
    private String name;

    /**
     * 生效版本号
     */
    @ApiModelProperty("生效版本号")
    private String version;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("创建人")
    private String userName;

    @ApiModelProperty("创建人ID")
    private String userId;

    @ApiModelProperty("直接上级工号")
    private String managerNoDirect;

    @ApiModelProperty("二级部门负责人工号")
    private String managerNoSecond;

    @ApiModelProperty( "一级部门负责人工号")
    private String managerNoFirst;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("流程节点变更的表单内容;业务需要展示的核心4个字段")
    private WorkflowEventForm workflowEventForm;

    @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店）")
    private String ownerType;

    @ApiModelProperty("首页: HOME  店铺主页:SHOP 会场:MARKET 分类页:CATEGORY 微页面:MICRO 积分会场：POINTS")
    private String type;

    @ApiModelProperty("审批人")
    private List<String> auditList;

    private String merchantCode;

    private String shopName;

    private String ownerId;

    /**
     * 类目长
     */
    private List<String> categoryLeaderList;
}
