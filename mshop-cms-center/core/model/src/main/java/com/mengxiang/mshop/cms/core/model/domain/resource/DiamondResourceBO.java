package com.mengxiang.mshop.cms.core.model.domain.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class DiamondResourceBO extends BaseResourceBO{

   @ApiModelProperty(value = "金刚位配置")
   private List<DiamondResourceConfig> carouselConfigDetails;

   @Data
   public static class DiamondResourceConfig{
      @ApiModelProperty(value = "金刚位图片")
      private String imgUrl;
      @ApiModelProperty(value = "关联的原金刚位id")
      private String sourceId;

   }
}
