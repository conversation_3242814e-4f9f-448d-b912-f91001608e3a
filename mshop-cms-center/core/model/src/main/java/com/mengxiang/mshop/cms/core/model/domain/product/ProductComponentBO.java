package com.mengxiang.mshop.cms.core.model.domain.product;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class ProductComponentBO extends ComponentBO {

   @ApiModelProperty(value = "背景图片")
   private String backgroundImgUrl;

   @ApiModelProperty(value = "带图样式  top 、left")
   private String imgStyle;

   @ApiModelProperty(value = "背景色")
   private String backgroundColor;

   @ApiModelProperty(value = "背景图 0:无图 1:有图")
   private Integer hasBackgroundImg;

   @ApiModelProperty(value = "动效 0:无动效 1:滑动缩放")
   private String animationType;

   @ApiModelProperty(value = "布局方式 11: 一行一  12:一行一 13:一行三 10:一行多")
   private String layoutType;

   @ApiModelProperty(value = "配套标题 : true = 启用 false=不启用")
   private Boolean showTitleImg = Boolean.FALSE;

   @ApiModelProperty(value = "配套标题图片地址")
   private String titleImgUrl;

   @ApiModelProperty(value = "字体选中色")
   private String activeFontColor;

   @ApiModelProperty(value = "默认展示商品数")
   private Integer pageSize = 8;

   @ApiModelProperty(value = "查看更多商品数")
   private Integer morePageSize = 8;

   @ApiModelProperty(value = "规则类型 自主: SELF 规则中心: RULE")
   private String ruleType;

   @ApiModelProperty(value = "商品配置")
   @IgnorePropertyJson
   private List<ProductComponentConfigDetailBO> productRuleConfigDetails;
}
