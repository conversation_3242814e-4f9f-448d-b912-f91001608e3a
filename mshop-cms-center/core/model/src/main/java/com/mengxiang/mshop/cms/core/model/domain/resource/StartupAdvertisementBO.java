package com.mengxiang.mshop.cms.core.model.domain.resource;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class StartupAdvertisementBO extends BaseResourceBO{

   @ApiModelProperty(value = "广告图片ios1")
   private String iosImgUrl1;
   @ApiModelProperty(value = "广告图片ios2")
   private String iosImgUrl2;
   @ApiModelProperty(value = "广告图片android")
   private String androidImgUrl;

   @ApiModelProperty(value = "轮播配置")
   private ResourceConfig carouselConfigDetails;

   @Data
   public static class ResourceConfig{
      @ApiModelProperty(value = "跳转目标类型：页面:PAGE、链接:LINK、PRODUCT:商品、ACTIVITY:档期 ")
      private String targetType;
      @ApiModelProperty(value = "业务单号id、链接地址值、url")
      private String targetId;
      @ApiModelProperty(value = "档期ID")
      private String activityId;
      @ApiModelProperty(value = "跳转小程序id")
      private String miniAppId;
      @ApiModelProperty(value = "跳转小程序链接")
      private String miniLinkUrl;
   }
}
