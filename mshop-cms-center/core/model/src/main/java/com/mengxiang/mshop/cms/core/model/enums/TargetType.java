package com.mengxiang.mshop.cms.core.model.enums;

/**
 * 目标类型
 * <AUTHOR>
 */
public enum TargetType {

    /**
     * 页面
     */
    PAGE("PAGE", "页面"),

    /**
     * 链接
     */
    LINK("LINK", "链接"),

    /**
     * 商品
     */
    PRODUCT("PRODUCT", "商品"),

    /**
     * 档期
     */
    ACTIVITY("ACTIVITY", "档期"),

    /**
     * 高佣
     */
    PROFITADDITIONAL_LIST("PROFITADDITIONAL_LIST","高佣"),
    /**
     * 榜单
     */
    TOPLIST("TOPLIST","榜单"),
    /**
     * 秒杀
     */
    SECKILL_LIST("SECKILL_LIST","秒杀"),
    /**
     * 优惠券
     */
    COUPON_LIST("COUPON_LIST","优惠券"),

    /**
     * 店铺页面
     */
    SHOPMICRO("SHOPMICRO","店铺页面"),
    /**
     * SAAS店铺页面
     */
    SAASSHOPMICRO("SAASSHOPMICRO","SAAS店铺页面"),

    /**
     * 超品会场-老
     */
    HOTSALE_OLD("HOTSALE_OLD","超品会场-老"),

    /**
     * 普通会场-老
     */
    CONFERENCE_OLD("CONFERENCE_OLD","普通会场-老"),
    /**
     * 会场3.0
     */
    CONFERENCE_3("CONFERENCE_3","会场3.0"),
    /**
     * 直播间
     */
    LIVE_ROOM("LIVE_ROOM","直播间"),
    /**
     * 店铺S码
     */
    SHOP("SHOP","店铺S码"),

    /**
     * 微信小程序
     */
    MINI_APP("MINI_APP","微信小程序"),
    /**
     * 公众号文章
     */
    OFFIACCOUNT("OFFIACCOUNT","公众号文章"),

    /**
     * 导师二维码
     */
    MENTORQR("MENTORQR","导师二维码"),

    ;
    private String type;
    private String desc;
    
    TargetType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    
    public static TargetType getByType(String type) {
        for (TargetType typeEnum : TargetType.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    public String getType() {
        return type;
    }
}
