package com.mengxiang.mshop.cms.core.model.enums;

/**
 * <AUTHOR>
 */

public enum ErrorEnum {

    /**
     * 服务超时
     */
    SERVER_TIME_OUT("服务超时","9999"),
    /**
     * 视频校验失败
     */
    VIDEO_CHECK_ERROR("视频校验失败","6666");

    private String code;
    private String msg;

    ErrorEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setValue(String msg) {
        this.msg = msg;
    }
}