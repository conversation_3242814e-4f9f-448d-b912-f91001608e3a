package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 组件类型枚举
 * <AUTHOR>
 */
public enum ComponentTypeEnum {
    /**
     图片组件 ：IMAGE
     */
    IMAGE("IMAGE", "图片组件"),
    /**
     banner组件：BANNER
     */
    BANNER("BANNER", "banner组件"),
    /**
     优惠券组件：COUPON
     */
    COUPON("COUPON", "优惠券组件"),
    /**
     积分优惠券组件：POINTSCOUPON
     */
    POINTSCOUPON("POINTSCOUPON", "积分优惠券组件"),
    /**
     档期组件：ACTIVITY
     */
    ACTIVITY("ACTIVITY", "档期组件"),
    /**
     导航组件：NAVIGATION
     */
    NAVIGATION("NAVIGATION", "导航组件"),
    /**
     秒杀组件：SECKILL
     */
    SECKILL("SECKILL", "秒杀组件"),
    /**
     商品组件：PRODUCT
     */
    PRODUCT("PRODUCT", "商品组件"),
    /**
     视频组件：VIDEO
     */
    VIDEO("VIDEO", "视频组件"),
    /**
     分隔符组件：SEPARATOR
     */
    SEPARATOR("SEPARATOR", ""),
    /**
     榜单组件：TOPLIST
     */
    TOPLIST("TOPLIST", "榜单组件"),
    /**
     高佣组件：PROFITADDITIONAL
     */
    PROFITADDITIONAL("PROFITADDITIONAL", "高佣组件"),
    /**
     图片魔方组件：CUBE
     */
    CUBE("CUBE", "图片魔方组件"),

    TEXT("TEXT", "文本组件"),

    MATERIAL("MATERIAL", "素材组件"),

    ALLPRODUCT("ALLPRODUCT", "全部商品组件"),

    USERINCENTIVE("USERINCENTIVE", "爱豆激励组件"),
    ;


    private String code;
    private String desc;


    ComponentTypeEnum(String code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ComponentTypeEnum getEnumByCode(String code) {
        for (ComponentTypeEnum item : ComponentTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
