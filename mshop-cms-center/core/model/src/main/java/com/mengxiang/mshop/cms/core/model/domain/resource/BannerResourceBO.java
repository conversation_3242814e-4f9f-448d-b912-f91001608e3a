package com.mengxiang.mshop.cms.core.model.domain.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class BannerResourceBO extends BaseResourceBO{

   @ApiModelProperty(value = "轮播配置")
   private List<BannerResourceConfig> carouselConfigDetails;

   @Data
   public static class BannerResourceConfig{
      @ApiModelProperty(value = "跳转目标类型：页面:PAGE、链接:LINK、PRODUCT:商品、ACTIVITY:档期 ")
      private String targetType;
      @ApiModelProperty(value = "业务单号id、链接地址值、url")
      private String targetId;
      @ApiModelProperty(value = "档期ID")
      private String activityId;
      @ApiModelProperty(value = "跳转小程序id")
      private String miniAppId;
      @ApiModelProperty(value = "图片url")
      private String imgUrl;
      @ApiModelProperty(value = "跳转小程序链接")
      private String miniLinkUrl;
   }
}
