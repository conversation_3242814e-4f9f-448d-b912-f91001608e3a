package com.mengxiang.mshop.cms.core.model.request.content;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class PageContentCheck {
    
    @ApiModelProperty(notes = "页面", required = true)
    private String pageCode;
    
    @ApiModelProperty(notes = "版本", required = true)
    private String version;
    
    @ApiModelProperty(notes = "组件url", required = true)
    List<PageContentRequest> componentUrls;
}
