package com.mengxiang.mshop.cms.core.model.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeSlotBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 组件工具类
 * <AUTHOR>
 */
public class ComponentUtils {

    public static final Map<ComponentTypeEnum, String> COMPONENT_DETAIL_MAP = new HashMap<>();
    static {
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.COUPON, "couponConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.BANNER, "carouselConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.ACTIVITY, "activityRuleConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.IMAGE, "hotConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.NAVIGATION, "navigationConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.PRODUCT, "productRuleConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.PROFITADDITIONAL, "productRuleConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.SECKILL, "seckillConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.TOPLIST, "topListConfigDetails");
        COMPONENT_DETAIL_MAP.put(ComponentTypeEnum.VIDEO, "videoConfigDetails");
    }


    public static <T> List<T> getListByString(List<String> jsons,Class<T> transfer) {
        if (CollectionUtils.isEmpty(jsons)) {
            return Lists.newArrayList();
        }
        List<T> resp = new ArrayList<>();
        for (String json : jsons) {
            T t = JSON.parseObject(json, transfer);
            resp.add(t);
        }
        return resp;
    }

    public <T> List<T> getListByJsonObject(List<JSONObject> jsons,Class<T> transfer) {
        return Optional.ofNullable(jsons)
                .orElse(Collections.emptyList())
                .stream()
                .map(json -> json.toJavaObject(transfer))
                .collect(Collectors.toList());
    }

    public static <T> T findComponent(String components,String componentCode, Class<T> transfer) {
        if (StringUtils.isEmpty(components)) {
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(components);
        return jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .filter(obj -> !StringUtils.isEmpty(obj.getString("componentCode")) && obj.getString("componentCode").equals(componentCode))
                .findFirst()
                .map(obj -> obj.toJavaObject(transfer))
                .orElse(null);
    }

    public ComponentTypeEnum getComponentType(String components,String componentCode) {
        if (StringUtils.isEmpty(components)) {
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(components);
        return jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .filter(obj -> !StringUtils.isEmpty(obj.getString("componentCode")) && obj.getString("componentCode").equals(componentCode))
                .findFirst()
                .map(obj -> ComponentTypeEnum.getEnumByCode(obj.getString("type")))
                .orElse(null);
    }
    public static <T> T getComponent (JSONObject json,Class<T> transfer) {
        if (Objects.isNull(json)) {
            return null;
        }
        return json.toJavaObject(transfer);
    }
    public static Map<ComponentTypeEnum, JSONObject> getComponentMap(String components, String componentCode) {
        if (StringUtils.isEmpty(components)) {
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(components);
        return jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .filter(obj -> !StringUtils.isEmpty(obj.getString("componentCode")) && obj.getString("componentCode").equals(componentCode))
                .collect(Collectors.toMap(obj -> ComponentTypeEnum.getEnumByCode(obj.getString("type")), Function.identity(), (k1, k2) -> k1));
    }
}
