package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 组件类型枚举
 * <AUTHOR>
 */
public enum ResourceTypeEnum {

    /**
     banner组件：BANNER
     */
    BANNER("BANNER", "banner组件"),
    /**
     优惠券组件：COUPON
     */
    DIAMOND("DIAMOND", "金刚位"),
    /**
     积分优惠券组件：POINTS_COUPON
     */
    STARTUPADVERTISEMENT("STARTUPADVERTISEMENT", "开机广告"),

    NAVIGATION("NAVIGATION", "导航"),
    ;


    private String code;
    private String desc;


    ResourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getEnumNameByCode(String code) {
        for (ResourceTypeEnum item : ResourceTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
