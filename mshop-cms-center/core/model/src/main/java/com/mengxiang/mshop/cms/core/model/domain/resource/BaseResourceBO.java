package com.mengxiang.mshop.cms.core.model.domain.resource;

import com.mengxiang.mshop.cms.core.model.domain.base.DirectUserGroupBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.ResourcePageTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class BaseResourceBO {
   @ApiModelProperty(value = "编号")
   private Long id;

   @ApiModelProperty(value = "名称")
   private String name;

   @ApiModelProperty("端：h5、app 、小程序")
   private List<String> channelList;

   @ApiModelProperty("端：h5、app 、小程序")
   private String channelName;

   @ApiModelProperty("类目")
   private String categoryId;

   @ApiModelProperty("类目名称")
   private String categoryName;
   /**
    * {@link ResourcePageTypeEnum}
    */
   @ApiModelProperty("页面位置")
   private String resourcePageType;
   @ApiModelProperty("页面位置")
   private String resourcePageName;
   /**
    * {@link ResourceTypeEnum}
    */
   @ApiModelProperty(value = "组件类型")
   private String resourceType;

   @ApiModelProperty(value = "组件类型")
   private String resourceName;

   @ApiModelProperty(value = "定向分群")
   private DirectUserGroupBO directUserGroup;

   @ApiModelProperty(value = "生效方式")
   private TimeConfigBO timeConfig;

   @ApiModelProperty(value = "操作人")
   private String createBy;

   /**
    * {@link ResourceStatusEnum}
    */
   @ApiModelProperty(value = "状态")
   private Integer status;

   @ApiModelProperty(value = "状态")
   private String statusName;

   @ApiModelProperty("所属者ID")
   private String ownerId;

   @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店）")
   private String ownerType;

   @ApiModelProperty("租户id")
   private String tenantId;

   @ApiModelProperty("排序")
   private String orderValue;

   @ApiModelProperty("来源类型")
   private String sourceType;
}
