package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 操作日志操作枚举
 * <AUTHOR>
 */
public enum OperationLogActionEnum {

    /**
     * 保存页面
     */
    SAVE("SAVE", "保存页面"),
    /**
     * 发布页面
     */
    PUBLISH("PUBLISH", "发布页面"),

    /**
     * 失效页面
     */
    DISABLED("DISABLED", "失效页面"),

    /**
     * 生效页面
     */
    EXECUTORY("EXECUTORY", "生效页面"),

    /**
     * 生效页面
     */
    EDIT("EDIT", "进入了编辑"),
    /**
     * 生效页面
     */
    AGREE("AGREE", "审批通过"),
    /**
     * 生效页面
     */
    REJECT("REJECT", "审批驳回"),

    ;


    private String code;
    private String desc;

    OperationLogActionEnum(String code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static OperationLogActionEnum checkEnumByCode(String code) {
        for (OperationLogActionEnum item : OperationLogActionEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
