package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 操作日志业务类型枚举
 * <AUTHOR>
 */
public enum OperationLogBizTypeEnum {

    /**
     * 操作日志记录类型
     * 1=页面
     */
    PAGE(1,"page", "页面操作记录"),
    /**
     * 操作日志记录类型
     * 2=规则配置
     */
    RULE(2,"rule", "规则操作记录"),

    ;


    private Integer code;
    private String name;

    private String desc;

    OperationLogBizTypeEnum(Integer code , String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }
    
    public static OperationLogBizTypeEnum getEnumByName(String name) {
        for (OperationLogBizTypeEnum item : OperationLogBizTypeEnum.values()) {
            if (item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static OperationLogBizTypeEnum getEnumByCode(Integer code) {
        for (OperationLogBizTypeEnum item : OperationLogBizTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    public String getName() {
        return name;
    }
    
}
