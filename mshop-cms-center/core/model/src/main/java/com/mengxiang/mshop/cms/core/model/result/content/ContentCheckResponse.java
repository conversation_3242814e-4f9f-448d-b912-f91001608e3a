package com.mengxiang.mshop.cms.core.model.result.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 检测返回结果
 */
@Data
@ApiModel
public class ContentCheckResponse {

    @ApiModelProperty(notes = "是否合格 文本、图片:是否审核成功, 视频:是否上传成功")
    private boolean qualifiedFlag;

    @ApiModelProperty(notes = "校验失败描述信息")
    private List<String> checkDesc;
    
    @ApiModelProperty(notes = "文本、图片、视频返回的校验数据id")
    private String dateId;
    
    @ApiModelProperty(notes = "检测内容")
    private String content;

    @ApiModelProperty(
            value = "建议操作提示文案",
            example = "存在下列敏感词【%s所有命中关键词，多个使用逗号分割】，请核实修改后使用！")
    private String suggestionTip;
}
