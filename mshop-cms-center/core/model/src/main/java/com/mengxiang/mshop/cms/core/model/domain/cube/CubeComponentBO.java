package com.mengxiang.mshop.cms.core.model.domain.cube;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImgComponentConfigDetailBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class CubeComponentBO extends ComponentBO {
   
   @ApiModelProperty(value = "布局类型 1=双图并列 2=三图左一右2 3=四图上一下三 4=四宫格 5=三图并列 6=六宫格 7=九宫格")
   private String layoutType;

   
   @ApiModelProperty(value = "图片配置")
   @IgnorePropertyJson
   private List<CubeComponentConfigDetailBO> imageList;
}
