package com.mengxiang.mshop.cms.core.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 组件详情聚合
 * <AUTHOR>
 */
@Data
public class ComponetAggregationRequest {

    @ApiModelProperty("页面编号")
    private String pageCode;
    
    @ApiModelProperty("版本")
    private String version;
    
    @ApiModelProperty("组件编号")
    private String componentCode;
    
    @ApiModelProperty("组件类型")
    private String componentType;
}
