package com.mengxiang.mshop.cms.core.model.enums;

/**
 * <AUTHOR>
 * @Date: 2023/5/25
 * @Description: 定向用户展示类型[0:全部 1:用户类型 2:用户分群]
 */
public enum DirectShowTypeEnum {

    /**
     * 全部
     */
    ALL(0, "全部"),
    /**
     * 用户类型
     */
    USER_TYPE(1, "用户类型"),

    /**
     * 用户分群
     */
    USER_GROUP(2, "用户分群");

    private int code;
    private String desc;


    DirectShowTypeEnum(int code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static DirectShowTypeEnum getEnumByCode(int code) {
        for (DirectShowTypeEnum item : DirectShowTypeEnum.values()) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }
}
