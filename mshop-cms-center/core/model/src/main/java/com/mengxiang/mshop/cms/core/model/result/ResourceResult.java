package com.mengxiang.mshop.cms.core.model.result;

import com.mengxiang.mshop.cms.core.model.domain.resource.BannerResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.DiamondResourceBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ResourceResult {

    @ApiModelProperty("banner组件")
    private List<BannerResourceBO> bannerResourceList;

    @ApiModelProperty("金刚位组件(替换图片)")
    private List<DiamondResourceBO.DiamondResourceConfig> diamondResourceList;


}
