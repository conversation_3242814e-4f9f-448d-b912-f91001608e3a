package com.mengxiang.mshop.cms.core.model.domain.promotion;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class UserIncentiveComponentBO extends ComponentBO {


   @ApiModelProperty(value = "配置详情")
   @IgnorePropertyJson
   private List<PromotionActivityComponentConfigDetailBO> promotionActivityConfigDetails;
}
