package com.mengxiang.mshop.cms.core.model.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/24
 * @Description:
 */
@Getter
public enum PromotionActivityTypeEnum {
    /**
     * 转发红包
     */
    RED_PACKET("RED_PACKET", "转发红包"),
    /**
     * 满返
     */
    FULL_RETURN("FULL_RETURN", "满返"),
    /**
     * 满赠
     */
    IDOL_GIFTS("IDOL_GIFTS", "满赠"),
    /**
     * PK榜单
     */
    PK_TOP_LIST("PK_TOP_LIST", "PK榜单"),
    /**
     * 派单任务
     */
    ORDER_TASK("ORDER_TASK", "派单任务");


    private String code;
    private String desc;


    PromotionActivityTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
