package com.mengxiang.mshop.cms.core.model.request.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工作流-业务单详情查询
 */
@Data
public class WorkflowDetailRequest {

    @ApiModelProperty("流程定义key;开发约定的流程定义key")
    private String procKey;

    /**
     * businessKey 业务key.
     */
    @ApiModelProperty("业务key;业务自己可以识别的唯一id")
    private String businessKey;

    @ApiModelProperty("流程编号")
    private String procInstCode;

}
