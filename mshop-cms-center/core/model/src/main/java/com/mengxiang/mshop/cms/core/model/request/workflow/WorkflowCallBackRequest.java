package com.mengxiang.mshop.cms.core.model.request.workflow;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流回调请求
 */
@Data
public class WorkflowCallBackRequest {
    @ApiModelProperty("流程key")
    private String procKey;
    @ApiModelProperty("工单号")
    private String procInstCode;
    @ApiModelProperty("业务key")
    private String businessKey;
    @ApiModelProperty("结束标识")
    private boolean endStatus;
    @ApiModelProperty("业务节点")
    private String taskKey;
    @ApiModelProperty("节点id")
    private String taskId;
    @ApiModelProperty("下个节点状态")
    private List<String> nextKeyList;
    @ApiModelProperty("操作名")
    private String buttonControl;
    @ApiModelProperty("创建人工号")
    private String createBy;
    @ApiModelProperty("创建人名称")
    private String createName;
    @ApiModelProperty("请求参数体")
    private HashMap<String, Object> reqBody;


}
