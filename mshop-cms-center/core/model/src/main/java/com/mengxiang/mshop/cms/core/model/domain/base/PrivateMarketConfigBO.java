package com.mengxiang.mshop.cms.core.model.domain.base;

import com.mengxiang.mshop.cms.core.model.enums.ChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
public class PrivateMarketConfigBO {
    @ApiModelProperty("渠道类型")
    private ChannelTypeEnum channelType;

//    @ApiModelProperty("人群包id列表")
//    private List<String> userGroupIds;

    @ApiModelProperty("人群包列表")
    private List<UserGroupBO> userGroups;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;
}
