package com.mengxiang.mshop.cms.core.model.domain.navigation;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/14
 * @Description: 导航组件
 */
@Data
public class NavigationComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.NAVIGATION.getCode();

    @ApiModelProperty(value = "导航 tab 名称")
    private String tabName;

    @ApiModelProperty(value = "导航tab 显示状态 1：显示 0 不显示")
    private Integer tabShowFlag;

    @ApiModelProperty(value = "弹幕:0展示, 1不展示")
    private Integer bulletScreenDisplay;

    @ApiModelProperty(value = "tab 导航内容展示样式")
    private String layoutType;

    @ApiModelProperty(value = "导航 tab 图片")
    private String tabImage;

    @ApiModelProperty(value = "锚点导航定位组件 code")
    private String archorComponentCode;

    @ApiModelProperty(value = "前端组件id")
    @IgnorePropertyJson
    private String componentId;

    @ApiModelProperty(value = "分割样式 文本: TEXT 图片:IMAGE")
    private String tabSeparatorType;

    @ApiModelProperty(value = "导航配置类型 商品: PRODUCT 档期活动: ACTIVITY")
    private String type;

    @ApiModelProperty(value = "规则类型 自主: SELF 规则中心: RULE")
    private String ruleType;

    @ApiModelProperty(value = "规则中心Code")
    private String ruleCode;

    @ApiModelProperty(value = "多个规则")
    private List<NavigationRulesBO> rules;

    @ApiModelProperty(value = "默认展示商品数")
    private Integer pageSize;

    @ApiModelProperty(value = "查看更多商品数")
    private Integer morePageSize;

    @ApiModelProperty(value = "自主选择的业务ID集合 , 活动商品ID集合、活动档期ID集合")
    private List<String> businessIds;

}
