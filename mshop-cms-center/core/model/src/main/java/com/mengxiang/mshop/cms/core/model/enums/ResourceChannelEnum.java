package com.mengxiang.mshop.cms.core.model.enums;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum  ResourceChannelEnum {

    SAA_S_APPLETS("SaaSApplets", "saas 企业饷店小程序渠道","小程序"),
    SAA_S_APP("SaaSApp", "saas 企业饷店APP","app"),
    ;

    private String code;
    private String desc;
    private String name;

    ResourceChannelEnum(String code, String desc,String name) {
        this.code = code;
        this.desc = desc;
        this.name = name;
    }


    public static String getEnumNameByCode(List<String> codeList) {
        if(CollectionUtils.isEmpty(codeList)){
            return "";
        }

        List<String> nameList = Lists.newArrayList();
        for (ResourceChannelEnum item : ResourceChannelEnum.values()) {
            if (codeList.contains(item.getCode())) {
                nameList.add(item.getName());
            }
        }
        if(CollectionUtils.isNotEmpty(nameList)){
            return nameList.stream().collect(Collectors.joining(","));
        }else{
            return "";
        }
    }
}
