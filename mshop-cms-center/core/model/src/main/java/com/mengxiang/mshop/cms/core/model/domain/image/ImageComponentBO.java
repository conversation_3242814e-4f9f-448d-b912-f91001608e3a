package com.mengxiang.mshop.cms.core.model.domain.image;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class ImageComponentBO extends ComponentBO {
   
   @ApiModelProperty(value = "背景图")
   private String url;

   @ApiModelProperty(value = "图片宽度")
   private Double width;

   @ApiModelProperty(value = "图片高度")
   private Double height;
   
   @ApiModelProperty(value = "弹幕:0展示, 1不展示")
   private Integer bulletScreenDisplay;
   
   @ApiModelProperty(value = "弹幕位置:BulletScreenSiteType枚举 左上:TOP_LEFT,左下:BOTTOM_LEFT,右上:TOP_RIGHT,右下:BOTTOM_RIGHT")
   private String bulletScreenSite;
   
   @ApiModelProperty(value = "热区配置")
   @IgnorePropertyJson
   private List<ImgComponentConfigDetailBO> hotConfigDetails;

   @ApiModelProperty(value = "挖孔区域配置")
   @IgnorePropertyJson
   private List<ImgComponentHoleConfigDetailBO> holeConfigDetails;

}
