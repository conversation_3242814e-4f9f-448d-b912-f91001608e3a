package com.mengxiang.mshop.cms.core.model.domain.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/29
 * @Description:
 */
@Data
public class TimeConfigBO {

    @ApiModelProperty(value = "生效方式 1=立即生效 2=定时生效")
    private Integer effectiveType;
    @ApiModelProperty(value = "定时类型 1=多时段 2=循环时段")
    private Integer timeType;
    @ApiModelProperty(value = "时段集")
    private List<TimeSlotBO> timeList;
    @ApiModelProperty(value = "定时周期")
    private TimeCycleBO timeCycle;
}
