package com.mengxiang.mshop.cms.core.model.request.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;



@Data
@ApiModel("内容检测")
public class ContentCheckRequest {

    @ApiModelProperty(notes = "检测内容 文本|图片地址|视频地址", required = true)
    private List<String> contents;

    @ApiModelProperty(notes = "检测类型 1:文字 2:图片 3:视频", required = true)
    private Integer type;
    
    @ApiModelProperty(notes = "操作人", required = true)
    private String operateBy;
}
