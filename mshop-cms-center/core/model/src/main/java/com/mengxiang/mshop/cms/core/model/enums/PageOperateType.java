package com.mengxiang.mshop.cms.core.model.enums;

/**
 * <AUTHOR>
 * @Date: 2023/4/18
 * @Description:
 */
public enum PageOperateType {

    /**
     * 保存并预览
     */
    SAVE(1, "保存并预览"),
    /**
     * 保存并发布
     */
    PUBLISH(2, "保存并发布");

    private Integer code;
    private String desc;


    PageOperateType(Integer code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
