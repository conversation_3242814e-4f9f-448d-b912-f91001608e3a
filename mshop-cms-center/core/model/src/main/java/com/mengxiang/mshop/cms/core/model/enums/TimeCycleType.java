package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 时间循环周期类型
 * <AUTHOR>
 */
public enum TimeCycleType {
    /**
     * 每周
     */
    WEEK(1, "WEEK", "每周"),
    /**
     * 每月
     */
    MONTH(2, "MONTH", "每月");
    
    private Integer code;
    
    private String name;
    
    private String desc;
    
    TimeCycleType(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
    
    public static TimeCycleType getEnumByCode(Integer code) {
        for (TimeCycleType item : TimeCycleType.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDesc() {
        return desc;
    }
}
