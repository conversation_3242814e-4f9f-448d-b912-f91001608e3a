package com.mengxiang.mshop.cms.core.model.domain.base;

import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.mshop.cms.core.model.enums.MarketTypeEnum;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 */
@NoArgsConstructor
@Data
public class PageBO extends BusinessModel {
    @ApiModelProperty("页面编号")
    private String pageCode;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("模版编号")
    private String templateCode;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("页面状态 ")
    private Integer status;

    @ApiModelProperty("页面标题")
    private String title;

    @ApiModelProperty("页面子标题")
    private String subTitle;

    @ApiModelProperty("页面背景颜色")
    private String backgroundColor;

    @ApiModelProperty(value = "页面背景图片")
    private String backgroundImgUrl;

    @ApiModelProperty("业务类型 1:代发 2:批发")
    private Integer bizType;

    @ApiModelProperty("搜索功能 0:关闭 1:开启")
    private Integer searchFlag;

    @ApiModelProperty("搜索框 0:收起 1:展开")
    private Integer searchBox;

    @ApiModelProperty("页面分享信息")
    private PageShareConfigBO shareConfig;

    @ApiModelProperty("所属者ID")
    private String ownerId;

    @ApiModelProperty("所属者类型")
    private String ownerType;

    @ApiModelProperty("端：h5、app 、小程序")
    private String channel;

    @ApiModelProperty("页面类型")
    private String type;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty(value = "生效方式")
    private TimeConfigBO timeConfig;

    @ApiModelProperty("组件集合")
    private String components;

    @ApiModelProperty("安全校验结果集合")
    private List<ContentCheckResponse> contentCheckResults;

    @ApiModelProperty("私密会场配置信息")
    private PrivateMarketConfigBO privateMarketConfig;

    @ApiModelProperty("会场类型 ，默认为普通会场")
    private MarketTypeEnum marketType=MarketTypeEnum.NORMAL;


}
