package com.mengxiang.mshop.cms.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Getter
public enum PageOwnerType {

    /**
     * 饷店
     */
    MENGXIANG("mengxiang", "饷店"),
    /**
     * saas租户
     */
    SAAS_TENANT("tenant", "saas租户"),
    /**
     * 商家
     */
    SUPPLIER("supplier", "商家"),
    /**
     * 系统
     */
    SYSTEM("system", "系统");

    private String ownerType;
    private String desc;

    PageOwnerType(String ownerType, String desc) {
        this.ownerType = ownerType;
        this.desc = desc;
    }

    public static PageOwnerType translate(String ownerType) {
        for (PageOwnerType typeEnum : PageOwnerType.values()) {
            if (typeEnum.getOwnerType().equals(ownerType)) {
                return typeEnum;
            }
        }
        return null;
    }

}
