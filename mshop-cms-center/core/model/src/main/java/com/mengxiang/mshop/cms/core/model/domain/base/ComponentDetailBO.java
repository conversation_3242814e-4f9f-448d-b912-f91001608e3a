package com.mengxiang.mshop.cms.core.model.domain.base;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class ComponentDetailBO extends ComponentStyleBO {

    @ApiModelProperty(value = "组件配置编号")
    @IgnorePropertyJson
    private String componentDetailCode;

    @ApiModelProperty(value = "组件详情排序")
    @IgnorePropertyJson
    private Integer order;
    
    @ApiModelProperty(value = "生效方式")
    @IgnorePropertyJson
    private TimeConfigBO timeConfig;

    @ApiModelProperty(value = "定向分群")
    @IgnorePropertyJson
    private DirectUserGroupBO directUserGroup;

}
