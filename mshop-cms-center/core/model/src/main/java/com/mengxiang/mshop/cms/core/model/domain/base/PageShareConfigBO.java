package com.mengxiang.mshop.cms.core.model.domain.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Data
public class PageShareConfigBO {

    @ApiModelProperty("转发功能 0:关闭 1:开启")
    private Integer shareSwitch;

    @ApiModelProperty("海报弹窗次数 日/次数")
    private Integer openPosterCount;

    @ApiModelProperty("分享卡片图片")
    private String shareCardImg;

    @ApiModelProperty("海报弹窗 0:关闭 1:开启")
    private Integer openPoster;

    @ApiModelProperty("转发海报文案")
    private List<String> forwardPosterText;

    @ApiModelProperty("分享主标题")
    private String forwardTitle;

    @ApiModelProperty("分享副标题")
    private String forwardSubTitle;

    @ApiModelProperty("转发海报图片")
    private String forwardPosterImg;

    @ApiModelProperty("分享小程序图片")
    private String shareMiniAppImg;

    @ApiModelProperty("转发按钮开关 0:关闭 1:开启")
    private Integer shareButtonSwitch = 1;


}
