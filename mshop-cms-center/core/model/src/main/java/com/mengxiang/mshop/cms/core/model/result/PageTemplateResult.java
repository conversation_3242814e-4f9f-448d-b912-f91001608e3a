package com.mengxiang.mshop.cms.core.model.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/27
 * @Description:
 */
@Data
public class PageTemplateResult {

    @ApiModelProperty("模版编号")
    private String templateCode;

    @ApiModelProperty("模版名称")
    private String templateName;

    @ApiModelProperty("模版描述")
    private String templateDesc;

    @ApiModelProperty("模版主图")
    private String templateImageUrl;

    @ApiModelProperty("页面使用范围 店铺主页:HOME  分类: CATEGORY  微页面:MICRO")
    private String pageUseType;

    @ApiModelProperty("组件使用规则")
    private List<TemplateComponentUseRule> componentUseRule;

    @Data
    public static class TemplateComponentUseRule {
        @ApiModelProperty(value = "组件类型")
        private String componentType;

        @ApiModelProperty(value = "权限列表 ALL、QUERY")
        private List<String> authorizationList;
    }
}
