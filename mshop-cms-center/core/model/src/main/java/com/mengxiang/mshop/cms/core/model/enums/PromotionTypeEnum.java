package com.mengxiang.mshop.cms.core.model.enums;

import com.google.common.collect.Lists;

import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/24
 * @Description:
 */
@Getter
public enum PromotionTypeEnum {
    /**
     * 全部商品
     */
    ALL_PRODUCT("ALL_PRODUCT", "全部商品", null),
    /**
     * 全部档期
     */
    ALL_ACTIVITY("ALL_ACTIVITY", "全部档期", null),
    /**
     * 一口价
     */
    FIXED_PRICE("FIXED_PRICE", "一口价", Lists.newArrayList("FIXED_PRICE")),
    /**
     * XN
     */
    NX("NX", "NX", Lists.newArrayList("N_PIECES_X_CNY","N_PIECES_X_DISCOUNT")),
    /**
     * 自主选品
     */
    SECKILL("SECKILL", "秒杀", Lists.newArrayList("SECKILL")),
    /**
     * 规则选品
     */
    COUPON("COUPON", "优惠券", Lists.newArrayList("COUPON"));

    private String code;
    private String desc;
    private List<String> marketingToolsTypeList;


    PromotionTypeEnum(String code, String desc,List<String> marketingToolsTypeList) {
        this.code = code;
        this.desc = desc;
        this.marketingToolsTypeList = marketingToolsTypeList;
    }
    public static List<String> findMarketingByCode(String code) {
        for (PromotionTypeEnum typeEnum : PromotionTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.marketingToolsTypeList;
            }
        }
        return null;
    }
}
