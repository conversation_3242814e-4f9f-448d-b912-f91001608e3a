package com.mengxiang.mshop.cms.core.model.request.workflow;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 请求参数
 */
@Data
public class WorkflowInfoVO {

    private String workbenchType;

    private Map<String, Object> optionalParamMap;

    /**
     * 提交审批的用户ID
     */
    private String userId;

    /**
     * 提交审批的名称
     */
    private String userName;

    /**
     * 类目长
     */
    private List<String> categoryLeaderList;
}
