package com.mengxiang.mshop.cms.core.model.enums;

/**
 * <AUTHOR>
 * @Date: 2023/5/29
 * @Description: 导航组件
 */
public enum NavigationStyleTypeEnum {

    /**
     * 普通导航
     */
    DEFAULT("0", "普通导航"),
    /**
     * 电梯导航
     */
    ARCHOR("1", "电梯导航");

    private String code;
    private String desc;


    NavigationStyleTypeEnum(String code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static NavigationStyleTypeEnum getEnumByCode(String code) {
        for (NavigationStyleTypeEnum item : NavigationStyleTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
