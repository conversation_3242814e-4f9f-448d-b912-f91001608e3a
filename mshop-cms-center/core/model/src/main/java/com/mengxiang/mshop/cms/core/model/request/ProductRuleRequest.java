package com.mengxiang.mshop.cms.core.model.request;

import com.mengxiang.base.common.model.request.PagingRequest;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductRuleRequest extends PagingRequest {

    private String ruleCode;

    /**
     * 关联id souceType=1传 普通会场频道的channelIdDetailId  souceType=2传 超品会场频道的hotSaleChannelDetailId
     */
    private String sourceId;

    /**
     * 关联类型 1=普通会场频道 2=超级会场频道 3=爆款
     */
    private Byte sourceType;

    /**
     * 选品中心的规则id
     */
    private String originalRuleId;

    /**
     * 选品中心的规则名称
     */
    private String originalRuleName;

    private String ruleName;
    /**
     * 规则状态 1=未生效 2=已生效 3=已失效
     */
    private Byte ruleStatus;

    /**
     * 规则类型 1=人工选品 2=规则选品
     */
    private Byte ruleType;

    /**
     * 排序类型 1=销量 2=转发量 3=上新 4=折扣 5=省赚 6=浏览量 7=价格 8=综合 9=人工
     */
    private Byte sortType;

    private String creator;

    @ApiModelProperty("生效方式")
    private TimeConfigBO timeConfig;

}
