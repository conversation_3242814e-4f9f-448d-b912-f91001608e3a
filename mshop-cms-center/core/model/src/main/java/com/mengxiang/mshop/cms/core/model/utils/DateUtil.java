package com.mengxiang.mshop.cms.core.model.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;


public class DateUtil {

	public static final String YMDSMD="yyyy-MM-dd ss:HH:dd";

	/**
	 * 获取时间差 秒
	 * @param hour
	 * @return
	 */
	public static long getTomorrowTimeOut (int hour) {
		DateTime tomorrow = cn.hutool.core.date.DateUtil.tomorrow();
		tomorrow.setField(DateField.HOUR_OF_DAY, hour);
		tomorrow.setField(DateField.MINUTE, 0);
		tomorrow.setField(DateField.SECOND, 0);
		tomorrow.setField(DateField.MILLISECOND, 0);
		// 计算当前时间与明天凌晨3点的时间差
		long timeout = cn.hutool.core.date.DateUtil.betweenMs(cn.hutool.core.date.DateUtil.date(), tomorrow);
		return Convert.convertTime(timeout, TimeUnit.MILLISECONDS, TimeUnit.SECONDS);
	}

    public static boolean compareCurrentMonth(Date compareDate){
        Calendar createCal = Calendar.getInstance();
        createCal.setTime(compareDate);

        Calendar currentCal = Calendar.getInstance();
        currentCal.setTime(new Date());

        boolean isSameYear = createCal.get(Calendar.YEAR)==currentCal.get(Calendar.YEAR);
        boolean isSameMonth = createCal.get(Calendar.MONTH)==currentCal.get(Calendar.MONTH);

        return isSameYear==isSameMonth;
    }

	public static Date parseDate(String dateStr){
    	if(StringUtils.isBlank(dateStr)){
    		return null;
		}

		if (dateStr.length() == 16) {  // Check if the input format is yyyy-MM-dd HH:mm
			dateStr += ":00";  // Append ":00" to adjust the format to yyyy-MM-dd HH:mm:ss
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			return sdf.parse(dateStr);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date parseDate2(String dateStr){
		if(StringUtils.isBlank(dateStr)){
			return null;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			return sdf.parse(dateStr);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date parseDate3(String dateStr){
		if(StringUtils.isBlank(dateStr)){
			return null;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
		try {
			return sdf.parse(dateStr);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	// 获取今年是哪一年
	public static Integer getNowYear() {
		Date date = new Date();
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		return Integer.valueOf(gc.get(1));
	}

	public static String dateToStr(Date date,String fomart){
		SimpleDateFormat sdf = new SimpleDateFormat(fomart);
		return sdf.format(date);
	}

	public static String dateToStrLong(Date dateDate) {
    	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(dateDate);
		return dateString;
	 }

	public static String dateToStr2(Date dateDate) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateString = formatter.format(dateDate);
		return dateString;
	}

	public static String dateToStr3(Date dateDate) {
		SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
		String dateString = formatter.format(dateDate);
		return dateString;
	}


	/**
	 * 获取上个月年月（yyyy-MM）
	 * @param month
	 * @return
	 */
	public static String getLastMonth(String month){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date date = null;
		try {
			date = sdf.parse(month + "-" + "01");
		} catch (Exception e) {
			e.printStackTrace();
		}

		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.MONTH, -1);
		if ((c.get(Calendar.MONTH) + 1)> 10){
			String lastMonth = c.get(Calendar.YEAR) + "-"+ (c.get(Calendar.MONTH) + 1);
			return lastMonth;
		}
		String lastMonth = c.get(Calendar.YEAR) + "-0"+ (c.get(Calendar.MONTH) + 1);
		return lastMonth;
	}


	/**
	 * 获取昨天日期
	 * @return
	 */
	public static String getLastDay(){
		Date date=new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -1);
		date = calendar.getTime();
		SimpleDateFormat format= new SimpleDateFormat("yyyy-MM-dd");
		String dateString = format.format(date);
		return dateString;
	}


	/**
	 * 获取昨天日期
	 * @return
	 */
	public static Date getLastHour(int hour){
		Date date=new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.HOUR, hour);
		date = calendar.getTime();
		return date;
	}

	public static void main(String[] args) {
		System.out.println(dateToStrLong(getLastHour(-48)));
	}

	public static String getNowTime(){
		Date date = new Date();
		DateFormat df = new SimpleDateFormat("hh:mm:ss");
		return df.format(date).toString();
	}

	/**
	 * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致,startTime必须要比endTime小
	 *
	 * @param nowTimeString 当前时间
	 * @param startTimeString 开始时间
	 * @param endTimeString 结束时间
	 * @return
	 * <AUTHOR>
	 */
	public static boolean isEffectiveDate(String nowTimeString, String startTimeString, String endTimeString) {
		Date nowTime = parseDate3(nowTimeString);
		Date startTime = parseDate3(startTimeString);
		Date endTime = parseDate3(endTimeString);
		if (nowTime.getTime() == startTime.getTime()
				|| nowTime.getTime() == endTime.getTime()) {
			return true;
		}

		Calendar date = Calendar.getInstance();
		date.setTime(nowTime);

		Calendar begin = Calendar.getInstance();
		begin.setTime(startTime);

		Calendar end = Calendar.getInstance();
		end.setTime(endTime);

		if (date.after(begin) && date.before(end)) {
			return true;
		} else {
			return false;
		}
	}

	public static String getLastMonthDate() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar c = Calendar.getInstance();
		c.setTime(new Date());
		c.add(Calendar.MONTH, -1);
		Date m = c.getTime();
		return sdf.format(m);
	}
	public static String getLastMonths() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		Calendar c = Calendar.getInstance();
		c.setTime(new Date());
		c.add(Calendar.MONTH, -1);
		Date m = c.getTime();
		return sdf.format(m);
	}

	public static Date getTodayStartTime(){
		Calendar calendar = Calendar.getInstance();
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
				00, 00, 00);
		return calendar.getTime();
	}

	public static Date getTodayEndTime(){
		Calendar calendar = Calendar.getInstance();
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
				23, 59, 59);
		return calendar.getTime();
	}

    /**
     * 如果date1为null,今天的一年后的日期
     * @return String //yyyy-MM-dd
     */
    public static Date getNextYear(){
        Date date = new Date();//取时间
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, 1);//把日期往后增加一年.整数往后推,负数往前移动
        date=calendar.getTime();
        return date;
    }


	/**
	 * 获取偏移后时间戳 忽略时分秒
	 * @return String //yyyy-MM-dd
	 */
	public static long getExcursionTime( Integer excursionDay ){
		Date date=new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.add(Calendar.DATE, excursionDay);
		date = calendar.getTime();
		return date.getTime();
	}

	/**
	 * 获取时间 忽略时分秒
	 * @return String //yyyy-MM-dd
	 */
	public static String getStrTime(String dateTime ){
		if( StringUtils.isBlank(dateTime) || !dateTime.contains(" ") ){
			return  dateTime;
		}
		return dateTime.split(" ")[0];
	}
    
    /**
     * 获取当前往后五分钟
     * @return String //yyyy-MM-dd
     */
    public static Date getTimeOut(){
        Date date= new Date();//取时间
        System.out.println(date.toString());
        Calendar   calendar   =   new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, 5);//把日期往后增加五分钟.整数往后推,负数往前移动
        date=calendar.getTime();
        return date;
    }

	/**
	 * 获取当前往后五分钟
	 * @return String //yyyy-MM-dd
	 */
	public static Date getTokenTimeOut(){
		Date date= new Date();//取时间
		System.out.println(date.toString());
		Calendar   calendar   =   new GregorianCalendar();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, 1);//把日期往后增加五分钟.整数往后推,负数往前移动
		date=calendar.getTime();
		return date;
	}

    public static int getOffsetMinutes(int minute)
	{
		long current = DateUtil.getLongNow()/1000;
		current += minute * 60;
		return (int)current;
	}

	public static Date getOffsetDate(int minute)
	{
		long current = DateUtil.getLongNow()/1000;
		current += minute * 60;
		return new Date(current);
	}
    
	public static long getLongNow(){
		return date2Long(getDateNow());
	}
	
	public static Date getDateNow(){
		Calendar cal=Calendar.getInstance();
		return cal.getTime();
	}

	public static boolean compareTimeIsTrue(String s1, String s2, String s3){
		try {
			if (s1.indexOf(":")<0||s1.indexOf(":")<0) {
				System.out.println("格式不正确");
			}else{
				String[]array1 = s1.split(":");
				int total1 = Integer.valueOf(array1[0])*3600+Integer.valueOf(array1[1])*60+Integer.valueOf(array1[2]);
				String[]array2 = s2.split(":");
				int total2 = Integer.valueOf(array2[0])*3600+Integer.valueOf(array2[1])*60+Integer.valueOf(array2[2]);
				String[]array3 = s3.split(":");
				int total3 = Integer.valueOf(array3[0])*3600+Integer.valueOf(array3[1])*60+Integer.valueOf(array3[2]);
				if(total3-total1 >0 && total2-total3>0){
					return true;
				}else{
					return false;
				}
			}
		} catch (NumberFormatException e) {
			// TODO Auto-generated catch block
			return true;
		}
		return false;

	}


	public static String getNowDate(){
		SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar cal=Calendar.getInstance();
		String format1 = format.format(cal.getTime());
		return format1;
	}

	public static String getNowDate2(){
		SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd");
		Calendar cal=Calendar.getInstance();
		String format1 = format.format(cal.getTime());
		return format1;
	}

	public static long date2Long(Date oDate){
		Calendar cal=Calendar.getInstance();
		cal.setTime(oDate);
		return cal.getTimeInMillis();
	}

	public static Date getDateByTimestamp(Long timestamp) {
	    SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
	    String d = format.format(timestamp);  
	    Date date = null;
		try {
			date = format.parse(d);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date;
	}

	public static Date getDateByTimestampV2(Long timestamp) {
		SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
		String d = format.format(timestamp);
		Date date = null;
		try {
			date = format.parse(d);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date;
	}

	public static Date getDateByTime() {
		SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm");
		String d = format.format(new Date());
		Date date = null;
		try {
			date = format.parse(d);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date;
	}

	/**
	 * 获取当前前后移动 X 分钟
	 */
	public static Date getTimePutForward(int minute){
		Date date= new Date();//取时间
		Calendar   calendar   =   new GregorianCalendar();
		calendar.setTime(date);
		calendar.add(Calendar.MINUTE, minute);//把日期往后增加 X 分钟.整数往后推,负数往前移动
		date=calendar.getTime();
		return date;
	}

	public static Long getCurrentDayTime() {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		Date d=new Date();
		String currentDayStr = sdf.format(d);
		try {
			return sdf.parse(currentDayStr).getTime();
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}
	public static String getDayByTimestamp(Long timestamp) {
		SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd");
		String d = format.format(timestamp);
		return d;
	}

	public static String getCurrentYearMonth() {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
		Date d=new Date();
		return sdf.format(d);
	}
	public static String getYearMonth(Date d) {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
		return sdf.format(d);
	}
	
	public static String getMonth(int offset)
	{
		Calendar c = Calendar.getInstance();
		c.add(Calendar.MONTH, offset);
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
		String time = format.format(c.getTime());
		return time;
	}

	/**
	 * 获取某日期到当月月底剩余天数(含当天)
	 */
	public static int getLeftDays(Date d) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(d);
		int activeDate = calendar.get(Calendar.DATE);// 获取激活日期
		calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
		int lastDate = calendar.get(Calendar.DATE);// 获取当月最后一天
		int leftDays = lastDate - activeDate + 1;
		return leftDays;

	}

	/**
            * 获取两个时间的相差天数
	 * @param beforeDate
	 * @param afterDate
	 * @return
             */
    public static long getDifferDay(Date beforeDate,Date afterDate) {
    	try {
			long beforeTime = beforeDate.getTime();
			long afterTime = afterDate.getTime();
			long timesDis=afterTime-beforeTime;
			return  (timesDis / (1000 * 60 * 60 * 24));
		}catch (Exception e){
    		return 30L;
		}

    }

    /**
     * @Description:    获取两个时间的相差小时数
     * <AUTHOR>
     * @CreateDate:     2019/5/9 21:55
     */
    public static long getDifferHour(Date beforeDate, Date afterDate) {

		try {
			long beforeTime = (beforeDate.getTime())/(1000*60*60);
			long afterTime = (afterDate.getTime())/(1000*60*60);
			long timesDis = afterTime - beforeTime;
			return timesDis > 0 ? timesDis + 1 : 24L;
		} catch (Exception e) {
			return 24L;
		}
	}

    /**
     * @Description:    获取两个时间的相差分钟数
     * <AUTHOR>
     * @CreateDate:     2019/5/9 21:55
     */
    public static long getDifferMinute2(Date beforeDate, Date afterDate) {

		try {
			long beforeTime = (beforeDate.getTime())/(1000);
			long afterTime = (afterDate.getTime())/(1000);
			long timesDis = afterTime - beforeTime;
			return timesDis > 0 ? timesDis : 60;
		} catch (Exception e) {
			return 60; // 报错返回 60 分钟
		}
	}

	/**
	 * @Description:    获取两个时间的相差分钟数
	 * <AUTHOR>
	 * @CreateDate:     2019/5/9 21:55
	 * @param endPeriod
	 */
	public static long getDifferMinute(String endPeriod) {
		try {
			String endTime = getNowDate2() + " " + endPeriod + ":59";
			long nowTime = System.currentTimeMillis();
			if(nowTime>=DateUtil.parseDate(endTime).getTime()){
				return 1;
			}else{
                long minutes = ( DateUtil.parseDate(endTime).getTime() - nowTime ) / 1000;
				return minutes > 0 ? minutes : 60;
			}
		} catch (Exception e) {
			return  60;
		}
	}

	/**
	 * @Description:    获取两个时间的相差分钟数
	 * <AUTHOR>
	 * @CreateDate:     2019/5/9 21:55
	 */
	public static long getDifferMilliscond(Date beforeDate, Date afterDate) {

		try {
			long beforeTime = (beforeDate.getTime());
			long afterTime = (afterDate.getTime());
			long timesDis = afterTime - beforeTime;
			return timesDis > 0 ? timesDis : timesDis;
		} catch (Exception e) {
			return 6000; // 报错返回 60 分钟
		}
	}

	/**
	 * 时间周期比较
	 * @param beforeDate
	 * @param now
	 * @param dayNum
	 * @return
	 */
	public static boolean compareDatePeriod(Date beforeDate,Date now,int dayNum){
		Calendar calendar = Calendar.getInstance();  //得到日历
		calendar.setTime(now);//把当前时间赋给日历
		calendar.add(Calendar.DAY_OF_MONTH, dayNum);
		Date before7days = calendar.getTime();   //得到7天前的时间
		if(before7days.getTime() < beforeDate.getTime()){
			return true;
		}else{
			return false;
		}
	}
	
	public static Date getOffsetDay(Date orign, int dayNum) {
		Calendar calendar = Calendar.getInstance();  //得到日历
		calendar.setTime(orign);//把当前时间赋给日历
		calendar.add(Calendar.DAY_OF_MONTH, dayNum);
		Date after = calendar.getTime();   //得到n天前(后)的时间
		return after;
	}
	
	public static Date getOffsetMonth(Date orign, int monthNum) {
		Calendar calendar = Calendar.getInstance();  //得到日历
		calendar.setTime(orign);//把当前时间赋给日历
		calendar.add(Calendar.MONTH, monthNum);
		Date after = calendar.getTime();   //得到n月前(后)的时间
		return after;
	}

	/**
	 * 是否在本月dayNum号之前
	 * @param dayNum
	 * @return
	 */
	public static boolean isBeforeDateOfMonth(int dayNum){
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH, 0);
		calendar.set(Calendar.DAY_OF_MONTH,dayNum);//设置为1号,当前日期既为本月第N天
		Date date = calendar.getTime();
		if(new Date().after(date)){
			return false;
		}
		return true;
	}


	/**
	 * 时间推前 X 分钟
	 * @return
	 */
	public static Date pushFewMinutes(String nowTime, int i) {
		Date afterDate = new Date(DateUtil.parseDate(nowTime).getTime() - i * 60000);
		return afterDate;
	}


    public static String getStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(todayStart.getTime());
    }

    public static String getEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(todayEnd.getTime());
    }

	public static boolean getTimeStatus(String startTime, long nowTime, String endTime) {
		if(DateUtil.parseDate(startTime).getTime() < nowTime && DateUtil.parseDate(endTime).getTime() > nowTime ){
			return true;
		}
		return false;
	}

	/**
	 * 获取两个日期之间的所有日期集合
	 * @param start
	 * @param end
	 * @return
	 */
	public static List<String> getBetweenDates(Date start, Date end) {
		List<String> result = new ArrayList<String>();
		Calendar tempStart = Calendar.getInstance();
		tempStart.setTime(start);
		tempStart.add(Calendar.DATE,-1);
		tempStart.add(Calendar.DAY_OF_YEAR, 1);

		Calendar tempEnd = Calendar.getInstance();
		tempEnd.setTime(end);
		tempEnd.add(Calendar.DATE,1);
		while (tempStart.before(tempEnd)) {
			// parseDate2( tempStart.getTime() );
			String timeStr = dateToStr(tempStart.getTime(), "yyyy-MM-dd");
			result.add(timeStr);
			tempStart.add(Calendar.DAY_OF_YEAR, 1);
		}

		return result;
	}



	public static boolean getTimeStatus2(Date startTime, long nowTime, Date endTime) {
		if(startTime.getTime() < nowTime && endTime.getTime() > nowTime ){
			return true;
		}
		return false;
	}

	/**
	 * 给date加days天 <br/>
	 * 创建者：马飞虎 <br/>
	 * 创建时间：2011-4-20 上午11:54:23 <br/>
	 *
	 * @param date
	 * @return
	 */
	public static Date addDay(Date date,int days){
		if(date == null){
			return date;
		}
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DAY_OF_MONTH,days);
		return c.getTime();
	}

	/**
	 * 将aDate格式化为字符串
	 *
	 * @param aDate
	 * @return 返回aDate按照默认DatePattern格式化后的字符串
	 */
	public static String getDate(Date aDate){
		SimpleDateFormat df;
		String returnValue = "";
		if(aDate != null){
			df = new SimpleDateFormat("yyyy-MM-dd");
			returnValue = df.format(aDate);
		}
		return (returnValue);
	}

	/**
	 * 查询距离当前多少天，time的时间
	 * time 7:00:00 days -1  查询出昨天的7点
	 * @param days
	 * @param time
	 * @return
	 */
	public static Date getDistanceNow(int days,String time){
		if(time==null || "".equals(time)){
			return null;
		}
		String toLiveTime = DateUtil.getDate(new Date()) +" "+ time;
		Date date = parseDate(toLiveTime);
		return DateUtil.addDay(date, days);
	}

	public static Date getNowDirectTime(String time){
		if(time==null || "".equals(time)){
			return null;
		}
		String toLiveTime = DateUtil.getDate(new Date()) +" "+ time;
		Date date = parseDate(toLiveTime);
		return date;
	}

	/**
	 * 判断当前时间是否在[startTime, endTime]区间,startTime必须要比endTime小
	 *
	 * @param nowTime 当前时间
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 * <AUTHOR>
	 */
	public static boolean isEnable(Date nowTime, Date startTime, Date endTime) {

		if( Objects.isNull(nowTime) || Objects.isNull(startTime) || Objects.isNull(startTime) ){
			return Boolean.FALSE;
		}

		if( nowTime.getTime() < startTime.getTime() || nowTime.getTime() >= endTime.getTime() ){
			return Boolean.FALSE;
		}

		return Boolean.TRUE;
	}

	public static boolean checkDateByWeek(Integer week,String startTimeString, String endTimeString){
		Calendar now = Calendar.getInstance();
		Integer nowWeek = now.get(Calendar.DAY_OF_WEEK)-1;
		if(nowWeek.equals(week) || nowWeek+7 == week){
			return isEffectiveDate(dateToStr3(new Date()),startTimeString+":00",endTimeString+":59");
		}
		return Boolean.FALSE;
	}

	public static boolean checkDateByMonth(Integer month,String startTimeString, String endTimeString){
		Calendar now = Calendar.getInstance();
		Integer nowMonth= now.get(Calendar.DAY_OF_MONTH);
		if(nowMonth.equals(month)){
			return isEffectiveDate(dateToStr3(new Date()),startTimeString+":00",endTimeString+":59");
		}
		return Boolean.FALSE;
	}
}
