package com.mengxiang.mshop.cms.core.model.request.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 业务单据提交时候-发起工作流请求-工作流
 */
@Data
public class WorkflowRequest {

    @ApiModelProperty("流程定义key;开发约定的流程定义key")
    private String procKey;

    /**
     * eventForm 流程节点变更的表单内容.
     */
    @ApiModelProperty("流程节点变更的表单内容;业务需要展示的核心4个字段")
    private WorkflowEventForm eventForm;

    /**
     * businessKey 业务key.
     */
    @ApiModelProperty("业务key;业务自己可以识别的唯一id")
    private String businessKey;

    @ApiModelProperty("其他选填参数")
    private Map<String,Object> optionalParamMap;

    @ApiModelProperty("父流程编号")
    private String pProcInstCode;

    @ApiModelProperty("阿米巴处理用户")
    private Long ombUserId;

}
