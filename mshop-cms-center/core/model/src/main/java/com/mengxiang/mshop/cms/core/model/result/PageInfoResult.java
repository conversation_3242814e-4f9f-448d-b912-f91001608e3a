package com.mengxiang.mshop.cms.core.model.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询返回页面信息
 * <AUTHOR>
 */
@Data
public class PageInfoResult {
    @ApiModelProperty("页面编号")
    private String pageCode;

    @ApiModelProperty("选品中心规则id")
    private String ruleCode;

    @ApiModelProperty("商家店铺id")
    private String merchantShopId;

    @ApiModelProperty("秒杀详情信息")
    private SecKillConfigDetailResult secKillDetailResult;


}
