package com.mengxiang.mshop.cms.core.model.request;

import com.mengxiang.base.common.model.request.PagingRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 组件创建
 */
@Data
public class OperationLogRequest extends PagingRequest {

    @ApiModelProperty(value = "业务编号",required = true)
    private String bizCode;
    
    @ApiModelProperty(value = "业务类型:OperationLogBizTypeEnum",required = true)
    private Integer bizType;

    @ApiModelProperty(value = "行为")
    private String action;
}
