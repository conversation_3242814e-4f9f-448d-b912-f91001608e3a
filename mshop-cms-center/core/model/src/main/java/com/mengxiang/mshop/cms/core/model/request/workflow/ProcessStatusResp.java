package com.mengxiang.mshop.cms.core.model.request.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 流程状态响应对象
 */
@Data
public class ProcessStatusResp {

    /**
     * 页面编号
     */
    @ApiModelProperty("页面编号")
    private String pageCode;

    /**
     * 生效版本号
     */
    @ApiModelProperty("版本号")
    private String version;
    /**
     * 业务key
     */
    @ApiModelProperty("业务key")
    private String businessKey;


    /**
     * 流程编码
     */
    @ApiModelProperty("流程编码")
    private String procInstCode;
    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private Integer processStatus;
    /**
     * 流程状态名称
     */
    @ApiModelProperty("流程状态名称")
    private String processStatusName;


}
