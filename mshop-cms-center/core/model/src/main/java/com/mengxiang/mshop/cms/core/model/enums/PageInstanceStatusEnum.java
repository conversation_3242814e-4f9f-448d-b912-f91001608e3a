package com.mengxiang.mshop.cms.core.model.enums;


import java.util.Objects;

/**
 * 页面状态值枚举
 * <AUTHOR>
 */
public enum PageInstanceStatusEnum {

    /**
     * 页面状态：0 : 初始化
     */
    INIT(0,"init", "初始化"),
    /**
     * 页面状态： 1.待发布
     */
    DRAFT(1,"draft", "待发布"),
    /**
     * 页面状态：2.审批中
     */
    APPROVAL(2,"approval", "审批中"),
    /**
     * 页面状态：3:发布失败
     */
    FAIL(3,"fail", "发布失败"),
    /**
     * 页面状态： 4: 已生效
     */
    PUBLISH(4,"published", "已生效"),
    /**
     * 页面状态：5:页面失效
     */
    DISABLED(5,"disabled", "页面失效"),

    /**
     * 页面状态： 6: 已发布待生效
     */
    EXECUTORY(6,"executory", "已发布待生效"),
    ;


    private Integer code;
    private String name;

    private String desc;

    PageInstanceStatusEnum(Integer code , String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }
    
    public static PageInstanceStatusEnum getEnumByName(String name) {
        for (PageInstanceStatusEnum item : PageInstanceStatusEnum.values()) {
            if (item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static PageInstanceStatusEnum getEnumByCode(Integer code) {
        for (PageInstanceStatusEnum item : PageInstanceStatusEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static boolean isShowStatus(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        if (code.equals(PageInstanceStatusEnum.PUBLISH.getCode()) ||
                code.equals(PageInstanceStatusEnum.FAIL.getCode()) ||
                code.equals(PageInstanceStatusEnum.APPROVAL.getCode())) {
            return true;
        }
        return false;
    }
    
    public String getName() {
        return name;
    }
    
}
