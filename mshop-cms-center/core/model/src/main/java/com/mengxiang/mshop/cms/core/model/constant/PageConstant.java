package com.mengxiang.mshop.cms.core.model.constant;


/**
 * 页面枚举
 * <AUTHOR>
 */
public class PageConstant {

    /**
     * 页面发布失败 机审 默认失败原因
     */
    public static final String PAGE_FAIL_CONTENT_MESSAGE = "上传视频不合规";

    /**
     * 页面发布失败 人审 默认失败原因
     */
    public static final String PAGE_FAIL_WORK_MESSAGE = "页面发布审核流不通过";

    public static final String BASE_FIX = "mshop_cms_center:";

    /**
     * 根据页面类型 缓存key "mshop_cms_center:pageType:{pageType}:{ownerId}
     */
    public static final String PAGE_TYPE_CACHE_KEY = BASE_FIX + "pageType:%s:%s";

    public static final String PAGE_TYPE_OWNER_HASH_KEY = BASE_FIX + "pageType:pageOwner";

    /**
     * 根据pageCode 缓存key
     */
    public static final String  PAGE_CODE_CACHE_KEY = BASE_FIX + "pageCode:%s";

    /**
     * 根据pageCode + version 缓存key
     */
    public static final String  PAGE_CODE_VERSION_CACHE_KEY = "version:%s";

    public static final String  CURRENT_PAGE_CACHE_KEY = "currentPage";

    public static final String  WORK_BENCH_NOTICE_FLAG = BASE_FIX +"workBenchNoticeFlag:";

    public static final String  WORK_BENCH_CANCEL_FLAG = BASE_FIX + "workBenchCancelFlag:";


    /**
     * 资源位缓存: 资源位类型+ownerType+ownerId+tenantId
     */
    public static final String  RESOURCE_CACHE_KEY = BASE_FIX + "resource:%s:%s:%s:%s";

    /**
     * 私密会场策略缓存key
     */
    public static final String  PRIVATE_MARKET_RULE_CACHE_KEY = BASE_FIX + "private_market_rule";


}
