package com.mengxiang.mshop.cms.core.model.domain.base;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class ComponentBO extends ComponentStyleBO {

    @ApiModelProperty(value = "组件编号")
    @IgnorePropertyJson
    private String componentCode;

    @ApiModelProperty(value = "组件名称")
    private String componentName;

    @ApiModelProperty(value = "前端组件id")
    @IgnorePropertyJson
    private String componentId;

    @ApiModelProperty(value = "版本号")
    @IgnorePropertyJson
    private String version;

    @ApiModelProperty(value = "组件类型")
    @IgnorePropertyJson
    private ComponentTypeEnum type;

    @ApiModelProperty(value = "组件排序")
    @IgnorePropertyJson
    private Integer order;
    
    @ApiModelProperty(value = "生效方式")
    @IgnorePropertyJson
    private TimeConfigBO timeConfig;

    @ApiModelProperty(value = "权限列表 ALL、QUERY")
    @IgnorePropertyJson
    private List<String> authorizationList;

    @ApiModelProperty(value = "定向分群")
    @IgnorePropertyJson
    private DirectUserGroupBO directUserGroup;
}
