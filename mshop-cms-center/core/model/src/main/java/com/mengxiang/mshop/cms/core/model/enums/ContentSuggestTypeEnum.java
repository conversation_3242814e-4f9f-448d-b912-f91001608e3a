package com.mengxiang.mshop.cms.core.model.enums;


/**
 * 内容建议
 * <AUTHOR>
 */
public enum ContentSuggestTypeEnum {

    /**
     * 未知原因
     */
    UN_KNOWN("un_known", "未知原因"),

    /**
     * 通过
     */
    PASS("pass", "通过"),

    /**
     * 审核
     */
    REVIEW("review", "审核"),

    /**
     * 阻塞
     */
    BLOCK("block", "阻塞"),

    /**
     * 处理中
     */
    PROCESSING("processing", "处理中");
    
    private String suggesst;
    
    private String desc;
    
    ContentSuggestTypeEnum(String suggesst, String desc) {
        this.suggesst = suggesst;
        this.desc = desc;
    }
    
    public static ContentSuggestTypeEnum getEnumBySuggesst(String suggesst) {
        for (ContentSuggestTypeEnum item : ContentSuggestTypeEnum.values()) {
            if (item.getSuggesst().equals(suggesst)) {
                return item;
            }
        }
        return null;
    }
    
    public String getSuggesst() {
        return suggesst;
    }
    
    public String getDesc() {
        return desc;
    }
}
