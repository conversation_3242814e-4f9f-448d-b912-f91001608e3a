package com.mengxiang.mshop.cms.core.model.request.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/8 15:35
 */
@Data
public class WorkflowEventForm {

    @ApiModelProperty("业务需要展示字段-标识1 如姓名")
    private String key1;

    @ApiModelProperty("业务需要展示字段-标识值1  如张三")
    private String value1;

    @ApiModelProperty("业务需要展示字段-标识1 如姓名")
    private String key2;

    @ApiModelProperty("业务需要展示字段-标识值1  如张三")
    private String value2;

    @ApiModelProperty("业务需要展示字段-标识1 如姓名")
    private String key3;

    @ApiModelProperty("业务需要展示字段-标识值1  如张三")
    private String value3;

    @ApiModelProperty("业务需要展示字段-标识1 如姓名")
    private String key4;

    @ApiModelProperty("业务需要展示字段-标识值1  如张三")
    private String value4;
}
