package com.mengxiang.mshop.cms.core.model.domain.seckill;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class SeckillComponentBO extends ComponentBO {

   @ApiModelProperty(value = "布局方式 11: 一行一  12:一行二 13:一行三")
   private String layoutType;

   @ApiModelProperty(value = "默认展示商品数")
   private Integer pageSize = 10;

   @ApiModelProperty(value = "查看更多商品数")
   private Integer morePageSize = 10;

   @ApiModelProperty(value = "秒杀类型 状态: STATUS 时间段: TIMESLOT")
   private String ruleType;

   @ApiModelProperty(value = "秒杀配置详情")
   @IgnorePropertyJson
   private List<SeckillComponentConfigDetailBO> seckillConfigDetails;
}
