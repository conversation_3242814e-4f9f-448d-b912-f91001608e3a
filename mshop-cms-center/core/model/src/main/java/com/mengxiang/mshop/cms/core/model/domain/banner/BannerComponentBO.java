package com.mengxiang.mshop.cms.core.model.domain.banner;

import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Data
public class BannerComponentBO extends ComponentBO {

   @ApiModelProperty(value = "图片宽度 必选")
   private Double width;

   @ApiModelProperty(value = "图片高度 可选")
   private Double height;

   @ApiModelProperty(value = "容器内轮播背景图")
   private String backgroundImgUrl;

   @ApiModelProperty(value = "背景图 0:无图 1:有图")
   private Integer hasBackgroundImg;
   
   @ApiModelProperty(value = "轮播样式：1容器内轮播、2图片轮播")
   private String carouselType;
   
   @ApiModelProperty(value = "轮播配置")
   @IgnorePropertyJson
   private List<BannerComponentConfigDetailBO> carouselConfigDetails;
}
