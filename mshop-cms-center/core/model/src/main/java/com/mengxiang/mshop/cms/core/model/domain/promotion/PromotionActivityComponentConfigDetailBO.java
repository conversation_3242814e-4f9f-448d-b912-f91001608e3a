package com.mengxiang.mshop.cms.core.model.domain.promotion;

import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PromotionActivityComponentConfigDetailBO extends ComponentDetailBO {

    @ApiModelProperty(value = "组件详情配置类型")
    @IgnorePropertyJson
    private String detailType = ComponentDetailTypeEnum.PROMOTIONACTIVITY.getCode();

    @ApiModelProperty(value = "活动名称")
    private String promotionActivityName;

    @ApiModelProperty(value = "活动类型")
    private String promotionActivityType;

}
