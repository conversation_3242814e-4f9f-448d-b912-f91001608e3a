package com.mengxiang.mshop.cms.core.service.processor.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.DirectUserGroupBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.NavigationStyleTypeEnum;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/5/11
 * @Description: 导航组件
 */
@Service
@Slf4j
public class NavigationComponentConverter extends AbstractBusinessConverter<PageContext, BusinessModel> {
    @Override
    protected InnerResult<BusinessModel> convert(PageContext context) {
        List<NavigationComponentBO> navigationComponents = context.getNavigationComponents();
        Map<String, MshopComponentInstanceDo> archorMap = context.getArchorIdMap();
        Map<String, MshopComponentInstanceDo> archorCodeMap = context.getArchorCodeMap();
        List<MshopComponentInstanceDo> components = context.getComponentInstances();
        List<MshopComponentInstanceDetailDo> componentDetails = context.getComponentInstanceDetails();
        if (CollectionUtils.isEmpty(navigationComponents)) {
            return new InnerResult<>(Boolean.TRUE, null);
        }

        navigationComponents.forEach(navigation -> {
            if (navigation.getStyleType().equals(NavigationStyleTypeEnum.DEFAULT.getCode()) ) {
                return;
            }
            MshopComponentInstanceDo componentInstanceDo = components.stream()
                    .filter(x -> x.getComponentCode().equals(navigation.getComponentCode()))
                    .findFirst()
                    .orElse(null);
            //处理包含组件
            if (CollectionUtils.isNotEmpty(navigation.getComponentCodes())) {
                navigation.getComponentCodes().stream().forEach(code -> {
                    MshopComponentInstanceDo mshopComponentInstanceDo = archorMap.get(code.getComponentId());
                    if (Objects.nonNull(mshopComponentInstanceDo)) {
                        if (StringUtils.isEmpty(code.getComponentCode()) && StringUtils.isNotEmpty(code.getComponentId())) {
                            code.setComponentCode(mshopComponentInstanceDo.getComponentCode());
                        }
                    }
                });
                if (Objects.nonNull(componentInstanceDo)) {
                    NavigationComponentBO navigationComponent = JSONObject.parseObject(componentInstanceDo.getMetaConfig(), NavigationComponentBO.class);
                    navigationComponent.setComponentCodes(navigation.getComponentCodes());
                    String metaConfig = getJsonStrConfig(navigationComponent);
                    componentInstanceDo.setMetaConfig(metaConfig);
                }
            }

            //处理每个tab定位组件
            navigation.getNavigationConfigDetails().stream().forEach(detail -> {
                        setArchorComponentCode(context,detail);
                        if (StringUtils.isNotEmpty(detail.getArchorComponentCode())) {
                            MshopComponentInstanceDo archorComponentInstance= archorCodeMap.get(detail.getArchorComponentCode());
                            if (Objects.nonNull(archorComponentInstance)) {
                                TimeConfigBO timeConfigBO = JSONObject.parseObject(archorComponentInstance.getTimeConfig(), TimeConfigBO.class);
                                MshopComponentInstanceDetailDo mshopComponentInstanceDetailDo = componentDetails.stream()
                                        .filter(x -> x.getConfigDetailCode().equals(detail.getComponentDetailCode()))
                                        .findFirst()
                                        .orElse(null);
                                detail.setTimeConfig(timeConfigBO);
                                mshopComponentInstanceDetailDo.setTimeConfig(archorComponentInstance.getTimeConfig());
                                if (StringUtils.isNotEmpty(archorComponentInstance.getDirectUserGroup())) {
                                    DirectUserGroupBO directUserGroupBO = JSON.parseObject(archorComponentInstance.getDirectUserGroup(), DirectUserGroupBO.class);
                                    detail.setDirectUserGroup(directUserGroupBO);
                                    mshopComponentInstanceDetailDo.setDirectUserGroup(archorComponentInstance.getDirectUserGroup());
                                }
                            }
                        }
                    });
        });
        return new InnerResult<>(Boolean.TRUE, null);
    }

    private void setArchorComponentCode (PageContext context,NavigationComponentConfigDetailBO detail) {
        Map<String, MshopComponentInstanceDo> archorMap = context.getArchorIdMap();
        List<MshopComponentInstanceDetailDo> componentDetails = context.getComponentInstanceDetails();
        if (StringUtils.isEmpty(detail.getArchorComponentCode()) && StringUtils.isNotEmpty(detail.getComponentId())) {
            MshopComponentInstanceDo mshopComponentInstanceDo = archorMap.get(detail.getComponentId());
            if (Objects.nonNull(mshopComponentInstanceDo)) {
                detail.setArchorComponentCode(mshopComponentInstanceDo.getComponentCode());
                MshopComponentInstanceDetailDo mshopComponentInstanceDetailDo = componentDetails.stream()
                        .filter(x -> x.getConfigDetailCode().equals(detail.getComponentDetailCode()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(mshopComponentInstanceDetailDo)) {
                    String configDetail = mshopComponentInstanceDetailDo.getConfigDetail();
                    NavigationComponentConfigDetailBO navigationComponentConfigDetailBO = JSONObject.parseObject(configDetail, NavigationComponentConfigDetailBO.class);
                    navigationComponentConfigDetailBO.setArchorComponentCode(mshopComponentInstanceDo.getComponentCode());
                    String metaConfig = getJsonStrConfig(navigationComponentConfigDetailBO);
                    mshopComponentInstanceDetailDo.setConfigDetail(metaConfig);
                }
            }
        }
    }
}
