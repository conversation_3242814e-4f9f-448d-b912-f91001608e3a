package com.mengxiang.mshop.cms.core.service.business.impl;

import cn.hutool.core.lang.TypeReference;
import com.akucun.mshop.common.util.BeanCopyUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.lock.base.api.ILock;
import com.mengxiang.base.common.lock.base.api.ILockFactory;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dao.MshopResourceComponentDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.ResourceQueryVO;
import com.mengxiang.mshop.cms.common.util.RedisRepository;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.*;
import com.mengxiang.mshop.cms.core.model.enums.*;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.mengxiang.mshop.cms.core.service.business.PageQueryService;
import com.mengxiang.mshop.cms.core.service.business.ResourceService;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.util.StreamUtils;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private MshopResourceComponentDao resourceComponentDao;

    @Autowired
    private ILockFactory iLockFactory;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Autowired
    private PageCacheService pageCacheService;

    @Autowired
    private PageQueryService pageQueryService;

    /**
     * 平台页面
     */
    private final static String PLATFORM_PAGE = "2";

    /**
     * 分页查询资源位
     *
     * @return
     */
    @Override
    public Pagination<BaseResourceBO> selectPage(ResourceRequest req) {
        //避免全表查询需要增加查询条件的校验
        Pagination<BaseResourceBO> pageData = new Pagination<>();
        try {
            PageHelper.startPage(req.getPageIndex(), req.getPageSize());
            // 获取查询对象
            ResourceQueryVO resourceQueryVO = BeanCopyUtil.copy(req, ResourceQueryVO.class);
            List<MshopResourceComponentDo> list = resourceComponentDao.selectList(resourceQueryVO);

            PageInfo<MshopPageInstanceDo> doPageInfo = new PageInfo(list);

            List<BaseResourceBO> finalList = list.stream().map(x -> {
                BaseResourceBO bo = new BaseResourceBO();
                bo.setId(x.getId());
                bo.setName(x.getName());
                bo.setCategoryId(x.getCategoryId());
                bo.setResourceType(x.getResourceType());
                bo.setResourceName(ResourceTypeEnum.getEnumNameByCode(x.getResourceType()));
                bo.setResourcePageName(ResourcePageTypeEnum.getEnumNameByCode(x.getResourcePageType()));
                bo.setResourcePageType(x.getResourcePageType());
                bo.setChannelList(Arrays.asList(x.getChannel().split(",")));
                bo.setChannelName(ResourceChannelEnum.getEnumNameByCode(bo.getChannelList()));
                bo.setStatus(x.getStatus());
                bo.setStatusName(ResourceStatusEnum.getEnumNameByCode(x.getStatus()));
                TimeConfigBO timeConfigBO = JSON.parseObject(x.getTimeConfig(), TimeConfigBO.class);
                bo.setTimeConfig(timeConfigBO);
                return bo;
            }).collect(Collectors.toList());

            pageData = new Pagination<>(req, doPageInfo.getTotal(), finalList);
        } catch (Exception ex) {
            log.error("selectPage error req:{}", JSON.toJSONString(req), ex);
        } finally {
            PageHelper.clearPage();
        }
        return pageData;
    }

    /**
     * 保存金刚位替换数据
     */
    @Override
    public void saveDiamondResource(DiamondResourceBO bo) {
        ILock iLock = iLockFactory.getLock(RedisRepository.REDIS_PREFIX + RedisRepository.PAGE_OPRATE + bo.getResourceType() + bo.getOwnerId() + bo.getOwnerType());
        try {
            boolean lock = iLock.lock(RedisRepository.DEFAULT_LOCK_TIMEOUT, RedisRepository.DEFAULT_LOCK_TIMEOUT);
            if (!lock) {
                throw new BusinessException(CmsErrorCodeEnum.SAVE_PAGE_DRAFT_ERROR.getErrorMsg());
            }
            ResourceQueryVO resourceQueryVO = new ResourceQueryVO();
            resourceQueryVO.setResourceType(ResourceTypeEnum.DIAMOND.getCode());
            resourceQueryVO.setTenantId(bo.getTenantId());
            resourceQueryVO.setOwnerId(bo.getOwnerId());
            resourceQueryVO.setOwnerType(bo.getOwnerType());
            resourceQueryVO.setStatus(ResourceStatusEnum.PUBLISH.getCode());
            List<MshopResourceComponentDo> list = resourceComponentDao.selectList(resourceQueryVO);
            if (CollectionUtils.isNotEmpty(list)) {
                //修改
                MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
                resourceComponentDo.setConfigDetail(JSON.toJSONString(bo.getCarouselConfigDetails()));
                resourceComponentDo.setUpdateBy(bo.getCreateBy());
                resourceComponentDo.setId(list.get(0).getId());
                resourceComponentDao.updateById(resourceComponentDo);
            } else {
                //新增
                MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
                resourceComponentDo.setName(bo.getName());
                resourceComponentDo.setResourceType(ResourceTypeEnum.DIAMOND.getCode());
                resourceComponentDo.setStatus(ResourceStatusEnum.PUBLISH.getCode());
                resourceComponentDo.setConfigDetail(JSON.toJSONString(bo.getCarouselConfigDetails()));
                resourceComponentDo.setChannel(String.join(ResourceChannelEnum.SAA_S_APP.getCode(), ResourceChannelEnum.SAA_S_APPLETS.getCode()));
                resourceComponentDo.setResourcePageType(ResourcePageTypeEnum.HOME.getCode());
                resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
                resourceComponentDo.setCreateBy(bo.getCreateBy());
                resourceComponentDo.setUpdateBy(bo.getCreateBy());
                resourceComponentDo.setTenantId(bo.getTenantId());
                resourceComponentDo.setOwnerId(bo.getOwnerId());
                resourceComponentDo.setOwnerType(bo.getOwnerType());
                resourceComponentDao.insert(resourceComponentDo);
            }
        } catch (IllegalArgumentException | BusinessException ige) {
            throw ige;
        } finally {
            if (Objects.nonNull(iLock)) {
                iLock.releaseLock();
            }
        }
    }


    /**
     * 查询金刚位替换数据
     */
    @Override
    public List<DiamondResourceBO.DiamondResourceConfig> findDiamondResourceList(DiamondResourceBO bo) {
        ResourceQueryVO resourceQueryVO = new ResourceQueryVO();
        resourceQueryVO.setResourceType(ResourceTypeEnum.DIAMOND.getCode());
        resourceQueryVO.setTenantId(bo.getTenantId());
        resourceQueryVO.setOwnerId(bo.getOwnerId());
        resourceQueryVO.setOwnerType(bo.getOwnerType());
        resourceQueryVO.setStatus(ResourceStatusEnum.PUBLISH.getCode());
        resourceQueryVO.setCategoryId(null);
        resourceQueryVO.setResourcePageType(null);
        List<MshopResourceComponentDo> list = resourceComponentDao.selectList(resourceQueryVO);
        if(CollectionUtils.isNotEmpty(list)){
            String configDetail = list.get(0).getConfigDetail();
            if(StringUtils.isNotBlank(configDetail)){
                return JSONObject.parseArray(list.get(0).getConfigDetail(),DiamondResourceBO.DiamondResourceConfig.class);
            }
        }
        return Lists.newArrayList();
    }


    /**
     * 保存banner组件
     */
    @Override
    public void saveBannerResource(BannerResourceBO bo) {
            if(Objects.nonNull(bo.getId())) {
                //校验数据权限
                findAndCheckResource(bo);
                //修改
                MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
                resourceComponentDo.setConfigDetail(JSON.toJSONString(bo.getCarouselConfigDetails()));
                resourceComponentDo.setChannel(bo.getChannelList().stream().collect(Collectors.joining(",")));
                resourceComponentDo.setResourcePageType(bo.getResourcePageType());
                resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
                resourceComponentDo.setUpdateBy(bo.getCreateBy());
                resourceComponentDo.setId(bo.getId());
                resourceComponentDo.setCategoryId(bo.getCategoryId());
                resourceComponentDo.setStatus(convertStatus(bo.getTimeConfig()));
                resourceComponentDo.setName(bo.getName());
                resourceComponentDao.updateById(resourceComponentDo);
            } else {
                //新增
                MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
                resourceComponentDo.setName(bo.getName());
                resourceComponentDo.setResourceType(ResourceTypeEnum.BANNER.getCode());
                resourceComponentDo.setStatus(convertStatus(bo.getTimeConfig()));
                resourceComponentDo.setConfigDetail(JSON.toJSONString(bo.getCarouselConfigDetails()));
                resourceComponentDo.setChannel(bo.getChannelList().stream().collect(Collectors.joining(",")));
                resourceComponentDo.setResourcePageType(bo.getResourcePageType());
                resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
                resourceComponentDo.setCreateBy(bo.getCreateBy());
                resourceComponentDo.setUpdateBy(bo.getCreateBy());
                resourceComponentDo.setTenantId(bo.getTenantId());
                resourceComponentDo.setOwnerId(bo.getOwnerId());
                resourceComponentDo.setOwnerType(bo.getOwnerType());
                resourceComponentDo.setCategoryId(bo.getCategoryId());
                resourceComponentDao.insert(resourceComponentDo);
            }
    }



    /**
     * 查询banner组件
     */
    @Override
    public BannerResourceBO getBannerResourceById(BannerResourceBO bo) {
        if(Objects.nonNull(bo.getId())) {
            //校验数据权限
            MshopResourceComponentDo resourceComponentDo =  findAndCheckResource(bo);
            //修改
            BannerResourceBO result = BeanCopyUtil.copy(resourceComponentDo,BannerResourceBO.class);
            result.setCarouselConfigDetails(JSONObject.parseArray(resourceComponentDo.getConfigDetail()
                    , BannerResourceBO.BannerResourceConfig.class));
            result.setChannelList(Arrays.asList(resourceComponentDo.getChannel().split(",")));
            TimeConfigBO timeConfigBO = JSON.parseObject(resourceComponentDo.getTimeConfig(), TimeConfigBO.class);
            result.setTimeConfig(timeConfigBO);
            return result;
        }
        return null;
    }





    /**
     * 保存banner组件
     */
    @Override
    public void saveNavigationResource(NavigationResourceBO bo) {
        //校验关联的会场是否有效
        for(NavigationResourceBO.ResourceConfig config:bo.getTabConfigDetails()){
            //检查页面是否还生效
            if(Objects.equals(TargetType.PAGE.getType(),config.getTargetType())){
                //校验会场3.0页面
                Optional<String> pageString = pageCacheService.getPageJsonByCode(config.getTargetId());
                String pageInfo = pageString .orElse("");
                if(StringUtils.isBlank(pageInfo)){
                    throw new BusinessException(config.getTabName()+"关联的页面未生效");
                }
            }
        }


        ILock iLock = iLockFactory.getLock(RedisRepository.REDIS_PREFIX + RedisRepository.PAGE_OPRATE + bo.getResourceType() + bo.getOwnerId() + bo.getOwnerType());
        try {
            boolean lock = iLock.lock(RedisRepository.DEFAULT_LOCK_TIMEOUT, RedisRepository.DEFAULT_LOCK_TIMEOUT);
            if (!lock) {
                throw new BusinessException(CmsErrorCodeEnum.SAVE_PAGE_DRAFT_ERROR.getErrorMsg());
            }
            //查询是是否有数据
            MshopResourceComponentDo mshopResourceComponentDo =  resourceComponentDao.queryByIdxResourceType(ResourceTypeEnum.NAVIGATION.getCode(),bo.getTenantId(),bo.getOwnerType(),bo.getOwnerId());


            if(Objects.nonNull(mshopResourceComponentDo)) {
                //修改
                MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
                resourceComponentDo.setConfigDetail(JSON.toJSONString(bo.getTabConfigDetails()));
                resourceComponentDo.setChannel(bo.getChannelList().stream().collect(Collectors.joining(",")));
                resourceComponentDo.setResourcePageType(bo.getResourcePageType());
                resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
                resourceComponentDo.setUpdateBy(bo.getCreateBy());
                resourceComponentDo.setId(mshopResourceComponentDo.getId());
                resourceComponentDo.setCategoryId(bo.getCategoryId());
                resourceComponentDo.setStatus(convertStatus(bo.getTimeConfig()));
                resourceComponentDo.setName(bo.getName());
                resourceComponentDao.updateById(resourceComponentDo);
            } else {
                //新增
                MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
                resourceComponentDo.setName(bo.getName());
                resourceComponentDo.setResourceType(ResourceTypeEnum.NAVIGATION.getCode());
                resourceComponentDo.setStatus(convertStatus(bo.getTimeConfig()));
                resourceComponentDo.setConfigDetail(JSON.toJSONString(bo.getTabConfigDetails()));
                resourceComponentDo.setChannel(bo.getChannelList().stream().collect(Collectors.joining(",")));
                resourceComponentDo.setResourcePageType(bo.getResourcePageType());
                resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
                resourceComponentDo.setCreateBy(bo.getCreateBy());
                resourceComponentDo.setUpdateBy(bo.getCreateBy());
                resourceComponentDo.setTenantId(bo.getTenantId());
                resourceComponentDo.setOwnerId(bo.getOwnerId());
                resourceComponentDo.setOwnerType(bo.getOwnerType());
                resourceComponentDo.setCategoryId(bo.getCategoryId());
                resourceComponentDao.insert(resourceComponentDo);
            }
            //更新缓存
            updateNavigationCache(bo.getTenantId());
        } catch (IllegalArgumentException | BusinessException ige) {
            throw ige;
        } finally {
            if (Objects.nonNull(iLock)) {
                iLock.releaseLock();
            }
        }
    }



    /**
     * 查询导航组件
     */
    @Override
    public NavigationResourceBO getNavigationResourceById(NavigationResourceBO bo) {
        MshopResourceComponentDo resourceComponentDo =  resourceComponentDao.queryByIdxResourceType(ResourceTypeEnum.NAVIGATION.getCode(),bo.getTenantId(),bo.getOwnerType(),bo.getOwnerId());

        if(Objects.nonNull(resourceComponentDo)) {
            NavigationResourceBO result = BeanCopyUtil.copy(resourceComponentDo,NavigationResourceBO.class);
            List<NavigationResourceBO.ResourceConfig> configList = JSONObject.parseArray(resourceComponentDo.getConfigDetail()
                    , NavigationResourceBO.ResourceConfig.class);
            List<String>  pageCode = configList.stream().filter(x->Objects.equals(TargetType.PAGE.getType(),x.getTargetType())).map(NavigationResourceBO.ResourceConfig::getTargetId).collect(Collectors.toList());
            Map<String,String> pageMap = Maps.newHashMap();
            PageSearchRequest pageSearchRequest = new PageSearchRequest();
            pageSearchRequest.setPageCodeList(pageCode);
            pageSearchRequest.setTenantId(resourceComponentDo.getTenantId());
            pageSearchRequest.setOwnerId(resourceComponentDo.getTenantId());
            Result<Pagination<PageSelectResult>> pageSelectResult =  pageQueryService.pageSelectV2(pageSearchRequest);
            if(Objects.nonNull(pageSelectResult)
                    && pageSelectResult.isSuccess()
                    && Objects.nonNull(pageSelectResult.getData())
                    && CollectionUtils.isNotEmpty(pageSelectResult.getData().getResult())){
                pageMap = pageSelectResult.getData().getResult().stream().collect(Collectors.toMap(PageSelectResult::getPageCode,PageSelectResult::getName,(e1,e2)->e2));
            }
            Map<String, String> finalPageMap = pageMap;
            configList.forEach(x->{
                x.setTargetName(finalPageMap.get(x.getTargetId()));
            });
            result.setTabConfigDetails(configList);
            result.setChannelList(Arrays.asList(resourceComponentDo.getChannel().split(",")));
            TimeConfigBO timeConfigBO = JSON.parseObject(resourceComponentDo.getTimeConfig(), TimeConfigBO.class);
            result.setTimeConfig(timeConfigBO);

            result.setOrderValue(resourceComponentDo.getOrderValue());
            result.setResourceType(resourceComponentDo.getResourceType());
            return result;
        }
        return null;
    }

    @Override
    public NavigationResourceBO getPlatPageDataByTenantInfo(NavigationResourceBO req) {
        if (ObjectUtils.isEmpty(req)|| StringUtils.isBlank(req.getTenantId()) || StringUtils.isBlank(req.getOwnerId()) || StringUtils.isBlank(req.getOwnerType()) || StringUtils.isBlank(req.getResourceType())){
            throw new BusinessException("参数异常");
        }

        MshopResourceComponentDo res = resourceComponentDao.getPlatPageDataByTenantInfo(req.getTenantId(), req.getOwnerId(), req.getOwnerType(), req.getResourceType());
        AssertUtil.isTrue(Objects.nonNull(res), "未查询到数据");
        AssertUtil.isTrue(StringUtils.isNotEmpty(res.getConfigDetail()), "配置信息异常");

        NavigationResourceBO copy = BeanCopyUtil.copy(res, NavigationResourceBO.class);
        List<NavigationResourceBO.ResourceConfig> configList= JSON.parseObject(res.getConfigDetail(), new TypeReference<List<NavigationResourceBO.ResourceConfig>>() {});
        copy.setTabConfigDetails(configList);
        return copy;
    }

    @Override
    public void savePlatPageData(NavigationResourceBO req) {

        if (ObjectUtils.isEmpty(req) ||StringUtils.isBlank(req.getTenantId()) || StringUtils.isBlank(req.getOwnerId()) || StringUtils.isBlank(req.getOwnerType()) || CollectionUtils.isEmpty(req.getTabConfigDetails())) {
            return;
        }

        // 通过租户信息，有则修改，无则新增
        MshopResourceComponentDo existingData = resourceComponentDao.getPlatPageDataByTenantInfo(req.getTenantId(),req.getOwnerId(), req.getOwnerType(), ResourceTypeEnum.NAVIGATION.getCode());

        if (ObjectUtils.isEmpty(existingData)) {
            MshopResourceComponentDo copy = BeanCopyUtil.copy(req, MshopResourceComponentDo.class);
            copy.setConfigDetail(JSON.toJSONString(req.getTabConfigDetails()));
            resourceComponentDao.insert(copy);
        } else {
            MshopResourceComponentDo copy = BeanCopyUtil.copy(req, MshopResourceComponentDo.class);
            String existingConfigDetail = existingData.getConfigDetail();
            String newConfigDetail = JSON.toJSONString(req.getTabConfigDetails());

            // 解析、合并配置详情 => 字符串
            List<Map<String, Object>> existingConfigList = JSON.parseObject(existingConfigDetail, new TypeReference<List<Map<String, Object>>>() {});
            List<Map<String, Object>> newConfigList = JSON.parseObject(newConfigDetail, new TypeReference<List<Map<String, Object>>>() {});
            existingConfigList.addAll(newConfigList);
            String mergedConfigDetail = JSON.toJSONString(existingConfigList);
            copy.setConfigDetail(mergedConfigDetail);
            resourceComponentDao.updateStatusByTenantId(copy);
        }
    }

    /**
     * 保存开机广告
     */
    @Override
    public void saveStartupAdvertisement(StartupAdvertisementBO bo) {
        if(Objects.nonNull(bo.getId())) {
            //校验数据权限
            findAndCheckResource(bo);
            //修改
            MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
            resourceComponentDo.setConfigDetail(StreamUtils.getJsonStrConfig(bo));
            resourceComponentDo.setResourcePageType(bo.getResourcePageType());
            resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
            resourceComponentDo.setUpdateBy(bo.getCreateBy());
            resourceComponentDo.setId(bo.getId());
            resourceComponentDo.setName(bo.getName());
            resourceComponentDo.setStatus(convertStatus(bo.getTimeConfig()));
            resourceComponentDao.updateById(resourceComponentDo);
        } else {
            //新增
            MshopResourceComponentDo resourceComponentDo = new MshopResourceComponentDo();
            resourceComponentDo.setName(bo.getName());
            resourceComponentDo.setResourceType(ResourceTypeEnum.STARTUPADVERTISEMENT.getCode());
            resourceComponentDo.setStatus(convertStatus(bo.getTimeConfig()));
            resourceComponentDo.setConfigDetail(StreamUtils.getJsonStrConfig(bo));
            resourceComponentDo.setChannel(ResourceChannelEnum.SAA_S_APP.getCode());
            resourceComponentDo.setResourcePageType(ResourcePageTypeEnum.HOME.getCode());
            resourceComponentDo.setTimeConfig(JSON.toJSONString(bo.getTimeConfig()));
            resourceComponentDo.setCreateBy(bo.getCreateBy());
            resourceComponentDo.setUpdateBy(bo.getCreateBy());
            resourceComponentDo.setTenantId(bo.getTenantId());
            resourceComponentDo.setOwnerId(bo.getOwnerId());
            resourceComponentDo.setOwnerType(bo.getOwnerType());
            resourceComponentDao.insert(resourceComponentDo);
        }
    }

    /**
     * 查询开机广告
     */
    @Override
    public StartupAdvertisementBO getStartupAdvertisementById(StartupAdvertisementBO bo) {
        if(Objects.nonNull(bo.getId())) {
            //校验数据权限
            MshopResourceComponentDo resourceComponentDo =  findAndCheckResource(bo);
            //修改
            StartupAdvertisementBO result = BeanCopyUtil.copy(resourceComponentDo,StartupAdvertisementBO.class);
            StartupAdvertisementBO detail = JSONObject.parseObject(resourceComponentDo.getConfigDetail()
                    , StartupAdvertisementBO.class);
            result.setCarouselConfigDetails(detail.getCarouselConfigDetails());
            result.setIosImgUrl1(detail.getIosImgUrl1());
            result.setIosImgUrl2(detail.getIosImgUrl2());
            result.setAndroidImgUrl(detail.getAndroidImgUrl());
            result.setChannelList(Arrays.asList(resourceComponentDo.getChannel().split(",")));
            TimeConfigBO timeConfigBO = JSON.parseObject(resourceComponentDo.getTimeConfig(), TimeConfigBO.class);
            result.setTimeConfig(timeConfigBO);
            return result;
        }
        return null;
    }

    /**
     * 资源位置状态处理
     */
    @Override
    public void updateStatus(BaseResourceBO bo){
        //入参校验
        if(!Objects.equals(bo.getStatus(),ResourceStatusEnum.PUBLISH.getCode())
           && !Objects.equals(bo.getStatus(),ResourceStatusEnum.DISABLED.getCode())){
            throw new BusinessException("505","状态不存在");
        }
        if(Objects.nonNull(bo.getId())) {
            //校验数据权限
            findAndCheckResource(bo);
            //修改生效配置 ,直接上线和下线都需要修改定时配置
            TimeConfigBO timeConfigBO = new TimeConfigBO();
            timeConfigBO.setEffectiveType(1);
            resourceComponentDao.updateStatusById(bo.getStatus(),null,bo.getId(),JSON.toJSONString(timeConfigBO));
        }
    }

    /**
     * 资源位置状态处理
     */
    @Override
    public void deleteById(BaseResourceBO bo){
        //入参校验
        if(Objects.nonNull(bo.getId())) {
            //校验数据权限
            findAndCheckResource(bo);

            resourceComponentDao.updateStatusById(null,1,bo.getId(),null);
        }
    }

    /**
     * 获取状态
     * @param timeConfig
     * @return
     */
    private Integer convertStatus(TimeConfigBO timeConfig){
        // 1=未生效 2=已生效 3=已失效.
       Integer status = TimeConfigUtils.findRuleStatus(timeConfig);
       if(Objects.equals(status,1)){
           return ResourceStatusEnum.EXECUTORY.getCode();
       }else if(Objects.equals(status,2)){
           return ResourceStatusEnum.PUBLISH.getCode();
       }else{
           return ResourceStatusEnum.DISABLED.getCode();
       }
    }

    /**
     * 查询和检查组件
     * @param bo
     * @return
     */
    private MshopResourceComponentDo findAndCheckResource(BaseResourceBO bo){
        if(Objects.isNull(bo.getId())){
            throw new BusinessException("500","数据不存在");
        }
        MshopResourceComponentDo componentDo = resourceComponentDao.getById(bo.getId());
        if(Objects.isNull(componentDo)){
            throw new BusinessException("501","数据不存在");
        }
        if(!Objects.equals(bo.getTenantId(),componentDo.getTenantId())){
            throw new BusinessException("502","数据不存在");
        }
        if(!Objects.equals(bo.getOwnerType(),componentDo.getOwnerType())){
            throw new BusinessException("503","数据不存在");
        }
        if(!Objects.equals(bo.getOwnerId(),componentDo.getOwnerId())){
            throw new BusinessException("504","数据不存在");
        }
        return componentDo;
    }

    /**
     * 处理定时生效
     */
    @Override
    public void checkStatus() {
        List<Integer> req = Lists.newArrayList();
        req.add(ResourceStatusEnum.PUBLISH.getCode());
        req.add(ResourceStatusEnum.EXECUTORY.getCode());
        List<MshopResourceComponentDo> list = resourceComponentDao.selectListByStatus(req);
        if(CollectionUtils.isNotEmpty(list)){
            for (MshopResourceComponentDo componentDo:list){
                 TimeConfigBO timeConfigBO = JSON.parseObject(componentDo.getTimeConfig(), TimeConfigBO.class);
                 Integer status = convertStatus(timeConfigBO);
                 if(!Objects.equals(status,componentDo.getStatus())){
                     //状态不同更新最新状态
                     resourceComponentDao.updateStatusById(status,null,componentDo.getId(),null);
                 }
            }
        }
    }

    /**
     * 更新导航缓存
     */
    public void updateNavigationCache(String tenantId){
        ResourceQueryVO pageQueryVO = new ResourceQueryVO();
        pageQueryVO.setStatus(ResourceStatusEnum.PUBLISH.getCode());
        pageQueryVO.setResourceType(ResourceTypeEnum.NAVIGATION.getCode());
        if(StringUtils.isNotBlank(tenantId)){
            pageQueryVO.setTenantId(tenantId);
            pageQueryVO.setOwnerId(tenantId);
            pageQueryVO.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
        }
        List<MshopResourceComponentDo> resourceComponentDoList = resourceComponentDao.selectList(pageQueryVO);
        if(CollectionUtils.isEmpty(resourceComponentDoList)){
            return;
        }
        for(MshopResourceComponentDo componentDo:resourceComponentDoList){
            List<NavigationResourceBO.ResourceConfig> configList = JSONObject.parseArray(componentDo.getConfigDetail(), NavigationResourceBO.ResourceConfig.class);
            if(CollectionUtils.isEmpty(configList)){
                continue;
            }
            List<NavigationResourceBO.ResourceConfig> saveCacheList = Lists.newArrayList();
            for(NavigationResourceBO.ResourceConfig config:configList){
                // fixme :在这里将新加一个type字段，当是平台数据，直接加到saveList中
                if(ObjectUtils.isNotEmpty(config) && ObjectUtils.isNotEmpty(config.getSourceType())
                    && PLATFORM_PAGE.equalsIgnoreCase(config.getSourceType())){
                    saveCacheList.add(config);
                    continue;
                }
                //检查开关
                if(!Objects.equals(ResourceStatusEnum.PUBLISH.getCode(),config.getStatus())){
                    continue;
                }
                //检查页面是否还生效
                if(Objects.equals(TargetType.PAGE.getType(),config.getTargetType())){
                    //校验会场3.0页面
                    Optional<String> pageString = pageCacheService.getPageJsonByCode(config.getTargetId());
                    String pageInfo = pageString .orElse("");
                    if(StringUtils.isBlank(pageInfo)){
                        continue;
                    }
                }
                saveCacheList.add(config);
            }

            //更新缓存
            String key = String.format(PageConstant.RESOURCE_CACHE_KEY,ResourceTypeEnum.NAVIGATION, componentDo.getOwnerType(),componentDo.getOwnerId(),componentDo.getTenantId());
            redisTemplate.delete(key);
            String jsonString = JSON.toJSONString(saveCacheList);
            redisTemplate.opsForValue().set(key, jsonString, 2, TimeUnit.DAYS);
        }
    }

}
