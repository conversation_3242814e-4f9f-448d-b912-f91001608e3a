package com.mengxiang.mshop.cms.core.service.business.detail.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.profit.ProfitAdditionalComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.profit.ProfitAdditionalComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/4
 * @Description:
 */
@Service
public class ProfitAdditionalInfoServiceImpl extends AbstractComponentInfoService<ProfitAdditionalComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    protected ProfitAdditionalComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        ProfitAdditionalComponentBO profitAdditionl = JSON.parseObject(metaConfig, ProfitAdditionalComponentBO.class);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, ProfitAdditionalComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(profitAdditionl::setProductRuleConfigDetails);
        if (CollectionUtil.isNotEmpty(profitAdditionl.getProductRuleConfigDetails())) {
            profitAdditionl.getProductRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return profitAdditionl;
    }

    @Override
    protected ProfitAdditionalComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        ProfitAdditionalComponentBO profitAdditionl = JSON.parseObject(metaConfig, ProfitAdditionalComponentBO.class);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, ProfitAdditionalComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(profitAdditionl::setProductRuleConfigDetails);
        if (CollectionUtil.isNotEmpty(profitAdditionl.getProductRuleConfigDetails())) {
            profitAdditionl.getProductRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return profitAdditionl;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public ProfitAdditionalComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        ProfitAdditionalComponentBO profitAdditionl = JSON.parseObject(componentStr, ProfitAdditionalComponentBO.class);
        if (Objects.nonNull(profitAdditionl.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(profitAdditionl.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }

        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(profitAdditionl.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(profitAdditionl.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        if (CollectionUtil.isNotEmpty(profitAdditionl.getProductRuleConfigDetails())) {
            profitAdditionl.getProductRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return profitAdditionl;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.PROFITADDITIONAL;
    }


    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
