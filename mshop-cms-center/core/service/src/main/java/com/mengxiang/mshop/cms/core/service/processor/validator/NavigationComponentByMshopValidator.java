package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeSlotBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.DataRuleTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.NavigationStyleTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.TimeEffectiveType;
import com.mengxiang.mshop.cms.core.model.enums.TimeType;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 导航组件校验
 *
 * <AUTHOR>
 */
@Component
public class NavigationComponentByMshopValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {
    
    @Override
    protected InnerResult<BusinessModel> validator(PageContext context) {
        //解析组件
        if (CollectionUtils.isEmpty(context.getNavigationComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        List<NavigationComponentBO> navigations = context.getNavigationComponents();
        for (NavigationComponentBO navigation : navigations) {
            paramVerify(navigation);
        }
        return new InnerResult<BusinessModel>(true, null);
    }
    
    
    /**
     * 参数校验
     */
    private void paramVerify(NavigationComponentBO navigation) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(navigation.getNavigationConfigDetails()), "导航组件 导航栏配置 不能为空");
        Preconditions.checkArgument(navigation.getNavigationConfigDetails().size() <= 10, "档期组件 导航栏配置 不能超过10个");
        Preconditions.checkArgument(Objects.nonNull(navigation.getNavigationType()), "导航组件 导航类型 不能为空");
        Preconditions.checkArgument(Objects.nonNull(navigation.getDirectUserGroup()) && Objects.nonNull(navigation.getDirectUserGroup().getDirectShowType()) , "导航组件 人群设置 不能为空");
        navigation.getNavigationConfigDetails().forEach(detail -> {
            if (navigation.getStyleType().equals(NavigationStyleTypeEnum.DEFAULT.getCode())) {
                Preconditions.checkArgument(StringUtils.isNotEmpty(detail.getType()), "导航组件 导航配置类型 不能为空");
                Preconditions.checkArgument(StringUtils.isNotEmpty(detail.getRuleType()), "导航组件 选品规则 不能为空");
                if (DataRuleTypeEnum.SELF.getCode().equals(detail.getRuleType())) {
                    Preconditions.checkArgument(CollectionUtils.isNotEmpty(detail.getBusinessIds()), "导航组件 自主选择的业务ID集合 不能为空");
                } else if (DataRuleTypeEnum.RULE.getCode().equals(detail.getRuleType())) {
                    Preconditions.checkArgument(CollectionUtils.isNotEmpty(detail.getRules()), "导航组件 选品规则 不能为空");
                    Preconditions.checkArgument(detail.getRules().size() <= 10, "导航组件 选品规则 不能超过10个");
                    List<TimeSlotBO> timeList = detail.getRules().stream()
                            .map(config -> {
                                TimeConfigBO timeConfigBO = config.getTimeConfig();
                                Preconditions.checkArgument(Objects.nonNull(timeConfigBO), "导航组件 规则生效方式 不能为空");
                                if (timeConfigBO.getEffectiveType().equals(TimeEffectiveType.REGULAR_TIME.getCode()) && timeConfigBO.getTimeType().equals(TimeType.MULTIPLE.getCode())) {
                                    Preconditions.checkArgument(timeConfigBO.getTimeList().stream().allMatch(t -> StringUtils.isNotEmpty(t.getStartTime()) && StringUtils.isNotEmpty(t.getEndTime())), "导航组件 规则生效方式多时段 不能为空");
                                }
                                if (TimeConfigUtils.isMultiple.test(timeConfigBO)) {
                                    return timeConfigBO.getTimeList();
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .flatMap(List::stream)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(timeList)) {
                        Preconditions.checkArgument(!TimeConfigUtils.isTimeSlotsOverlap(timeList), "档期组件 多个规则时间段不能重叠");
                    }
                }
            } else {
                if (StringUtils.isEmpty(detail.getComponentId()) && StringUtils.isEmpty(detail.getArchorComponentCode())) {
                    throw new IllegalArgumentException("导航组件 定位组件 不能为空");
                }
            }
        });
    }
}
