package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.service.integration.feign.AkcCmsClient;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.TargetType;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * banner组件 校验跳转 老会场
 * <AUTHOR>
 */
@Component
@Slf4j
public class BannerComponentAkcCmsValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Autowired
    private AkcCmsClient akcCmsClient;

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context) {
        if (CollectionUtils.isEmpty(context.getImageComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        List<BannerComponentBO> banners = context.getBannerComponents();
        for (BannerComponentBO banner : banners) {
            Preconditions.checkArgument(Objects.nonNull(banner.getDirectUserGroup()) && Objects.nonNull(banner.getDirectUserGroup().getDirectShowType()) , "banner组件 人群设置 不能为空");
            if (CollectionUtils.isNotEmpty(banner.getCarouselConfigDetails())) {
                //校验老会场是否有效
                List<String> hotSaleIds = banner.getCarouselConfigDetails().stream().filter(c -> TargetType.HOTSALE_OLD.getType().equals(c.getTargetType())).map(x -> x.getTargetId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hotSaleIds)) {
                    hotSaleIds.forEach(hotSaleId -> {
                        HotSaleConfigVO hotSaleConfigVO = akcCmsClient.queryHotSalecInfo(Integer.parseInt(hotSaleId));
                        if (Objects.isNull(hotSaleConfigVO)) {
                            throw new BusinessException("banner组件 超品会场-老【"+hotSaleId+"】 必须是已生效");
                        }
                    });
                }


                List<String> conferenceIds = banner.getCarouselConfigDetails().stream().filter(c -> TargetType.CONFERENCE_OLD.getType().equals(c.getTargetType())).map(x -> x.getTargetId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(conferenceIds)) {
                    conferenceIds.forEach(conferenceId -> {
                        ConferenceConfigRes conferenceConfigRes = akcCmsClient.conferenceBaseInfoAgg(Integer.parseInt(conferenceId));
                        if (Objects.isNull(conferenceConfigRes)) {
                            throw new BusinessException("banner组件 普通会场-老【"+conferenceId+"】 必须是已生效");
                        }
                    });
                }
            }
        }
        return new InnerResult<BusinessModel>(true, null);
    }
}
