package com.mengxiang.mshop.cms.core.service.processor;

import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.process.model.BusinessContext;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.base.common.process.processor.AbstractBusinessProcessor;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: zhangmoxun
 * @Date: 2023/3/8
 * @Description: 业务解析抽象层
 * @param <C>
 * @param <M>
 */
public abstract class AbstractBusinessParser<C extends BusinessContext, M extends BusinessModel> extends
        AbstractBusinessProcessor<C, M> {

    /**
     * 业务执行前处理 校验器无逻辑
     *
     * @param context 业务上下文
     * @return 业务执行结果
     */
    @Override
    protected InnerResult<M> beforeProcess(C context) {
        return new InnerResult<M>(Boolean.TRUE, null);
    }

    /**
     * 获取执行器名称
     *
     * @return
     */
    @Override
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 业务执行后处理
     *
     * @param context 业务上下文
     * @return 业务执行结果
     */
    @Override
    protected InnerResult<M> doProcess(C context) {
        InnerResult<BusinessModel> innerResult = this.parse(context);
        if (innerResult.getSuccess() && !context.getNeedInterrupt()) {
            return new InnerResult<>(Boolean.TRUE, null);
        } else {
            return new InnerResult(innerResult.getErrorCode(), CmsProdConstant.APP_ID);
        }
    }

    /**
     * 解析器逻辑 遇到失败则抛出异常
     *
     * @param context 上下文
     * @return
     */
    protected abstract InnerResult<BusinessModel> parse(C context);

    public <T>  List<T>  getDataByJsonObject(List<JSONObject> jsons,Class<T> transfer) {
        return Optional.ofNullable(jsons)
                .orElse(Collections.emptyList())
                .stream()
                .map(json -> json.toJavaObject(transfer))
                .collect(Collectors.toList());
    }
}
