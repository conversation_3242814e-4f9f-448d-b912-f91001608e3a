package com.mengxiang.mshop.cms.core.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleEnableRequestDto;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.lock.base.api.ILock;
import com.mengxiang.base.common.lock.base.api.ILockFactory;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.base.common.process.model.BusinessType;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageTemplateDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageTemplateDo;
import com.mengxiang.mshop.cms.common.service.integration.feign.DcAiwardClient;
import com.mengxiang.mshop.cms.common.util.RedisRepository;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketRuleBO;
import com.mengxiang.mshop.cms.core.model.enums.*;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ProcessStatusRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ProcessStatusResp;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.utils.ContentUtils;
import com.mengxiang.mshop.cms.core.service.business.*;
import com.mengxiang.mshop.cms.core.service.context.BusinessServiceResult;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.enums.ProcessStatusEnum;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import com.mengxiang.mshop.cms.core.service.util.ResultUtils;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.mengxiang.mshop.cms.core.service.enums.BusinessTypeEnum.SAVE_PAGE_MENGXIANG;
import static com.mengxiang.mshop.cms.core.service.enums.BusinessTypeEnum.SAVE_PAGE_SUPPLIER;
import static com.mengxiang.mshop.cms.core.service.enums.BusinessTypeEnum.SAVE_PAGE_SAAS;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Slf4j
public class PageOperateServiceImpl extends AbstractBusinessService<SavePageRequest, PageContext, PageBO, BusinessServiceResult<PageBO>> implements PageOperateService {

    @Autowired
    private ILockFactory iLockFactory;

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Autowired
    private MshopPageTemplateDao templateDao;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private PageCacheService pageCacheService;

    @Autowired
    private DcAiwardClient dcAiwardClient;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private WorkbenchService workbenchService;

    @Autowired
    private ContentCheckService contentCheckService;

    @Autowired
    private MqService mqService;

    @Autowired
    private PageUpdateService pageUpdateService;

    @Autowired
    private PrivateMarketService privateMarketService;

    private static final HashMap<PageOwnerType, BusinessType> BUSINESS_ROUTE = new HashMap<>();

    static {
        BUSINESS_ROUTE.put(PageOwnerType.SUPPLIER, SAVE_PAGE_SUPPLIER);
        BUSINESS_ROUTE.put(PageOwnerType.MENGXIANG, SAVE_PAGE_MENGXIANG);
        BUSINESS_ROUTE.put(PageOwnerType.SAAS_TENANT, SAVE_PAGE_SAAS);
    }

    @Override
    public PageBO savePageBaseConfig(SavePageRequest request) {
        MshopPageTemplateDo templateDo = templateDao.getByTemplateCode(request.getTemplateCode());
        if (Objects.isNull(templateDo)) {
            throw new BusinessException(CmsErrorCodeEnum.TEMPLATE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        if (StringUtils.isNotEmpty(templateDo.getUseChannels())) {
            List<String> channels = Arrays.asList(templateDo.getUseChannels().split(","));
            if (!channels.contains(request.getChannel())) {
                throw new BusinessException("该模版不支持此端");
            }
        }
        MshopPageInstanceDo page = new MshopPageInstanceDo();
        String pageCode = sequenceGeneratorService.getSequence(request.getOwnerId());
        page.setPageCode(pageCode);
        if (StringUtils.isEmpty(request.getType())) {
            page.setType(templateDo.getPageUseType());
        }
        page.setStatus(PageInstanceStatusEnum.DRAFT.getCode());
        page.setOwnerId(request.getOwnerId());
        page.setOwnerType(request.getOwnerType());
        page.setTenantId(request.getTenantId());
        page.setTemplateCode(request.getTemplateCode());
        page.setChannel(request.getChannel());
        int res = mshopPageInstanceDao.insert(page);
        if (res <= 0) {
            throw new BusinessException(CmsErrorCodeEnum.SAVE_PAGE_ERROR.getErrorMsg());
        }
        PageBO pageBO = new PageBO();
        pageBO.setPageCode(pageCode);
        return pageBO;
    }

    @Override
    public Boolean setPageInvalidation (String pageCode,String updateBy,String updateUserId) {
        MshopPageInstanceDo pageInstanceDo = mshopPageInstanceDao.getByPageCode(pageCode,null);
        if (Objects.isNull(pageInstanceDo)) {
            throw new BusinessException(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        if (PageInstanceStatusEnum.APPROVAL.getCode().equals(pageInstanceDo.getStatus())) {
            throw new BusinessException(CmsErrorCodeEnum.PAGE_STATUS_ERROR.getErrorMsg());
        }
        boolean result = updatePageStatus(pageCode,pageInstanceDo.getVersion(),PageInstanceStatusEnum.DISABLED.getCode());
        if (!result) {
            return false;
        }

        clearPageOnLine(pageInstanceDo.getPageCode(),pageInstanceDo.getType(),pageInstanceDo.getOwnerId(),pageInstanceDo.getVersion());
        saveOperationLog(OperationLogActionEnum.DISABLED,pageInstanceDo.getPageCode(),pageInstanceDo.getStatus()
                ,PageInstanceStatusEnum.DISABLED.getCode(),updateBy,updateUserId,pageInstanceDo.getOwnerType());
        //发送mq消息
        sendPublishPageMQ(pageInstanceDo.getPageCode(), pageInstanceDo.getVersion(),PageInstanceStatusEnum.DISABLED.getCode());
        return true;
    }

    @Override
    public boolean clearPageOnLine(String pageCode,String type,String ownerId,String version) {
        pageCacheService.clearPageJsonByVersion(pageCode,version);
        pageCacheService.clearPageJsonByTypeAndOwnerId(type,ownerId);
        pageCacheService.clearPageJsonByCode(pageCode);
        if (StringUtils.isNotEmpty(version)) {
            SellRuleEnableRequestDto request = new SellRuleEnableRequestDto();
            request.setBusinessCode(pageCode);
            request.setRuleIds(Lists.newArrayList());
            dcAiwardClient.batchEnable(request);
        }
        return true;
    }

    @Override
    public Boolean pageCheckPublish(String pageCode, String version) {
        String status = contentCheckService.queryContentCheckStatus(pageCode,version);
        //校验人审是否通过
        ProcessStatusRequest request = new ProcessStatusRequest();
        request.setPageCode(pageCode);
        request.setVersion(version);
        ProcessStatusResp processStatusResp = workbenchService.findProcessStatus(request);
        Integer workflowStatus = ProcessStatusEnum.AUDIT_SUCCESS.getCode();
        if (Objects.nonNull(processStatusResp)) {
            workflowStatus = processStatusResp.getProcessStatus();
        }
        //检测通过
        if (status.equals(ContentSuggestTypeEnum.PASS.getSuggesst()) && workflowStatus.equals(ProcessStatusEnum.AUDIT_SUCCESS.getCode())) {
            Integer result = publishPage(pageCode, version);
            if (Objects.nonNull(result)){
                //发送MQ消息
                sendPublishPageMQ(pageCode, version,result);
            }
            return Objects.nonNull(result);
        }
        //不通过
        if (status.equals(ContentSuggestTypeEnum.BLOCK.getSuggesst()) || workflowStatus.equals(ProcessStatusEnum.AUDIT_FAIL.getCode())) {
            return updatePageStatus(pageCode, version,PageInstanceStatusEnum.FAIL.getCode());
        }
        //审核中,长时间不审核,可能端上页面缓存已失效,需要保持线上最近一次发布版本生效
        Optional<String> pageInfoOpt = pageCacheService.getPageJsonByCode(pageCode);
        String pageInfo = pageInfoOpt.orElse("");
        if (StringUtils.isEmpty(pageInfo)) {
            pageCacheService.compensatePageCacheByClosestVersion(pageCode);
        }
        return true;
    }

    @Override
    public Integer publishPage (String pageCode,String publishVersion) {
        return Optional.ofNullable(pageCode)
                .filter(StringUtils::isNotEmpty)
                .map(code -> mshopPageInstanceDao.getByPageCode(pageCode, null))
                .filter(Objects::nonNull)
                .map(page -> {
                    PageBO pageBO = pageQueryService.detailByDb(pageCode, publishVersion);
                    Integer status = PageInstanceStatusEnum.PUBLISH.getCode();
                    boolean checkRuleTimeByEndTime = TimeConfigUtils.checkRuleTimeByEndTime(pageBO.getTimeConfig());
                    //发布页面时, 页面已失效
                    if (!checkRuleTimeByEndTime) {
                        status = PageInstanceStatusEnum.DISABLED.getCode();
                        saveOperationLog(OperationLogActionEnum.DISABLED,pageBO.getPageCode(),pageBO.getStatus()
                                ,PageInstanceStatusEnum.DISABLED.getCode(),"system","system",pageBO.getOwnerType());
                    }
                    // 生效时间在未来 把状态改成 待生效
                    boolean checkRuleTimeByStartTime = TimeConfigUtils.checkRuleTimeByStartTime(pageBO.getTimeConfig());
                    if (checkRuleTimeByStartTime) {
                        status = PageInstanceStatusEnum.EXECUTORY.getCode();
                    }
                    mshopPageDraftDao.updateStatusByPageCode(pageCode,publishVersion,status);
                    int res = mshopPageInstanceDao.updateStatusAndVersionByPageCode(pageCode, status, publishVersion);
                    if (res <= 0) {
                        return null;
                    }
                    pageBO.setStatus(status);
                    if (MarketTypeEnum.PRIVATE.equals(pageBO.getMarketType())){
                        //保存私密会场相关信息
                        List<Long> activityRuleIds = pageQueryService.getComponentRuleIds(pageBO.getComponents(), ComponentTypeEnum.ACTIVITY);
                        privateMarketService.savePrivateRule(pageBO.getPageCode(), activityRuleIds,pageBO.getPrivateMarketConfig());
                    }
                    boolean checkRuleTime = TimeConfigUtils.checkRuleTime(pageBO.getTimeConfig());
                    if (checkRuleTime) {
                        boolean batchEnableRes = ruleBatchEnable(pageBO);
                        if (!batchEnableRes) {
                            return null;
                        }
                        boolean syncCacheResult = pageCacheService.syncCache(pageBO);
                        return syncCacheResult? status : null;
                    } else {
                        //删除目前线上在线页面缓存
                        PageBO onLinePage = pageQueryService.detailByCache(pageCode,null);
                        String onLineVersion = null;
                        if (Objects.nonNull(onLinePage)) {
                            onLineVersion = onLinePage.getVersion();
                        }
                        clearPageOnLine(pageCode,pageBO.getType(),pageBO.getOwnerId(),onLineVersion);
                    }
                    return status;
                }).orElse(null);
    }

    /**
     * 保存操作记录
     */
    @Override
    public void saveOperationLog(OperationLogActionEnum action,String pageCode,Integer beforeStatus
            ,Integer afterStatus,String createBy,String createUserId,String ownerType) {
        OperationLogSaveRequest operationSaveRequest = new OperationLogSaveRequest();
        operationSaveRequest.setBeforeData(PageInstanceStatusEnum.getEnumByCode(beforeStatus).getDesc());
        operationSaveRequest.setAfterData(PageInstanceStatusEnum.getEnumByCode(afterStatus).getDesc());
        operationSaveRequest.setAction(action.getCode());
        operationSaveRequest.setRemark(action.getDesc());
        operationSaveRequest.setBizType(OperationLogBizTypeEnum.PAGE.getCode());
        operationSaveRequest.setBizCode(pageCode);
        operationSaveRequest.setCreateBy(createBy);
        operationSaveRequest.setCreateUserId(createUserId);
        if(Objects.equals(PageOwnerType.SUPPLIER.getOwnerType(),ownerType)
                && ContentUtils.checkUserId(createUserId)){
            //饷店人员修改商家页面 日志要改成饷店
            operationSaveRequest.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
        }else{
            operationSaveRequest.setOwnerType(ownerType);
        }
        operationLogService.saveOperationLog(operationSaveRequest);
    }

    /**
     * 选品规则生效
     * @param pageBO
     * @return
     */
    private boolean ruleBatchEnable (PageBO pageBO) {
        List<Long> ruleIds = pageQueryService.getComponentRuleIds(pageBO.getComponents());
        SellRuleEnableRequestDto request = new SellRuleEnableRequestDto();
        request.setBusinessCode(pageBO.getPageCode());
        request.setRuleIds(ruleIds);
        boolean batchEnableRes = dcAiwardClient.batchEnable(request);
        if (!batchEnableRes) {
            return false;
        }
//        //规则生效后需要保存私密会场相关信息
//        List<Long> activityRuleIds = pageQueryService.getComponentRuleIds(pageBO.getComponents(), ComponentTypeEnum.ACTIVITY);
//
//        privateMarketService.savePrivateRule(pageBO.getPageCode(), activityRuleIds,pageBO.getPrivateMarketConfig());

        return true;
    }
    
    @Override
    public Boolean updatePageStatus(String pageCode, String version,Integer status) {
        int res = mshopPageInstanceDao.updatePageStatus(pageCode,version,status);
        if (StringUtils.isNotEmpty(version)) {
            mshopPageDraftDao.updateStatusByPageCode(pageCode,version,status);
        }
        return res > 0;
    }
    
    @Override
    public PageBO save(SavePageRequest request) {
        ILock iLock = iLockFactory.getLock(RedisRepository.REDIS_PREFIX + RedisRepository.PAGE_OPRATE + request.getPageCode());
        try {
            boolean lock = iLock.lock(RedisRepository.DEFAULT_LOCK_TIMEOUT, RedisRepository.DEFAULT_LOCK_TIMEOUT);
            if (!lock) {
                throw new BusinessException(CmsErrorCodeEnum.SAVE_PAGE_DRAFT_ERROR.getErrorMsg());
            }
            BusinessServiceResult<PageBO> execute = super.execute(request);
            Result<PageBO> result = ResultUtils.convertToResult(execute, execute.getData());
            return result.getData();
        } catch (IllegalArgumentException | BusinessException ige) {
            throw ige;
        } finally {
            if (Objects.nonNull(iLock)) {
                iLock.releaseLock();
            }
        }
    }

    @Override
    public PageContext buildContext(SavePageRequest request) {
        PageContext context = new PageContext();
        if(Objects.equals(PageOwnerType.SUPPLIER.getOwnerType(), request.getOwnerType())
                &&  Objects.equals(PageType.MARKET_PAGE.getType(), request.getType())){
            //商家3.0的走 梦饷运营流程
            context.setRequest(request).setBusinessType(BUSINESS_ROUTE.get(PageOwnerType.MENGXIANG));
        }else{
            context.setRequest(request).setBusinessType(BUSINESS_ROUTE.get(PageOwnerType.translate(request.getOwnerType())));
        }
        return context;
    }

    @Override
    public BusinessServiceResult<PageBO> buildResponse(PageContext context, InnerResult<PageBO> innerResult) {
        BusinessServiceResult serviceResult = new BusinessServiceResult();
        PageBO pageBO = new PageBO();
        if (!innerResult.getSuccess()){
            if (CollectionUtil.isNotEmpty(context.getContentCheck())) {
                pageBO.setContentCheckResults(context.getContentCheck());
                serviceResult.setData(pageBO);
            }
            serviceResult.setErrorCode(innerResult.getErrorCode(), "tuan-activity-center");
            return serviceResult;
        }
        serviceResult.setSuccess(true);
        MshopPageDraftDo mshopPageDraftDo = context.getFeaturePageDraft();
        MshopPageInstanceDo instanceDo = context.getFeaturePageInstance();
        pageBO.setPageCode(mshopPageDraftDo.getPageCode());
        pageBO.setVersion(mshopPageDraftDo.getVersion());
        pageBO.setStatus(instanceDo.getStatus());
        serviceResult.setData(pageBO);
        return serviceResult;
    }

    @Override
    public void sendPublishPageMQ(String pageCode,String publishVersion,Integer status){
        try{
            //只发送饷店的消息
            PageSearchRequest request = new PageSearchRequest();
            request.setPageCodeList(Lists.newArrayList(pageCode));
//            request.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
            request.setType(PageType.MARKET_PAGE.getType());
            Result<Pagination<PageSelectResult>> result = pageQueryService.pageSelect(request);
            if(Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData()) || CollectionUtils.isEmpty(result.getData().getResult())){
                log.warn("sendPublishPageMQ  result is null req:{},{}",request,result);
                return;
            }
            PageSelectResult pageSelectResult = result.getData().getResult().get(0);
            if(Objects.isNull(pageSelectResult)){
                log.warn("sendPublishPageMQ  pageSelectResult is null req:{},{}",request,result);
                return;
            }
            pageSelectResult.setStatus(status);
            mqService.sendMsg("AKC_CMS_CONFERENCE_TOPIC","AKC_CMS_CONFERENCE_TAG", JSON.toJSONString(pageSelectResult));
        }catch (Exception ex){
            log.error("sendPublishPageMQ error req:{},{}",pageCode,publishVersion,ex);
        }
    }

    /**
     * 设置主页
     */
    @Override
    public void setPageIndex(String pageCode,String pageType,String ownerId,String ownerType){
        //校验,当前类型
        MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(pageCode,null);
        if(Objects.isNull(mshopPageInstanceDo)){
            throw new BusinessException("当前页面不存在");
        }
//        if(!PageInstanceStatusEnum.PUBLISH.getCode().equals(mshopPageInstanceDo.getStatus())){
//            throw new BusinessException("未生效的页面无法设置成主页");
//        }
        PageBO pageBO = pageQueryService.detailByPageType(ownerId,pageType);
        if(Objects.nonNull(pageBO) && pageBO.getPageCode().equals(pageCode)){
            throw new BusinessException("当前页面已经是主页");
        }
        //分布式锁
        ILock iLock = iLockFactory.getLock(RedisRepository.REDIS_PREFIX + RedisRepository.PAGE_SET_INDEX + ownerId+"_"+ownerType);
        try{
            boolean lock = iLock.lock(RedisRepository.DEFAULT_LOCK_TIMEOUT, RedisRepository.DEFAULT_LOCK_TIMEOUT);
            if (!lock) {
                throw new BusinessException(CmsErrorCodeEnum.SAVE_PAGE_DRAFT_ERROR.getErrorMsg());
            }
            String currentPageCode = null;
            //修改当前主页变成店铺会场页
            if(Objects.nonNull(pageBO)){
                currentPageCode = pageBO.getPageCode();
            }
            //修改当前的页面变成主页
            pageUpdateService.updateOnlyPageByPageType(pageCode,currentPageCode,ownerId,ownerType);
        }
        catch (IllegalArgumentException | BusinessException ige) {
            throw ige;
        }
        catch (Exception ex) {
            log.error("setPageIndex error req:{},{},{}",pageCode,pageType,ownerId,ex);
            throw new BusinessException("设置主页失败");
        }
        finally {
            if (Objects.nonNull(iLock)) {
                iLock.releaseLock();
            }
        }
    }
}
