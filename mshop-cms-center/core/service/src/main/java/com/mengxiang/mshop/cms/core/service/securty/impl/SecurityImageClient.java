package com.mengxiang.mshop.cms.core.service.securty.impl;


import com.aikucun.common2.base.Result;
import com.aikucun.security.ugc.api.dto.request.ImageScanReq2DTO;
import com.aikucun.security.ugc.api.dto.response.ImageScanApiResult2Resp;
import com.aikucun.security.ugc.api.dto.response.ScanResultSuggestionResp;
import com.aikucun.security.ugc.api.enums.ImageSceneEnum;
import com.aikucun.security.ugc.api.enums.SuggestionTypeEnum;
import com.aikucun.security.ugc.api.feign.SecurityImageScanApiClient;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SecurityImageClient {
    
    @Value("${risk.check.application.name:mshop-cms-center}")
    private String appId;
    
    @Value("${risk.check.app-secret:65527753358b41e28243a05d5a6f75b7}")
    private String appSecret;
    
    @Value("${risk.check.application.name:mshop-cms-center}")
    private String bizType;
    
    @Resource
    private SecurityImageScanApiClient securityImageScanApiClient;
    
    public List<ContentCheckResponse> riskImageCheck(List<String> contents, String userId) {
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }
        List<List<String>> partitions = Lists.partition(contents, 20);
        List<ContentCheckResponse> resp = new ArrayList<>();
        for (List<String> partition : partitions) {
            List<ContentCheckResponse> contentCheckResponses = doRiskImageCheck(partition, userId);
            if (!CollectionUtils.isEmpty(contentCheckResponses)) {
                resp.addAll(contentCheckResponses);
            }
        }
        return resp;
    }
    
    private List<ContentCheckResponse> doRiskImageCheck(List<String> contents, String userId) {
        ImageScanReq2DTO imageScanReqDTO = this.buildImageScanReq(userId, contents);
        
        List<ContentCheckResponse> resps = contents.stream().map(e -> {
            ContentCheckResponse responseParam = new ContentCheckResponse();
            responseParam.setContent(e);
            responseParam.setQualifiedFlag(false);
            return responseParam;
        }).collect(Collectors.toList());
        
        try {
            Result<List<ImageScanApiResult2Resp>> result = securityImageScanApiClient.imageScanSync2(imageScanReqDTO);
            if (null == result || !result.getSuccess()) {
                //riskImageCheck
                log.warn("[[riskImageCheck]] 调用图片安全校验出现异常 contents:{}", JSON.toJSONString(result));
                throw new BusinessException("校验图片失败");
            }
            List<ImageScanApiResult2Resp> data = result.getData();
            if (CollectionUtils.isEmpty(result.getData())) {
                return resps;
            }
//            boolean present = data.stream().filter(e -> !"200".equals(e.getCode())).findAny().isPresent();
//            if(present){
//                //调用异常。
//                log.warn("[[riskImageCheck]] 调用图片安全校验出现异常 contents:{}", JSON.toJSONString(result));
//                throw new BusinessException("校验图片失败");
//            }
            
            //查询被风控
            Map<String, ImageScanApiResult2Resp> imageMap = data.stream()
                    .collect(Collectors.toMap(ImageScanApiResult2Resp::getUrl, v -> v, (v1, v2) -> v1));
            
            for (ContentCheckResponse resp : resps) {
                ImageScanApiResult2Resp imageScanApiResult2Resp = imageMap.get(resp.getContent());
                if (null == imageScanApiResult2Resp) {
                    resp.setCheckDesc(Lists.newArrayList("没有返回审核结果"));
                    continue;
                }
                resp.setDateId(imageScanApiResult2Resp.getDataId());
                //pass或者review直接返回
                if (SuggestionTypeEnum.pass.equals(imageScanApiResult2Resp.getSuggestion()) || SuggestionTypeEnum.review
                        .equals(imageScanApiResult2Resp.getSuggestion())) {
                    resp.setQualifiedFlag(true);
                    continue;
                }
                //提示词
                resp.setSuggestionTip(imageScanApiResult2Resp.getSuggestionTip());
                //检测描述
                List<ScanResultSuggestionResp> results = imageScanApiResult2Resp.getResults();
                if (CollectionUtils.isEmpty(results)) {
                    continue;
                }
                //设置检测描述内容
                List<String> checkDesc = new ArrayList<>();
                for (ScanResultSuggestionResp scanResultSuggestionResp : results) {
                    ImageSceneEnum anEnum = ImageSceneEnum.getEnum(scanResultSuggestionResp.getScene());
                    if (null != anEnum) {
                        checkDesc.add(anEnum.getDesc());
                    }
                }
                resp.setCheckDesc(checkDesc);
            }
            return resps;
        } catch (Exception e) {
            log.error("[[riskImageCheck]] 调用文本内容安全校验出现异常 req:{}", contents, e);
        }
        return null;
    }
    
    private ImageScanReq2DTO buildImageScanReq(String userId, List<String> imageUrls) {
        ImageScanReq2DTO imageScanReqDTO = new ImageScanReq2DTO();
        //暂定宽度为600等比例缩略图
        List<String> smallImageUrls = new ArrayList<>();
        for (String url : imageUrls) {
            smallImageUrls.add(url);
        }
        imageScanReqDTO.setImageUrls(smallImageUrls);
        imageScanReqDTO.setUserId(userId);
        imageScanReqDTO.setAppId(appId);
        imageScanReqDTO.setAppSecret(appSecret);
        imageScanReqDTO.setBizType(bizType);
        return imageScanReqDTO;
    }
}
