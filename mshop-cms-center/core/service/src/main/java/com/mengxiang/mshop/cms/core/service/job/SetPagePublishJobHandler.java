package com.mengxiang.mshop.cms.core.service.job;

/**
 * <AUTHOR>
 * @Date: 2023/4/21
 * @Description: 定时生效页面
 */

import cn.hutool.core.collection.CollectionUtil;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogActionEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class SetPagePublishJobHandler {

    @Autowired
    private PageOperateService pageOperateService;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @XxlJob("setPagePublishJobHandler")
    public ReturnT<String> setPagePublishJobHandler(String params) {
        List<MshopPageInstanceDo> instanceDos = mshopPageInstanceDao.queryByStatus(PageInstanceStatusEnum.EXECUTORY.getCode());
        if (CollectionUtil.isEmpty(instanceDos)) {
            return ReturnT.SUCCESS;
        }
        for (MshopPageInstanceDo page : instanceDos) {
            if (StringUtils.isEmpty(page.getVersion())) {
                continue;
            }
            pageOperateService.pageCheckPublish(page.getPageCode(),page.getVersion());
            MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(page.getPageCode(),page.getVersion());
            if (Objects.nonNull(mshopPageInstanceDo) && !page.getStatus().equals(mshopPageInstanceDo.getStatus())) {
                pageOperateService.saveOperationLog(OperationLogActionEnum.PUBLISH,page.getPageCode(),page.getStatus()
                        ,mshopPageInstanceDo.getStatus(),"system","system",mshopPageInstanceDo.getOwnerType());
            }
        }
        return ReturnT.SUCCESS;
    }
}
