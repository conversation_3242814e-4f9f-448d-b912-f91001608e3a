package com.mengxiang.mshop.cms.core.service.processor.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.CouponComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.PointsCouponComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.cube.CubeComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImageComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.material.MaterialComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.AllProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.ProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.profit.ProfitAdditionalComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.promotion.UserIncentiveComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.seckill.SeckillComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.text.SeparatorComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.text.TextComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.toplist.TopListComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentBO;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.business.SequenceGeneratorService;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BaseComponentConverter extends AbstractBusinessConverter<PageContext, BusinessModel> {

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;


    private final Map<Class<? extends ComponentBO>, AbstractMap.SimpleEntry<Function<PageContext, List<? extends ComponentBO>>, Function<ComponentBO, List<? extends ComponentDetailBO>>>> componentMap = new HashMap<>();
    public BaseComponentConverter() {
        componentMap.put(BannerComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getBannerComponents, component -> ((BannerComponentBO) component).getCarouselConfigDetails()));
        componentMap.put(ActivityComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getActivityComponents, component -> ((ActivityComponentBO) component).getActivityRuleConfigDetails()));
        componentMap.put(CouponComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getCouponComponents, component -> ((CouponComponentBO) component).getCouponConfigDetails()));
        componentMap.put(PointsCouponComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getPointsCouponComponents, component -> ((PointsCouponComponentBO) component).getCouponConfigDetails()));
        componentMap.put(ImageComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getImageComponents, this::getImageConfigDetails));
        componentMap.put(NavigationComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getNavigationComponents, component -> ((NavigationComponentBO) component).getNavigationConfigDetails()));
        componentMap.put(ProductComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getProductComponents, component -> ((ProductComponentBO) component).getProductRuleConfigDetails()));
        componentMap.put(ProfitAdditionalComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getProfitComponents, component -> ((ProfitAdditionalComponentBO) component).getProductRuleConfigDetails()));
        componentMap.put(SeckillComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getSeckillComponents, component -> ((SeckillComponentBO) component).getSeckillConfigDetails()));
        componentMap.put(TopListComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getTopListComponents, component -> ((TopListComponentBO) component).getTopListConfigDetails()));
        componentMap.put(VideoComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getVideoComponents, component -> ((VideoComponentBO) component).getVideoConfigDetails()));
        componentMap.put(SeparatorComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getSeparatorComponents, component -> Lists.newArrayList()));
        componentMap.put(CubeComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getCubeComponents, component -> ((CubeComponentBO) component).getImageList()));
        componentMap.put(TextComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getTextComponents, component -> Lists.newArrayList()));
        componentMap.put(MaterialComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getMaterialComponents, component -> Lists.newArrayList()));
        componentMap.put(AllProductComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getAllProductComponents, component -> Lists.newArrayList()));
        componentMap.put(UserIncentiveComponentBO.class, new AbstractMap.SimpleEntry<>(PageContext::getUserIncentiveComponents, component -> Lists.newArrayList()));
    }

    private List<? extends ComponentDetailBO> getImageConfigDetails(ComponentBO component) {
        if (component instanceof ImageComponentBO) {
            ImageComponentBO imageComponent = (ImageComponentBO) component;
            List<ComponentDetailBO> configDetails = new ArrayList<>();
            configDetails.addAll(imageComponent.getHotConfigDetails());
            configDetails.addAll(CollectionUtil.isEmpty(imageComponent.getHoleConfigDetails()) ? Lists.newArrayList() : imageComponent.getHoleConfigDetails());
            return configDetails;
        }
        return Collections.emptyList();
    }

    @Override
    protected InnerResult<BusinessModel> convert(PageContext context) {
        SavePageRequest request = context.getRequest();
        MshopPageDraftDo mshopPageDraftDo = context.getFeaturePageDraft();
        String pageCode = mshopPageDraftDo.getPageCode();
        String featureVersion = context.getFeatureVersion();
        List<MshopComponentInstanceDo> componentDos = context.getComponentInstances();
        List<MshopComponentInstanceDetailDo> componentDetails = context.getComponentInstanceDetails();
        if (StringUtils.isEmpty(request.getComponents())){
            return new InnerResult<>(Boolean.TRUE, null);
        }
        Map<String,MshopComponentInstanceDo> archorIdMap = context.getArchorIdMap();
        Map<String,MshopComponentInstanceDo> archorCodeMap = context.getArchorCodeMap();
        componentMap.entrySet().stream().flatMap(entry -> {
            Class<? extends ComponentBO> clazz = entry.getKey();
            AbstractMap.SimpleEntry<Function<PageContext, List<? extends ComponentBO>>, Function<ComponentBO, List<? extends ComponentDetailBO>>> pair = entry.getValue();
            List<? extends ComponentBO> components = pair.getKey().apply(context);
            return components.stream().map(component -> new AbstractMap.SimpleEntry<>(clazz, new AbstractMap.SimpleEntry<>(component, pair.getValue().apply(component))));
        }).forEach(entry -> {
            Class<? extends ComponentBO> clazz = entry.getKey();
            ComponentBO component = entry.getValue().getKey();
            String componentCode = component.getComponentCode();
            if (StringUtils.isEmpty(componentCode)) {
                componentCode = sequenceGeneratorService.getSequence(pageCode);
                component.setComponentCode(componentCode);
            }
            MshopComponentInstanceDo mshopComponent = getComponentInstance(pageCode, featureVersion, component.getType(), component, clazz);
            if (StringUtils.isEmpty(mshopComponent.getTimeConfig()) && StringUtils.isNotEmpty(mshopPageDraftDo.getTimeConfig())) {
                mshopComponent.setTimeConfig(mshopPageDraftDo.getTimeConfig());
            }
            List<? extends ComponentDetailBO> configDetails = entry.getValue().getValue();
            if (CollectionUtil.isNotEmpty(configDetails)) {
                List<MshopComponentInstanceDetailDo> details = configDetails.stream()
                        .map(configDetail -> {
                            String componentDetailCode = configDetail.getComponentDetailCode();
                            if (StringUtils.isEmpty(componentDetailCode)) {
                                componentDetailCode = sequenceGeneratorService.getSequence(pageCode);
                                configDetail.setComponentDetailCode(componentDetailCode);
                            }
                            MshopComponentInstanceDetailDo mshopComponentInstanceDetail = getComponentInstanceDetail(mshopComponent, configDetail, configDetail.getClass());
                            if (StringUtils.isEmpty(mshopComponentInstanceDetail.getTimeConfig()) && StringUtils.isNotEmpty(mshopComponent.getTimeConfig())) {
                                mshopComponentInstanceDetail.setTimeConfig(mshopComponent.getTimeConfig());
                            }
                            try {
                                Field field = configDetail.getClass().getDeclaredField("detailType");
                                field.setAccessible(true);
                                String detailType = (String) field.get(configDetail);
                                mshopComponentInstanceDetail.setComponentDetailType(detailType);
                            } catch (Exception e) {
                                log.warn("BaseComponentConverter detailType Exception",e);
                            }
                            return mshopComponentInstanceDetail;
                        }).filter(Objects::nonNull).collect(Collectors.toList());
                componentDetails.addAll(details);
            }
            componentDos.add(mshopComponent);
            //前端组件id
            String componentId = component.getComponentId();
            if(StringUtils.isNotEmpty(componentId)) {
                archorIdMap.put(componentId,mshopComponent);
            }
            archorCodeMap.put(mshopComponent.getComponentCode(),mshopComponent);
        });
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
