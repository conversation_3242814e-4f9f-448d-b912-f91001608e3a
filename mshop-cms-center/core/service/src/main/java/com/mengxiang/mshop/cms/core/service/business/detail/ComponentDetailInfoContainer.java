package com.mengxiang.mshop.cms.core.service.business.detail;


import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ComponentDetailInfoContainer {
    
    private static final Map<String, AbstractComponentInfoService> STRING_TAG_CONVERT_MAP = new HashMap<>();
    
    public ComponentDetailInfoContainer(List<AbstractComponentInfoService> registerServices) {
        for (AbstractComponentInfoService componentConvert : registerServices) {
            ComponentTypeEnum componentType = componentConvert.getComponentType();
            if (null == componentType) {
                continue;
            }
            STRING_TAG_CONVERT_MAP.put(componentType.name(), componentConvert);
        }
    }
    
    /**
     * 获取检验类型
     */
    public AbstractComponentInfoService getComponentConvert(ComponentTypeEnum componentType) {
        if (null == componentType) {
            throw new BusinessException("500","不支持的组件类型");
        }
        AbstractComponentInfoService componentDetailInfoService = STRING_TAG_CONVERT_MAP.get(componentType.name());
        if (null == componentType) {
            throw new BusinessException("500","不支持的组件类型");
        }
        return componentDetailInfoService;
    }
}