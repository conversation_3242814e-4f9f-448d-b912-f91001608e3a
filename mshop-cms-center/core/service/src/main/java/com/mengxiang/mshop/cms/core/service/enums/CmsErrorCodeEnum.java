package com.mengxiang.mshop.cms.core.service.enums;

import com.mengxiang.base.common.model.exception.constant.IErrorCode;
import com.mengxiang.base.common.process.constant.BusinessSceneEnum;
import com.mengxiang.base.common.process.error.BusinessErrorCode;
import com.mengxiang.base.common.process.error.ErrorCode;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
public enum CmsErrorCodeEnum implements BusinessErrorCode {

    /**
     * 未知系统异常
     */
    SYSTEM_ERROR(BusinessSceneEnum.COMPONENT,
            new ErrorCode(
                    IErrorCode.PROCESSING_ERROR).toString(),
            "未知系统异常",
            "网络繁忙，稍后再试试看吧:)"),

    /**
     * 未知异常
     */
    UNKNOWN_ERROR(BusinessSceneEnum.CONTENT_MANAGE,
            new ErrorCode("998").toString(),
            "未知异常",
            "网络繁忙，稍后再试试看吧:)"),

    /**
     * 参数不正确
     */
    ILLEGAL_ARGUMENT(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.ARGUMENT_ERROR).toString(), "参数不正确", "参数不正确"),


    /**
     * 页面不存在
     */
    PAGE_ISNOTEXISTS_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "页面不存在", "页面不存在"),

    /**
     * 模版不存在
     */
    TEMPLATE_ISNOTEXISTS_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "模版不存在", "模版不存在"),


    /**
     * 页面正在保存中
     */
    SAVE_PAGE_DRAFT_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), " 页面保存排队中,请稍后重试", " 页面保存排队中,请稍后重试"),

    /**
     * 页面保存失败
     */
    SAVE_PAGE_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "页面保存失败,请稍后重试", "页面保存失败,请稍后重试"),

    /**
     * 视频校验失败
     */
    SAVE_PAGE_VIDEO_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "视频校验失败,请稍后重试", "视频校验失败,请稍后重试"),

    /**
     * 此页面状态不支持编辑
     */
    PAGE_STATUS_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "此页面状态不支持操作", "此页面状态不支持操作"),

    /**
     * 页面结束时间要大于当前时间
     */
    PAGE_ENDTIME_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "页面结束时间要大于当前时间", "页面结束时间要大于当前时间"),

    /**
     * 私密会场的开始结束时间配置不能为空
     */
    PAGE_TIME_EMPTY_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场的开始结束时间配置不能为空", "私密会场的开始结束时间配置不能为空"),

    /**
     * 私密会场的开始结束时间间隔太长
     */
    PAGE_TIME_INTERVAL_TOO_LONG_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场的开始结束时间间隔太长", "私密会场的开始结束时间间隔太长"),


    /**
     * 私密会场的配置不能为空
     */
    PAGE_PRIVATE_CONFIG_EMPTY_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场的配置不能为空", "私密会场的配置不能为空"),


    /**
     * 私密会场的配置渠道不能为空
     */
    PAGE_PRIVATE_CONFIG_CHANNEL_EMPTY_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场的配置渠道不能为空", "私密会场的配置渠道不能为空"),


    /**
     * 私密会场的档期组件不能为空
     */
    PAGE_PRIVATE_COMPONENT_SCHEDULE_EMPTY_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场档期组件不能为空", "私密会场档期组件不能为空"),

    /**
     * 私密会场的配置人群不能为空
     */
    PAGE_PRIVATE_CONFIG_GROUPS_EMPTY_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场的配置人群不能为空", "私密会场的配置人群不能为空"),

    /**
     * 私密会场的配置档期活动不能为空
     */
    PAGE_PRIVATE_CONFIG_ACTIVITY_EMPTY_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "私密会场的配置档期活动不能为空", "私密会场的配置档期活动不能为空"),
    /**
     * 无权限享有店铺装修功能
     */
    PAGE_NON_PERMISSION_ERROR(BusinessSceneEnum.CONTENT_MANAGE, new ErrorCode(
            IErrorCode.PROCESSING_ERROR).toString(), "无权限享有店铺装修功能", "无权限享有店铺装修功能"),

    ;


    /**
     * 错误发生场景
     */
    private final BusinessSceneEnum errorScene;

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String errorMsg;

    /**
     * 错误提示，用于展现给用户
     */
    private final String displayMsg;

    /**
     * 构造方法
     *
     * @param errorScene
     * @param errorCode
     * @param errorMsg
     * @param displayMsg
     */
    CmsErrorCodeEnum(BusinessSceneEnum errorScene, String errorCode, String errorMsg, String displayMsg) {
        this.errorScene = errorScene;
        this.code = errorCode;
        this.errorMsg = errorMsg;
        this.displayMsg = displayMsg;
    }

    /**
     * 获取标准错误码对象
     *
     * @return
     */
    @Override
    public ErrorCode getErrorCode() {

        ErrorCode errorCode = new ErrorCode(code);
        errorCode.setErrorMsg(errorMsg);
        errorCode.setDisplayMsg(displayMsg);
        return errorCode;
    }

    /**
     * 获取标准错误码对象
     *
     * @return
     */
    @Override
    public String getErrorCodeStr() {

        return getErrorCode().toString();
    }

    /**
     * Getter method for property <tt>errorScene</tt>.
     *
     * @return property value of errorScene
     */
    public BusinessSceneEnum getErrorScene() {
        return errorScene;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>errorMsg</tt>.
     *
     * @return property value of errorMsg
     */
    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * Getter method for property <tt>displayMsg</tt>.
     *
     * @return property value of displayMsg
     */
    @Override
    public String getDisplayMsg() {
        return displayMsg;
    }
}
