package com.mengxiang.mshop.cms.core.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.mengxiang.mshop.cms.core.service.business.MqService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class MqServiceImpl implements MqService {

    @Resource
    private RocketMQTemplate rocketMQTemplate;


    @Autowired
    private Gson gson;

    @Override
    public void sendMsg(String topic, String tag, String message) {
        try {
            rocketMQTemplate.asyncSend(topic + ":" + tag, gson.toJson(message).getBytes(Charset.forName("utf-8")), new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("MqService sendMsg  message:{},{} ", message,JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("MqService sendMsg send message error.message {} ", message, throwable);
                }
            });
        } catch (Exception e) {
            log.error("MqService sendMsg send message error {}", e.getMessage(), e);
        }
    }
}
