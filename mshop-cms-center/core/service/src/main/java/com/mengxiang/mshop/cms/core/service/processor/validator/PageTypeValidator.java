package com.mengxiang.mshop.cms.core.service.processor.validator;

import cn.hutool.core.collection.CollectionUtil;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * banner 页面类型校验
 * <AUTHOR>
 */
@Component
@Slf4j
public class PageTypeValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        SavePageRequest request = context.getRequest();
        if(PageType.isOnlyPage(request.getType())) {
            List<String> types = Arrays.asList(request.getType());
            List<MshopPageInstanceDo> pages = mshopPageInstanceDao.selectPagesByTypes(request.getOwnerId(),request.getOwnerType(),types);
            if (CollectionUtil.isEmpty(pages)) {
                return new InnerResult<BusinessModel>(true, null);
            }
            if (StringUtils.isNotEmpty(request.getPageCode())) {
                pages = pages.stream().filter(page -> !request.getPageCode().equals(page.getPageCode())).collect(Collectors.toList());
            }
            boolean isValid = pages.stream().anyMatch(page -> page.getStatus().equals(PageInstanceStatusEnum.EXECUTORY.getCode()) || page.getStatus().equals(PageInstanceStatusEnum.PUBLISH.getCode()) || page.getStatus().equals(PageInstanceStatusEnum.APPROVAL.getCode()));
            if (isValid) {
                String desc = "有效的" + PageType.translate(request.getType()).getDesc() + "只能创建一个";
                throw new BusinessException(desc);
            }
        }
        return new InnerResult<BusinessModel>(true, null);
    }

}
