package com.mengxiang.mshop.cms.core.service.business.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDetailDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.service.business.ComponentDetailService;
import com.mengxiang.mshop.cms.core.service.business.SequenceGeneratorService;
import com.mengxiang.mshop.cms.core.service.business.detail.ComponentDetailInfoContainer;
import com.mengxiang.mshop.cms.core.model.request.ComponetDetailCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 *
 * 组件详情服务
 */
@Service
@Slf4j
public class ComponentDetailServiceImpl implements ComponentDetailService {
    
    @Autowired
    private MshopComponentInstanceDetailDao componentInstanceDetailDao;
    
    @Autowired
    private ComponentDetailInfoContainer componentDetailInfoContainer;

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;
    
    @Override
    public String create(ComponetDetailCreateRequest req) {
        log.info("[[create]] 创建组件详情 req:{}", JSON.toJSONString(req));
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getPageCode()), "页面编号 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getComponentCode()), "组件编号 不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getComponentType()), "组件类型 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getCreateBy()), "创建人 不能为空");
        ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(req.getComponentType());
        if(componentType == null){
            log.error("[[create]] 创建组件 不支持的组件类型 req:{}",JSON.toJSONString(req));
            throw new BusinessException("不支持的组件类型");
        }
        //组件类型
        MshopPageInstanceDo pageInstance = mshopPageInstanceDao.getByPageCode(req.getPageCode(),null);
        //页面校验
        if (Objects.isNull(pageInstance)) {
            log.info("[[create]] 没有查询到页面 pageCode:{}",req.getPageCode());
            throw new BusinessException(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        //创建组件详情
        String detailCode = sequenceGeneratorService.getSequence(pageInstance.getOwnerId());
        MshopComponentInstanceDetailDo instanceDetailDo = new MshopComponentInstanceDetailDo();
        instanceDetailDo.setPageCode(req.getPageCode());
        instanceDetailDo.setComponentCode(req.getComponentCode());
        instanceDetailDo.setConfigDetailCode(detailCode);
        instanceDetailDo.setComponentType(componentType.getCode());
        componentInstanceDetailDao.insert(instanceDetailDo);
        return detailCode;
    }
    
    @Override
    public List<MshopComponentInstanceDetailDo> queryByPageCode(String pageCode, String version) {
        return componentInstanceDetailDao.queryByPageCode(pageCode,version);
    }
}
