package com.mengxiang.mshop.cms.core.service.business.impl;

import com.aikucun.dc.profile.facade.stub.LabelRequestDto;
import com.aikucun.dc.profile.facade.stub.LabelResponseDto;
import com.aikucun.dc.profile.write.common.enums.DataType;
import com.aikucun.dc.profile.write.common.enums.ModelType;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.mshop.cms.common.service.integration.feign.DcProfileClient;
import com.mengxiang.mshop.cms.core.model.domain.base.DirectUserGroupBO;
import com.mengxiang.mshop.cms.core.model.enums.DirectShowTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Date: 2023/5/9
 * @Description: 定向分群
 */
@Service
@Slf4j
public class DirectUserGroupServiceImpl implements DirectUserGroupService {

    @Autowired
    private DcProfileClient dcProfileClient;

    /**
     * 爱库存App
     */
    private String akcApp = "akcApp";

    /**
     * 爱库存App H5
     */
    private String akcAppH5 = "akcAppH5";

    /**
     *
     * @param directUserGroup 组件用户分群配置
     * @param baseReqModule 聚合用户登录信息
     * @return 是否展示组件
     */
    @Override
    public boolean userFilter(DirectUserGroupBO directUserGroup, AggrBaseReqModule baseReqModule) {
        try {
            if (Objects.isNull(directUserGroup)) {
                return true;
            }
            if (DirectShowTypeEnum.ALL.getCode() == directUserGroup.getDirectShowType()) {
                //全部显示
                return true;
            } else if (DirectShowTypeEnum.USER_TYPE.getCode() == directUserGroup.getDirectShowType()) {
                //根据用户类型显示
                Integer userType = baseReqModule == null ? 1 : getRole(baseReqModule.getCurrentRoleType(),baseReqModule.getDistributorId());
                if (directUserGroup.getNewUserType().contains(String.valueOf(userType))) {
                    if (isSeller(baseReqModule.getCurrentRoleType(),baseReqModule.getDistributorId())) {
                        //如果是店主，判断店主等级和子等级
                        return validUserLevel(baseReqModule.getLevel(), baseReqModule.getUserSubLevel(), directUserGroup.getUserLevels());
                    } else {
                        return true;
                    }
                }
            } else if (DirectShowTypeEnum.USER_GROUP.getCode() == directUserGroup.getDirectShowType()) {
                //用户分群
                if (isAkcApp(baseReqModule.getChannel())) {
                    if (validUserGroup(baseReqModule.getReSellerId(), directUserGroup.getUserGroups(), ModelType.APP_USER)) {
                        return true;
                    }
                } else {
                    if (validUserGroup(baseReqModule.getUserId() + "", directUserGroup.getUserGroups(), ModelType.C_USER)) {
                        return true;
                    }
                }
            }
        } catch (Throwable ex) {
            log.error("userFilter error directUserGroup:{}", JSONObject.toJSONString(directUserGroup),ex);
        }
        return false;
    }

    public boolean validUserGroup(String modelId, String userGroups, ModelType modelType) {
        try {
            //查询C端用户
            //使用userId指代modelId
            List<LabelRequestDto> labelList = Arrays.stream(userGroups.split(",")).filter(str -> str.contains("_")).map(userGroupsLabelId -> {
                LabelRequestDto labelRequestDto = new LabelRequestDto();
                labelRequestDto.setLabelId(Integer.parseInt(userGroupsLabelId.split("_")[1]));
                labelRequestDto.setDataType(DataType.STRING);
                return labelRequestDto;
            }).collect(toList());
            if (CollectionUtils.isEmpty(labelList)) {
                return false;
            }
            List<LabelResponseDto> labelValues = dcProfileClient.getLabelValues(modelType, modelId, labelList);
            if (CollectionUtils.isEmpty(labelValues)) {
                return false;
            }
            List<LabelResponseDto> valueList = labelValues.stream().filter(vo -> StringUtils.isNotBlank(vo.getValue())).collect(toList());
            if (CollectionUtils.isEmpty(valueList)) {
                return false;
            }
            return true;
        } catch (Throwable e) {
            log.error( "用户画像标签查询error modelId:{} userGroups:{}" ,modelId , userGroups, e);
            return false;
        }
    }

    /**
     * 1:普通用户 2 ：店主 3：店长
     *
     * @return
     */
    public static Integer getRole(Integer currentRoleType,Long distributorId) {
        if (currentRoleType == null) {
            return 1;
        }
        //管理员
        if (Objects.equals(4, currentRoleType)) {
            currentRoleType = distributorId != null ? 3 : 1;
        }
        return currentRoleType;
    }

    public boolean isAkcApp(String channel) {
        if (akcApp.equals(channel) || akcAppH5.equals(channel)) {
            return true;
        }
        return false;
    }

    /**
     * 店主
     */
    public boolean isSeller(Integer currentRoleType,Long distributorId) {
        return 2 == getRole(currentRoleType,distributorId);
    }

    /**
     * c
     */
    public static boolean isBuyer(Integer currentRoleType,Long distributorId) {
        return 1 == getRole(currentRoleType, distributorId);
    }

    /**
     * 校验档期userLevels中是否包含当前用户等级
     *
     * @param userLevel    用户等级
     * @param userSubLevel 用户子等级
     * @param userLevels   用户等级(1-0,1-1,1-2,1-3,2,3,4,5,6)，多个以逗号分割
     * @return true=包含/false=不包含
     */
    public static boolean validUserLevel(Integer userLevel, String userSubLevel, String userLevels) {
        if (StringUtils.isEmpty(userLevels)) {
            return true;
        }
        if (Objects.equals(userLevel, 1) && StringUtils.isBlank(userSubLevel)) {
            userSubLevel = "0";
        } else if (!Objects.equals(userLevel, 1)) {
            //只有1级用户有子等级
            userSubLevel = null;
        }
        if (StringUtils.isNotBlank(userLevels) && userLevel != null) {
            return ("," + userLevels + ",").contains("," + userLevel + (StringUtils.isNotBlank(userSubLevel) ? "-" + userSubLevel : "") + ",");
        }
        return false;
    }
}
