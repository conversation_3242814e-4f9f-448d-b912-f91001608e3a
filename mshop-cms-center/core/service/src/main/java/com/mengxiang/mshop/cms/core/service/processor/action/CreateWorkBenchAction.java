package com.mengxiang.mshop.cms.core.service.processor.action;

import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ReStartProcRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.SaveProcRequest;
import com.mengxiang.mshop.cms.core.service.business.WorkbenchService;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/5/16
 * @Description: 创建审核流
 */
@Service
public class CreateWorkBenchAction extends AbstractBusinessAction<PageContext, BusinessModel> {
    @Autowired
    private WorkbenchService workbenchService;

    @Override
    protected void beforeAction(PageContext context) {

    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        if (Objects.isNull(context.getWorkBenchType()) || context.getWorkBenchType().equals(0)) {
            return new InnerResult<>(Boolean.TRUE, null);
        }
        SavePageRequest savePageRequest = context.getRequest();
        MshopPageDraftDo featurePageDraft = context.getFeaturePageDraft();
        MshopPageDraftDo currentPageDraft = context.getCurrentPageDraft();
        if (context.getWorkBenchType().equals(1)) {
            SaveProcRequest request = new SaveProcRequest();
            request.setPageCode(featurePageDraft.getPageCode());
            request.setVersion(featurePageDraft.getVersion());
            request.setUserId(savePageRequest.getCreateUserId());
            request.setUserName(savePageRequest.getCreateBy());
            request.setUserCode(savePageRequest.getCreateUserCode());
            request.setManagerNoDirect(savePageRequest.getManagerNoDirect());
            request.setName(savePageRequest.getName());
            request.setAuditList(savePageRequest.getAuditList());
            request.setOwnerType(savePageRequest.getOwnerType());
            request.setType(savePageRequest.getType());
            request.setMerchantCode(savePageRequest.getMerchantCode());
            request.setShopName(savePageRequest.getShopName());
            request.setOwnerId(savePageRequest.getOwnerId());
            request.setCategoryLeaderList(savePageRequest.getCategoryLeaderList());
            workbenchService.createProcNo(request);
        }
        if (context.getWorkBenchType().equals(2)) {
            ReStartProcRequest request = new ReStartProcRequest();
            request.setPageCode(featurePageDraft.getPageCode());
            request.setVersion(featurePageDraft.getVersion());
            request.setUserId(savePageRequest.getCreateUserId());
            request.setUserName(savePageRequest.getCreateBy());
            request.setUserCode(savePageRequest.getCreateUserCode());
            request.setManagerNoDirect(savePageRequest.getManagerNoDirect());
            request.setBusinessKey(context.getBusinessKey());
            request.setAuditList(savePageRequest.getAuditList());
            request.setOwnerType(savePageRequest.getOwnerType());
            request.setType(savePageRequest.getType());
            request.setCategoryLeaderList(savePageRequest.getCategoryLeaderList());
            workbenchService.rejectReStart(request);
        }
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
