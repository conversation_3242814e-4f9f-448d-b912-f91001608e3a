package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 基础页面配置参数校验
 * <AUTHOR>
 */
@Component
public class PageConfigBaseArgumentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        SavePageRequest request = context.getRequest();
        Preconditions.checkArgument(Objects.nonNull(request.getOperateType()), "操作类型不能为空");
        //Preconditions.checkArgument(StringUtils.isNotEmpty(request.getTitle()), "页面标题不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(request.getName()), "页面名称不能为空");
        //Preconditions.checkArgument(StringUtils.isNotEmpty(request.getSubTitle()), "页面子标题不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(request.getType()), "页面类型不能为空");
        Preconditions.checkArgument(Objects.nonNull(request.getSearchFlag()), "搜索功能不能为空");
        Preconditions.checkArgument(Objects.nonNull(request.getSearchBox()), "搜索框设置不能为空");
        return new InnerResult<BusinessModel>(true, null);
    }

}
