package com.mengxiang.mshop.cms.core.service.processor.action;

import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.PageOperateType;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.content.PageContentRequest;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 2023/3/31
 * @Description:
 */
@Service
public class SecurityVideoScanAsyncAction extends AbstractBusinessAction<PageContext, BusinessModel> {
    @Autowired
    private ContentCheckService contentCheckService;

    @Override
    protected void beforeAction(PageContext context) {

    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        SavePageRequest savePageRequest = context.getRequest();
        if(savePageRequest.getOperateType().equals(PageOperateType.SAVE.getCode())) {
            return new InnerResult<>(true, null);
        }
        List<String> urls = Optional.ofNullable(context.getVideoComponents())
                .map(Collection::stream)
                .orElse(Stream.empty())
                .flatMap(x -> x.getVideoConfigDetails().stream())
                .map(VideoComponentConfigDetailBO::getUrl)
                .collect(Collectors.toList());

        if (urls.isEmpty()) {
            return new InnerResult<>(true, null);
        }
        String pageCode = context.getFeaturePageDraft().getPageCode();
        String featureVersion = context.getFeatureVersion();
        
        List<PageContentRequest> componentUrls  = new ArrayList<>();
        List<VideoComponentBO> videoComponents = context.getVideoComponents();
        for (VideoComponentBO component : videoComponents) {
            List<VideoComponentConfigDetailBO> details = component.getVideoConfigDetails();
            for (VideoComponentConfigDetailBO detail : details) {
                PageContentRequest request = new PageContentRequest();
                request.setComponentCode(component.getComponentCode());
                request.setComponentDetailCode(detail.getComponentDetailCode());
                request.setUrl(detail.getUrl());
                componentUrls.add(request);
            }
        }
        List<ContentCheckResponse> contentCheckResp = contentCheckService.pageContentCheck(pageCode, featureVersion, componentUrls);
        
        boolean isFail = CollectionUtils.isEmpty(contentCheckResp) || Optional.ofNullable(contentCheckResp)
                .map(Collection::stream)
                .orElse(Stream.empty())
                .anyMatch(x -> !x.isQualifiedFlag());
        if (isFail) {
            context.setContentCheck(contentCheckResp);
            context.setNeedInterrupt(true);
            return new InnerResult<>(CmsErrorCodeEnum.SAVE_PAGE_VIDEO_ERROR.getErrorCode(), CmsProdConstant.APP_ID);
        }
        return new InnerResult<>(true, null);
    }
}
