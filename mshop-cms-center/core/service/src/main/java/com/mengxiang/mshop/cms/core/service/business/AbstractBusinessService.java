package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.base.common.model.request.BaseRequest;
import com.mengxiang.base.common.process.impl.AbstractServiceExecuteTemplate;
import com.mengxiang.base.common.process.model.BusinessContext;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.base.common.process.model.ServiceResult;

/**
 *
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description: 抽象业务处理服务
 * @param <B>
 * @param <C>
 * @param <M>
 * @param <R>
 */
public abstract class AbstractBusinessService<B extends BaseRequest, C extends BusinessContext, M extends BusinessModel, R extends ServiceResult>
        extends AbstractServiceExecuteTemplate<B, C, M, R> {

    /**
     * 业务流程执行入口
     *
     * @param request 业务请求对象
     * @return
     */
    protected R execute(B request) {
        return super.execute(request, new ServiceInterceptor<B, C, M, R>() {
            @Override
            public C getBusinessContext(B request) {
                return buildContext(request);
            }

            @Override
            public R getResultInstance(C context, InnerResult<M> innerResult) {
                return buildResponse(context, innerResult);
            }
        });
    }

    /**
     * 构建业务上下文
     *
     * @param request
     * @return
     */
    public abstract C buildContext(B request);

    /**
     * 获取业务执行结果
     *
     * @param context 上下文
     * @param innerResult
     * @return
     */
    public abstract R buildResponse(C context, InnerResult<M> innerResult);
}
