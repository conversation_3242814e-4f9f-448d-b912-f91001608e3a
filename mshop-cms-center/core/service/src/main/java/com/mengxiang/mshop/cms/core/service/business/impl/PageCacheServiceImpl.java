package com.mengxiang.mshop.cms.core.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.mengxiang.mshop.cms.core.service.business.PageQueryService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date: 2023/4/12
 * @Description:
 */
@Service
@Slf4j
public class PageCacheServiceImpl implements PageCacheService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Autowired
    private PageQueryService pageQueryService;

    @Value("${mshop-cms-center.caffeineCache.expireAfterAccess:600}")
    private Long expireAfterAccess;

    @Value("${mshop-cms-center.caffeineCache.expireAfterWrite:120}")
    private Long expireAfterWrite;

    @Value("${mshop-cms-center.page.instance.limit:20}")
    private Integer pageInstanceLimit;


    @Value("${mshop-cms-center.caffeineCache.maximumSize:1000}")
    private Long maximumSize;

    @Value("${mshop-cms-center.searchRedisStringFlag:true}")
    private Boolean searchRedisStringFlag;
    @Value("#{'${mshop-cms-center.saveCachePageList:1}'.split(',')}")
    private List<String> saveCachePageList;

    private LoadingCache<String, String> caffeineCache;

    @PostConstruct
    @Override
    public void buildCaffeineCache() {
        caffeineCache = Caffeine.newBuilder()
                // 设置最后一次访问后经过固定时间过期
//                .expireAfterAccess(expireAfterAccess, TimeUnit.SECONDS)
                .expireAfterWrite(expireAfterWrite, TimeUnit.SECONDS)
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(maximumSize)
                .build(key -> findPageStringCache(key));
    }

    @Override
    public boolean syncCache(PageBO page) {
        if (Objects.isNull(page)) {
            return false;
        }
        String pageCode = page.getPageCode();
        String version = page.getVersion();
        String pageJson = JSONObject.toJSONString(page);
        //距离明日 凌晨3点 秒数
        long timeout = DateUtil.getTomorrowTimeOut(3);
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        //设置 pageCode -> pageInfo redis
        redisTemplate.opsForHash().put(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY, pageJson);
        String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
        //设置 pageCode + version -> pageInfo redis
        redisTemplate.opsForHash().put(pageCodeKey, pageVersionKey, pageJson);
        redisTemplate.expire(pageCodeKey, timeout, TimeUnit.SECONDS);

        //存一份string的
        redisTemplate.opsForValue().set(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY, pageJson, timeout, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(pageCodeKey + ":" + pageVersionKey, pageJson, timeout, TimeUnit.SECONDS);

        //设置 pageType + ownerId -> pageCode redis
        if (PageType.isOnlyPage(page.getType())) {
            String typeOwnerIdKey = String.format(PageConstant.PAGE_TYPE_CACHE_KEY, page.getType(), page.getOwnerId());
            redisTemplate.opsForHash().put(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, typeOwnerIdKey, pageCode);
            redisTemplate.expire(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, timeout, TimeUnit.SECONDS);

            //存一份string的
            redisTemplate.opsForValue().set(PageConstant.PAGE_TYPE_OWNER_HASH_KEY + ":" + typeOwnerIdKey, pageCode, timeout, TimeUnit.SECONDS);
        }
        return true;
    }

    @Override
    public boolean compensatePageCache() {
        List<MshopPageInstanceDo> instances = Optional.ofNullable(
                mshopPageInstanceDao.queryByStatus(PageInstanceStatusEnum.PUBLISH.getCode())
        ).orElse(Collections.emptyList());

        List<MshopPageInstanceDo> tenantInstances = instances.stream()
                .filter(instance -> PageOwnerType.SAAS_TENANT.getOwnerType().equals(instance.getOwnerType()))
                .collect(Collectors.toList());
        List<MshopPageInstanceDo> nonTenantInstances = instances.stream()
                .filter(instance -> !PageOwnerType.SAAS_TENANT.getOwnerType().equals(instance.getOwnerType()))
                .collect(Collectors.toList());
        processInstances(tenantInstances);
        processInstances(nonTenantInstances);
        return true;
    }

    private void processInstances(List<MshopPageInstanceDo> instances) {
        int totalSize = instances.size();
        IntStream.range(0, (totalSize + pageInstanceLimit - 1) / pageInstanceLimit)
                .parallel()
                .mapToObj(i -> instances.subList(i * pageInstanceLimit, Math.min(totalSize, (i + 1) * pageInstanceLimit)))
                .forEach(batch -> {
                    batch.stream()
                            .filter(instance -> StringUtils.isNotEmpty(instance.getVersion()))
                            .map(instance -> {
                                String sourcePageCode = instance.getPageCode();
                                String sourceVersion = instance.getVersion();
                                MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getByPageCode(sourcePageCode, sourceVersion);
                                return new AbstractMap.SimpleEntry<>(instance, mshopPageDraftDo);
                            })
                            .filter(pair -> Objects.nonNull(pair.getValue()) && StringUtils.isNotEmpty(pair.getValue().getTimeConfig()))
                            .filter(pair -> {
                                TimeConfigBO timeConfig = JSONObject.parseObject(pair.getValue().getTimeConfig(), TimeConfigBO.class);
                                return TimeConfigUtils.checkRuleTime(timeConfig);
                            })
                            .forEach(pair -> {
                                String type = pair.getKey().getType();
                                String sourcePageCode = pair.getKey().getPageCode();
                                String ownerId = pair.getKey().getOwnerId();
                                compensatePage(type, ownerId, sourcePageCode, pair.getKey().getVersion());
                            });
                });
    }

    @Override
    public boolean compensatePageCacheForFail() {
        Optional.ofNullable(mshopPageInstanceDao.queryByStatus(PageInstanceStatusEnum.FAIL.getCode()))
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .forEach(page -> {
                    compensatePageCacheByClosestVersion(page.getPageCode());
                });
        return true;
    }

    public void compensatePage(String type, String ownerId, String sourcePageCode, String version) {
        Optional<String> pageCodeOpt = getPageJsonByTypeAndOwnerId(type, ownerId);
        String pageCode = pageCodeOpt.orElse("");
        if (StringUtils.isEmpty(pageCode) && PageType.isOnlyPage(type)) {
            syncPageJsonByTypeAndOwnerId(type, ownerId, sourcePageCode);
        }
        Optional<String> pageInfoOpt = getPageJsonByCode(sourcePageCode);
        String pageInfo = pageInfoOpt.orElse("");
        if (StringUtils.isEmpty(pageInfo)) {
            PageBO page = pageQueryService.detailByDb(sourcePageCode, version);
            if (Objects.nonNull(page)) {
                syncPageJsonByPage(page);
            }
        }
    }

    @Override
    public void compensatePageCacheByClosestVersion(String pageCode) {
        MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getLastByPageCode(pageCode, PageInstanceStatusEnum.PUBLISH.getCode());
        if (Objects.isNull(mshopPageDraftDo)) {
            return;
        }
        MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(pageCode, null);
        if (Objects.isNull(mshopPageInstanceDo)) {
            return;
        }
        if (mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.DISABLED.getCode())) {
            return;
        }
        if (StringUtils.isEmpty(mshopPageDraftDo.getTimeConfig())) {
            return;
        }
        TimeConfigBO timeConfig = JSONObject.parseObject(mshopPageDraftDo.getTimeConfig(), TimeConfigBO.class);
        boolean isValid = TimeConfigUtils.checkRuleTime(timeConfig);
        if (isValid) {
            compensatePage(mshopPageInstanceDo.getType(), mshopPageInstanceDo.getOwnerId(), pageCode, mshopPageDraftDo.getVersion());
        }
    }

    @Override
    public Optional<String> getPageJsonByTypeAndOwnerId(String type, String ownerId) {
        //先读取string结构redis数据
        String result = getPageStringJsonByTypeAndOwnerId(type, ownerId);
        if (StringUtils.isNotBlank(result)) {
            return Optional.of(result);
        }
        log.info("getPageJsonByTypeAndOwnerId forHash type:{} ownerId:{}", type, ownerId);
        String typeOwnerIdKey = String.format(PageConstant.PAGE_TYPE_CACHE_KEY, type, ownerId);
        String pageCode = (String) redisTemplate.opsForHash().get(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, typeOwnerIdKey);
        if (StringUtils.isEmpty(pageCode)) {
            return Optional.empty();
        }
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        String pageJson = (String) redisTemplate.opsForHash().get(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isEmpty(pageJson)) {
            return Optional.empty();
        }
        return Optional.of(pageJson);
    }

    /**
     * 先读取string结构redis数据
     *
     * @param type
     * @param ownerId
     * @return
     */
    private String getPageStringJsonByTypeAndOwnerId(String type, String ownerId) {
        String typeOwnerIdKey = String.format(PageConstant.PAGE_TYPE_CACHE_KEY, type, ownerId);
        //先查询stringRedis
        String pageCode = findPageStringCache(PageConstant.PAGE_TYPE_OWNER_HASH_KEY + ":" +typeOwnerIdKey);
        if (StringUtils.isEmpty(pageCode)) {
            return null;
        }
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        String pageJson = findPageStringCache(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isEmpty(pageJson)) {
            return null;
        }
        return pageJson;
    }

    @Override
    public boolean syncPageJsonByTypeAndOwnerId(String type, String ownerId, String syncPageCode) {
        //存一份string的
        syncPageJsonStringByTypeAndOwnerId(type, ownerId, syncPageCode);

        String typeOwnerIdKey = String.format(PageConstant.PAGE_TYPE_CACHE_KEY, type, ownerId);
        String pageCode = (String) redisTemplate.opsForHash().get(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, typeOwnerIdKey);
        if (StringUtils.isEmpty(pageCode)) {
            //距离明日 凌晨3点 秒数
            long timeout = DateUtil.getTomorrowTimeOut(3);
            redisTemplate.opsForHash().put(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, typeOwnerIdKey, syncPageCode);
            redisTemplate.expire(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, timeout, TimeUnit.SECONDS);
        }
        return true;
    }

    /**
     * 存一份string的
     *
     * @param type
     * @param ownerId
     * @param syncPageCode
     */
    public void syncPageJsonStringByTypeAndOwnerId(String type, String ownerId, String syncPageCode) {
        String typeOwnerIdKey = String.format(PageConstant.PAGE_TYPE_CACHE_KEY, type, ownerId);
        String pageCode = redisTemplate.opsForValue().get(PageConstant.PAGE_TYPE_OWNER_HASH_KEY + ":" + typeOwnerIdKey);
        if (StringUtils.isEmpty(pageCode)) {
            //距离明日 凌晨3点 秒数
            long timeout = DateUtil.getTomorrowTimeOut(3);
            redisTemplate.opsForValue().set(PageConstant.PAGE_TYPE_OWNER_HASH_KEY + ":" + typeOwnerIdKey, syncPageCode, timeout, TimeUnit.SECONDS);
        }
    }

    @Override
    public boolean clearPageJsonByTypeAndOwnerId(String type, String ownerId) {
        String typeOwnerIdKey = String.format(PageConstant.PAGE_TYPE_CACHE_KEY, type, ownerId);
        redisTemplate.delete(PageConstant.PAGE_TYPE_OWNER_HASH_KEY + ":" + typeOwnerIdKey);
        String pageCode = (String) redisTemplate.opsForHash().get(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, typeOwnerIdKey);
        if (StringUtils.isNotEmpty(pageCode)) {
            redisTemplate.opsForHash().delete(PageConstant.PAGE_TYPE_OWNER_HASH_KEY, typeOwnerIdKey);
        }
        return true;
    }

    @Override
    public Optional<String> getPageJsonByCode(String pageCode) {
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);

        if (saveCachePageList.contains(pageCode)) {
            String cacheString = findCaffeineCacheByKey(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);
            if (StringUtils.isNotBlank(cacheString)) {
                return Optional.of(cacheString);
            }
        }

        //先查询stringRedis
        String jsonString = findPageStringCache(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isNotBlank(jsonString)) {
            return Optional.of(jsonString);
        }
        log.info("getPageJsonByCode forHash pageCode:{}", pageCode);
        String pageJson = (String) redisTemplate.opsForHash().get(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isEmpty(pageJson)) {
            return Optional.empty();
        }
        return Optional.of(pageJson);
    }

    @Override
    public boolean clearPageJsonByCode(String pageCode) {
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        String pageJson = (String) redisTemplate.opsForHash().get(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isNotEmpty(pageJson)) {
            redisTemplate.opsForHash().delete(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY);
        }
        //清理string key
        redisTemplate.delete(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);
        return true;
    }

    @Override
    public boolean syncPageJsonByPage(PageBO page) {
        String pageCode = page.getPageCode();
        String version = page.getVersion();
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);

        String jsonString = findPageStringCache(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isEmpty(jsonString)) {
            String pageJson = JSONObject.toJSONString(page);
            //距离明日 凌晨3点 秒数
            long timeout = DateUtil.getTomorrowTimeOut(3);
            String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
            //存一份string的
            redisTemplate.opsForValue().set(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY, pageJson, timeout, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(pageCodeKey + ":" + pageVersionKey, pageJson, timeout, TimeUnit.SECONDS);
        }

        String currentPage = (String) redisTemplate.opsForHash().get(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY);
        if (StringUtils.isEmpty(currentPage)) {
            String pageJson = JSONObject.toJSONString(page);
            //距离明日 凌晨3点 秒数
            long timeout = DateUtil.getTomorrowTimeOut(3);
            String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
            redisTemplate.opsForHash().put(pageCodeKey, PageConstant.CURRENT_PAGE_CACHE_KEY, pageJson);
            redisTemplate.opsForHash().put(pageCodeKey, pageVersionKey, pageJson);
            redisTemplate.expire(pageCodeKey, timeout, TimeUnit.SECONDS);
        }
        return true;
    }

    @Override
    public Optional<String> getPageJsonByVersion(String pageCode, String version) {
        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);

        if (saveCachePageList.contains(pageCode)) {
            String cacheString = findCaffeineCacheByKey(pageCodeKey + ":" + pageVersionKey);
            if (StringUtils.isNotBlank(cacheString)) {
                return Optional.of(cacheString);
            }
        }

        //先查询stringRedis
        String jsonString = findPageStringCache(pageCodeKey + ":" + pageVersionKey);
        if (StringUtils.isNotBlank(jsonString)) {
            return Optional.of(jsonString);
        }
        log.info("getPageJsonByVersion forHash pageCode:{} version:{}", pageCode, version);
        Map<Object, Object> pageMap = redisTemplate.opsForHash().entries(pageCodeKey);
        if (pageMap != null) {
            String pageJson = (String) pageMap.get(pageVersionKey);
            return Optional.ofNullable(pageJson);
        }
        return Optional.empty();
    }

    @Override
    public boolean clearPageJsonByVersion(String pageCode, String version) {
        if (StringUtils.isEmpty(version)) {
            return true;
        }

        String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
        String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
        redisTemplate.delete(pageCodeKey + ":" + pageVersionKey);
        Map<Object, Object> pageMap = redisTemplate.opsForHash().entries(pageCodeKey);
        if (pageMap != null) {
            String pageJson = (String) pageMap.get(pageVersionKey);
            if (StringUtils.isNotEmpty(pageJson)) {
                redisTemplate.opsForHash().delete(pageCodeKey, pageVersionKey);
            }
        }
        return true;
    }

    private String findPageStringCache(String key) {
        if (searchRedisStringFlag) {
            String result = redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotEmpty(result)) {
                return result;
            }
        }
        return null;
    }

    @Override
    public void putPageCaffeineCache(String pageCode, String version) {
        if (StringUtils.isNotBlank(pageCode) && saveCachePageList.contains(pageCode)) {
            String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
            String jsonString = findPageStringCache(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);
            if (StringUtils.isNotBlank(jsonString)) {
                String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
                caffeineCache.put(pageCodeKey + ":" + pageVersionKey, jsonString);
                caffeineCache.put(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY, jsonString);
            }
        }
    }

    @Override
    public void clearPageCaffeineCache(String pageCode, String version) {
        if (StringUtils.isNotBlank(pageCode) && saveCachePageList.contains(pageCode)) {
            String pageCodeKey = String.format(PageConstant.PAGE_CODE_CACHE_KEY, pageCode);
            String pageVersionKey = String.format(PageConstant.PAGE_CODE_VERSION_CACHE_KEY, version);
            caffeineCache.invalidate(pageCodeKey + ":" + pageVersionKey);
            caffeineCache.invalidate(pageCodeKey + ":" + PageConstant.CURRENT_PAGE_CACHE_KEY);

        }
    }

    @Override
    public String findCaffeineCacheByKey(String key) {
        try {
            return caffeineCache.get(key);
        } catch (Exception e) {
            log.error("findCaffeineCacheByKey error, key:{}", key, e);
        }
        return null;
    }
}
