package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.base.PageShareConfigBO;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 梦饷运营页面配置校验
 * <AUTHOR>
 */
@Component
public class PageConfigByMshopArgumentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {
    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        SavePageRequest request = context.getRequest();
        Preconditions.checkArgument(Objects.nonNull(request.getBizType()), "业务类型不能为空");
        PageShareConfigBO pageShareConfig = request.getShareConfig();
        if (Objects.nonNull(pageShareConfig)) {
            if (Objects.nonNull(pageShareConfig.getShareSwitch()) && pageShareConfig.getShareSwitch().equals(1)) {
                Preconditions.checkArgument(StringUtils.isNotEmpty(pageShareConfig.getShareCardImg()), "分享卡片图片 不能为空");
                Preconditions.checkArgument(StringUtils.isNotEmpty(pageShareConfig.getForwardPosterImg()), "转发海报图片 不能为空");
                Preconditions.checkArgument(CollectionUtils.isNotEmpty(pageShareConfig.getForwardPosterText()), "转发海报文案 不能为空");
                Preconditions.checkArgument(StringUtils.isNotEmpty(pageShareConfig.getShareMiniAppImg()), "分享小程序图片 不能为空");
                Preconditions.checkArgument(StringUtils.isNotEmpty(pageShareConfig.getForwardTitle()), "分享主标题 不能为空");
                Preconditions.checkArgument(StringUtils.isNotEmpty(pageShareConfig.getForwardSubTitle()), "分享副标题 不能为空");
                Preconditions.checkArgument(Objects.nonNull(pageShareConfig.getShareButtonSwitch()), "分享按钮开关 不能为空");
            }
        }
        return new InnerResult<BusinessModel>(true, null);
    }

}
