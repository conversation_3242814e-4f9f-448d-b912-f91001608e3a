package com.mengxiang.mshop.cms.core.service.job;

import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 发布失败页面
 * <AUTHOR>
 */
@Component
@Slf4j
public class PageCacheForFailJobHandler {

    @Autowired
    private PageCacheService pageCacheService;

    @XxlJob("compensatePageCacheForFail")
    public ReturnT<String> compensatePageCacheForFail(String params) {
        try {
            pageCacheService.compensatePageCacheForFail();
        } catch (Exception e) {
            log.error("compensatePageCacheForFail 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }
}
