package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.request.OperationLogRequest;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.request.PageOperationRequest;
import com.mengxiang.mshop.cms.core.model.result.OperationLogResult;

/**
 * 操作记录服务
 */
public interface OperationLogService {
    /**
     * 分页查询
     * @return
     */
    Result<Pagination<OperationLogResult>> operationLogPage(OperationLogRequest req);

    Result<Boolean> saveOperationLog(OperationLogSaveRequest req);

}
