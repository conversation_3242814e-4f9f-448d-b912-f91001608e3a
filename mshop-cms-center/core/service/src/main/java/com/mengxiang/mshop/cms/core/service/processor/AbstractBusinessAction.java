package com.mengxiang.mshop.cms.core.service.processor;

import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.process.model.BusinessContext;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.base.common.process.processor.AbstractBusinessProcessor;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description: 业务动作抽象层
 * @param <C>
 * @param <M>
 */
public abstract class AbstractBusinessAction<C extends BusinessContext, M extends BusinessModel> extends
        AbstractBusinessProcessor<C, M> {

    @Override
    protected InnerResult<M> beforeProcess(C context) {
        this.beforeAction(context);
        return new InnerResult<M>(Boolean.TRUE, null);
    }

    @Override
    protected InnerResult<M> doProcess(C context) {
        InnerResult<M> result;
        if (this.tradeLock(context)) {
            result = this.doAction(context);
            if (result.getSuccess()) {
                this.afterAction(context);
            }
        } else {
            Logger.error("出现锁竞争:{}", this.getProcessorName());
            return new InnerResult<>(Boolean.FALSE, null);
        }
        return result;
    }

    @Override
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 业务执行前动作 可用于简单参数检查
     *
     * @param context
     * @return
     */
    protected abstract void beforeAction(C context);

    /**
     * 业务执行核心
     *
     * @param context
     * @return
     */
    protected abstract InnerResult<M> doAction(C context);


    /**
     * 业务执行前 非核心流程
     *
     * @param context 交易上下文
     * @return 是否获取锁成功
     */
    protected void afterAction(C context) {
    }

    /**
     * 适用于数据库状态变更场景
     * 默认以数据库变更行数判断
     *
     * @param context 上下文
     * @return 是否获取锁成功
     */
    protected boolean tradeLock(C context) {
        return Boolean.TRUE;
    }

}
