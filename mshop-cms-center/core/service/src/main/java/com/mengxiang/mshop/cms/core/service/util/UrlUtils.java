package com.mengxiang.mshop.cms.core.service.util;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Auther: zhangmoxun
 * @Date: 2023/3/28
 * @Description:
 */
public class UrlUtils {

    /**
     * 拼接URL参数
     *
     * @param baseUrl   基础URL
     * @param params    参数Map
     * @return          拼接后的URL
     */
    public static String buildUrl(String baseUrl, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }
        String queryString = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        return baseUrl + "?" + queryString;
    }
}
