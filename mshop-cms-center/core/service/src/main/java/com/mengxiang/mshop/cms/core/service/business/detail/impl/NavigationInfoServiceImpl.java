package com.mengxiang.mshop.cms.core.service.business.detail.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeSlotBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationRulesBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.DataRuleTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.NavigationStyleTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/4
 * @Description:
 */
@Service
public class NavigationInfoServiceImpl extends AbstractComponentInfoService<NavigationComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    protected NavigationComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        NavigationComponentBO navigation = JSON.parseObject(metaConfig, NavigationComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),navigation);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> {
                            NavigationComponentConfigDetailBO detailBO = detailBase(detailDo, NavigationComponentConfigDetailBO.class);
                            if (CollectionUtil.isNotEmpty(detailBO.getRules())) {
                                detailBO.getRules().forEach(rule -> {
                                    if (Objects.nonNull(rule.getTimeConfig())) {
                                        Integer ruleStatus = TimeConfigUtils.findRuleStatus(rule.getTimeConfig());
                                        rule.setRuleStatus(ruleStatus);
                                        String ruleCreateTime = DateUtil.dateToStrLong(detailDo.getCreateTime());
                                        rule.setRuleCreateTime(ruleCreateTime);
                                    }
                                });
                            }
                            return detailBO;
                        }).filter(Objects::nonNull).collect(Collectors.toList()))
                .ifPresent(navigation::setNavigationConfigDetails);
        if (CollectionUtil.isNotEmpty(navigation.getNavigationConfigDetails())) {
            navigation.getNavigationConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return navigation;
    }

    @Override
    protected NavigationComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        NavigationComponentBO navigation = JSON.parseObject(metaConfig, NavigationComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),navigation);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(navigation.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(navigation.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> {
                            NavigationComponentConfigDetailBO detailBO = detailBase(detailDo, NavigationComponentConfigDetailBO.class);
                            if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(detailBO.getTabShowFlag()) && detailBO.getTabShowFlag().equals(0)) {
                                return null;
                            }
                            return detailBO;
                        }).filter(Objects::nonNull).collect(Collectors.toList()))
                .ifPresent(navigation::setNavigationConfigDetails);
        if (CollectionUtil.isEmpty(navigation.getNavigationConfigDetails())) {
            return null;
        }
        //查DB 人群不为空, 端上预览
        if (Objects.nonNull(aggrBaseReqModule)) {
            //多个规则,选择生效时间最近的规则
            for (NavigationComponentConfigDetailBO detail : navigation.getNavigationConfigDetails()) {
                if (detail.getRuleType().equals(DataRuleTypeEnum.SELF.getCode()) || CollectionUtils.isNotEmpty(detail.getBusinessIds())) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(detail.getRules())) {
                    NavigationRulesBO navigationRulesBO = findRule(detail.getRules());
                    if (Objects.nonNull(navigationRulesBO)) {
                        detail.setRuleCode(navigationRulesBO.getRuleCode());
                        detail.setRuleType(DataRuleTypeEnum.RULE.getCode());
                    } else {
                        detail.setRuleCode(null);
                    }
                }
            }
        }
        navigation.getNavigationConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        return navigation;
    }

    public NavigationRulesBO findRule (List<NavigationRulesBO> list) {
        List<NavigationRulesBO> rules = queryEffectiveRules(list);
        NavigationRulesBO rule;
        if  (CollectionUtil.isEmpty(rules) || rules.size() > 1) {
            rule = queryClosestRule(list);
        } else {
            rule = rules.get(0);
        }
        if (Objects.nonNull(rule)) {
            return rule;
        }
        return null;
    }

    public List<NavigationRulesBO> queryEffectiveRules(List<NavigationRulesBO> rules){
        if (CollectionUtil.isEmpty(rules)) {
            return new ArrayList<>();
        }
        return rules.stream()
                .filter(detail -> TimeConfigUtils.checkRuleTime(detail.getTimeConfig()))
                .collect(Collectors.toList());
    }

    public NavigationRulesBO queryClosestRule (List<NavigationRulesBO> rules) {
        Date currentTime = new Date();
        NavigationRulesBO closestRule = null;
        Date closestStartTime = null;
        for (NavigationRulesBO navigationRule : rules) {
            TimeConfigBO timeConfig = navigationRule.getTimeConfig();
            List<TimeSlotBO> timeList = timeConfig.getTimeList();
            for (TimeSlotBO timeSlot : timeList) {
                Date startTime = DateUtil.parseDate(timeSlot.getStartTime());
                if (closestStartTime == null || Math.abs(startTime.getTime() - currentTime.getTime()) < Math.abs(closestStartTime.getTime() - currentTime.getTime())) {
                    closestRule = navigationRule;
                    closestStartTime = startTime;
                }
            }
        }
        return closestRule;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        List<Long> ruleIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(componentStr)) {
            NavigationComponentBO navigationComponentBO = JSON.parseObject(componentStr, NavigationComponentBO.class);
            if (CollectionUtils.isNotEmpty(navigationComponentBO.getNavigationConfigDetails())) {
                navigationComponentBO.getNavigationConfigDetails().forEach(detail -> {
                    List<Long> rules = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(detail.getRules())) {
                        rules = detail.getRules().stream().map(r -> Long.parseLong(r.getRuleCode())).collect(Collectors.toList());
                    }
                    if (StringUtils.isNotEmpty(detail.getRuleCode())) {
                        rules.add(Long.parseLong(detail.getRuleCode()));
                    }
                    ruleIds.addAll(rules);
                });
            }
        }
        if(CollectionUtils.isNotEmpty(ruleIds)) {
            return ruleIds.stream().distinct().collect(Collectors.toList());
        }
        return ruleIds;
    }

    @Override
    public NavigationComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        NavigationComponentBO navigation = JSON.parseObject(componentStr, NavigationComponentBO.class);
        if (Objects.nonNull(navigation.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(navigation.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(navigation.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(navigation.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        //过滤无效配置详情
        List<NavigationComponentConfigDetailBO> list = this.queryEffectiveDetails(navigation.getNavigationConfigDetails());
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        //过滤不显示的tab
        list = list.stream().filter(x -> Objects.isNull(x.getTabShowFlag()) || x.getTabShowFlag().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (StringUtils.isNotEmpty(navigation.getStyleType()) && navigation.getStyleType().equals(NavigationStyleTypeEnum.DEFAULT.getCode())) {
            //从多规则里找有效规则 兼容 店铺装修单规则 以及档期的ID集合
            for (NavigationComponentConfigDetailBO detail : list) {
                if (detail.getRuleType().equals(DataRuleTypeEnum.SELF.getCode()) || CollectionUtils.isNotEmpty(detail.getBusinessIds())) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(detail.getRules())) {
                    List<NavigationRulesBO> rules = queryEffectiveRules(detail.getRules());
                    if (CollectionUtil.isNotEmpty(rules)) {
                        NavigationRulesBO navigationRulesBO = rules.get(0);
                        detail.setRuleCode(navigationRulesBO.getRuleCode());
                        detail.setRuleType(DataRuleTypeEnum.RULE.getCode());
                    } else {
                        detail.setRuleCode(null);
                    }
                }
            }
            boolean isRule= list.stream().allMatch(l -> l.getRuleType().equals(DataRuleTypeEnum.RULE.getCode()));
            List<NavigationComponentConfigDetailBO> finalList = list.stream().filter(x -> StringUtils.isNotEmpty(x.getRuleCode()) || CollectionUtil.isNotEmpty(x.getBusinessIds())).collect(Collectors.toList());
            if (isRule && CollectionUtil.isEmpty(finalList)) {
                return null;
            }
            finalList.sort(Comparator.comparing(obj -> obj.getOrder()));
            navigation.setNavigationConfigDetails(finalList);
        } else {
            navigation.setNavigationConfigDetails(list);
        }
        return navigation;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.NAVIGATION;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
