package com.mengxiang.mshop.cms.core.service.processor.action;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogActionEnum;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogBizTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOperateType;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.business.OperationLogService;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.mengxiang.mshop.cms.core.model.utils.ContentUtils.checkUserId;

/**
 * <AUTHOR>
 * @Date: 2023/4/11
 * @Description:
 */
@Service
public class OperationLogSaveAction extends AbstractBusinessAction<PageContext, BusinessModel> {
    @Autowired
    private OperationLogService operationLogService;



    @Override
    protected void beforeAction(PageContext context) {
        
    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {

        SavePageRequest request = context.getRequest();
        MshopPageDraftDo currentPageDraft = context.getCurrentPageDraft();
        MshopPageDraftDo featurePageDraft = context.getFeaturePageDraft();
        OperationLogSaveRequest operationSaveRequest = new OperationLogSaveRequest();
        if (Objects.isNull(currentPageDraft)) {
            operationSaveRequest.setAfterData(JSONObject.toJSONString(featurePageDraft));
        } else {
            operationSaveRequest.setBeforeData(JSONObject.toJSONString(currentPageDraft));
            operationSaveRequest.setAfterData(JSONObject.toJSONString(featurePageDraft));
        }
        if (request.getOperateType().equals(PageOperateType.SAVE.getCode())) {
            operationSaveRequest.setAction(OperationLogActionEnum.SAVE.getCode());
            operationSaveRequest.setRemark(OperationLogActionEnum.SAVE.getDesc());
        } else {
            operationSaveRequest.setAction(OperationLogActionEnum.PUBLISH.getCode());
            operationSaveRequest.setRemark(OperationLogActionEnum.PUBLISH.getDesc());
        }
        operationSaveRequest.setBizType(OperationLogBizTypeEnum.PAGE.getCode());
        operationSaveRequest.setBizCode(featurePageDraft.getPageCode());
        operationSaveRequest.setCreateBy(request.getCreateBy());
        operationSaveRequest.setCreateUserId(request.getCreateUserId());
        if(Objects.equals(PageOwnerType.SUPPLIER.getOwnerType(),request.getOwnerType())
                &&  checkUserId(request.getCreateUserId())){
            //饷店人员修改商家页面 日志要改成饷店
            operationSaveRequest.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
        }else{
            operationSaveRequest.setOwnerType(request.getOwnerType());
        }
        operationLogService.saveOperationLog(operationSaveRequest);
        return new InnerResult<>(Boolean.TRUE, null);
    }



}
