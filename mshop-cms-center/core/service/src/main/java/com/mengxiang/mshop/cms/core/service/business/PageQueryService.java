package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;

import java.util.List;
import java.util.Map;

/**
 * @Auther: zhangmoxun
 * @Date: 2023/3/13
 * @Description:
 */
public interface PageQueryService {
    
    
    /**
     * 页面组件详情(携带版本)
     */
    String queryComponentsByDb(String pageCode,String version);

    String queryComponentsByPreview(String pageCode,String version,AggrBaseReqModule aggrBaseReqModule);
    
    /**
     * 店铺页面 查询
     */
    PageBO detailByPageType(String ownerId,String pageType);


    PageBO detailToBeforePublished(String pageCode);

    PageBO detailToNewPage(String pageCode, String version);

    Result<Pagination<PageSelectResult>> pageSelect(PageSearchRequest req);

    Result<Pagination<PageSelectResult>> pageSelectV2(PageSearchRequest req);

    PageBO detailByCache (String pageCode,String version);

    PageBO detailByDb (String pageCode,String version);

    PageBO detailByPreview (String pageCode,String version,AggrBaseReqModule aggrBaseReqModule);

    PageBO queryByTemplateCode (String templateCode,String ownerId,String ownerType);

    String convertComponents(List<MshopComponentInstanceDo> componentInstance, List<MshopComponentInstanceDetailDo> details);

    String convertComponentsByPreview(List<MshopComponentInstanceDo> componentInstance,List<MshopComponentInstanceDetailDo> details, AggrBaseReqModule aggrBaseReqModule);

    String convertEffectiveComponentJson(String componentsStr, AggrBaseReqModule aggrBaseReqModule);

    List<Long> getComponentRuleIds(String componentsStr);

    List<Long> getComponentRuleIds(String componentsStr, ComponentTypeEnum componentType);

    List<TimeConfigBO> getComponentEffectiveTime(String componentsStr, ComponentTypeEnum componentType);


    /**
     * 批量查询页面信息
     * @param pageCodeList
     * @return
     */
    Map<String,PageBO> findDetailByCache(List<String> pageCodeList);
}
