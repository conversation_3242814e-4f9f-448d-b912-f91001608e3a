package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogActionEnum;
import com.mengxiang.mshop.cms.core.model.request.CreatePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
public interface PageOperateService {
    /**
     * 保存/编辑 页面
     * @param request
     * @return
     */
    PageBO save(SavePageRequest request);

    /**
     * 保存/编辑 页面配置信息
     * @param request
     * @return
     */
    PageBO savePageBaseConfig(SavePageRequest request);

    /**
     * 发布页面
     * @param pageCode
     * @param publishVersion
     * @return
     */
    Integer publishPage (String pageCode,String publishVersion);

    /**
     * 修改页面状态
     * @param pageCode
     * @param version
     * @param status
     * @return
     */
    Boolean updatePageStatus(String pageCode, String version, Integer status);

    /**
     * 失效在线页面
     * @param pageCode
     * @return
     */
    Boolean setPageInvalidation (String pageCode,String updateBy,String updateUserId);

    void saveOperationLog(OperationLogActionEnum action, String pageCode, Integer beforeStatus, Integer afterStatus,String createBy,String createUserId,String ownerType);

    boolean clearPageOnLine(String pageCode,String type,String ownerId,String version);

    Boolean pageCheckPublish(String pageCode, String version);

    void sendPublishPageMQ(String pageCode,String publishVersion,Integer status);

    /**
     * 设置主页
     */
    void setPageIndex(String pageCode,String pageType,String ownerId,String ownerType);
}
