package com.mengxiang.mshop.cms.core.service.context;

import com.mengxiang.base.common.process.model.BusinessContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description: 通用上下文
 * @param <T>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CmsContext<T> extends BusinessContext {
    /**
     * 存储进入引擎的原始业务对象
     */
    private T request;

    /**
     * 请求来源，akapp,h5,other
     */
    private String requestSource;


}

