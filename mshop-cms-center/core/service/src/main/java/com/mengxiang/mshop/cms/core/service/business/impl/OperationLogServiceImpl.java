package com.mengxiang.mshop.cms.core.service.business.impl;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dao.MshopOperationLogDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopOperationLogDo;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogBizTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.OperationLogRequest;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.result.OperationLogResult;
import com.mengxiang.mshop.cms.core.service.business.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 操作日志实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class OperationLogServiceImpl implements OperationLogService {


    
    @Autowired
    private MshopOperationLogDao mshopOperationLogDao;

    
    @Override
    public Result<Pagination<OperationLogResult>> operationLogPage(OperationLogRequest req) {

        OperationLogBizTypeEnum enumByCode = OperationLogBizTypeEnum.getEnumByCode(req.getBizType());
        if(null == enumByCode){
            log.warn("[operationLogPage]没有枚举类型");
            return Result.success(new Pagination(req, 0L, new ArrayList<>()));
        }

        Integer pageSize = (null == req.getPageSize()) ? 10 : req.getPageSize();
        Integer pageIndex = (null == req.getPageIndex() || req.getPageIndex() < 1) ? 1 : req.getPageIndex();
        int startIndex = (pageIndex - 1) * pageSize;
        
        //总数查询
        Long count = mshopOperationLogDao.countByBizCode(req.getBizCode(), req.getBizType(),req.getAction());
        if (null == count || count.intValue() == 0) {
            log.warn("[operationLogPage]没有查询到operationLog.count数据");
            return Result.success(new Pagination(req, 0L, new ArrayList<>()));
        }
        
        //LIST查询
        List<MshopOperationLogDo> logDoList = mshopOperationLogDao
                .queryListByBizCode(pageSize, startIndex, req.getBizCode(), req.getBizType(),req.getAction());
        if (CollectionUtils.isEmpty(logDoList)) {
            log.warn("[operationLogPage]没有查询到operationLog数据");
            return Result.success(new Pagination(req, 0L, new ArrayList<>()));
        }

        List<OperationLogResult> resultList = new ArrayList<>();
        for (int i = 0; i < logDoList.size(); i++) {
            MshopOperationLogDo logDo = logDoList.get(i);
            OperationLogResult result = new OperationLogResult();
            result.setAction(logDo.getAction());
            result.setRemark(logDo.getRemark());
            result.setBizType(logDo.getBizType());
            result.setBizCode(logDo.getBizCode());
            result.setBeforeData(logDo.getBeforeData());
            result.setAfterData(logDo.getAfterData());
            result.setCreateBy(logDo.getCreateBy());
            result.setCreateTime(logDo.getCreateTime());
            result.setOwnerType(logDo.getOwnerType());
            resultList.add(result);
        }

        Pagination pagination = new Pagination(req, count, resultList);
        return Result.success(pagination);
    }

    @Override
    public Result<Boolean> saveOperationLog(OperationLogSaveRequest req) {

        OperationLogBizTypeEnum enumByCode = OperationLogBizTypeEnum.getEnumByCode(req.getBizType());
        if(null == enumByCode){
            throw new BusinessException("无对应业务类型");
        }
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getAction()), "行为不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getBizCode()), "业务编号不能为空");

        MshopOperationLogDo operationLogDo = new MshopOperationLogDo();
        operationLogDo.setAction(req.getAction());
        operationLogDo.setAfterData(req.getAfterData());
        operationLogDo.setBeforeData(req.getBeforeData());
        operationLogDo.setBizType(req.getBizType());
        operationLogDo.setCreateBy(req.getCreateBy());
        operationLogDo.setRemark(req.getRemark());
        operationLogDo.setBizCode(req.getBizCode());
        operationLogDo.setCreateTime(new Date());
        operationLogDo.setCreateUserId(req.getCreateUserId());
        operationLogDo.setOwnerType(req.getOwnerType());
        int insert = mshopOperationLogDao.insert(operationLogDo);
        return Result.success(insert == 1);
    }
}
