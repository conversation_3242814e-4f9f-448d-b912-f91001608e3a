package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.cube.CubeComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.cube.CubeComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 图片魔方组件
 * <AUTHOR>
 */
@Service
public class CubeInfoServiceImpl extends AbstractComponentInfoService<CubeComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    public CubeComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        CubeComponentBO image = JSON.parseObject(metaConfig, CubeComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),image);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, CubeComponentConfigDetailBO.class))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .ifPresent(image::setImageList);
        return image;
    }

    @Override
    public CubeComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        CubeComponentBO image = JSON.parseObject(metaConfig, CubeComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),image);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(image.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(image.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, CubeComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(image::setImageList);
        return image;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public CubeComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        CubeComponentBO image = JSON.parseObject(componentStr, CubeComponentBO.class);
        if (Objects.nonNull(image.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(image.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(image.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(image.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        if (CollectionUtil.isNotEmpty(image.getImageList())) {
//            过滤无效配置详情
            List<CubeComponentConfigDetailBO> list = this.queryEffectiveDetails(image.getImageList());
            if (CollectionUtil.isEmpty(list)) {
                return null;
            }
            image.setImageList(list);
        }
        return image;
    }
    
    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.CUBE;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
