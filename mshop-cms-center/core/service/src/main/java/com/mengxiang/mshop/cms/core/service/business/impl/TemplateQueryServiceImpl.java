package com.mengxiang.mshop.cms.core.service.business.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageTemplateDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageTemplateDo;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import com.mengxiang.mshop.cms.core.service.business.TemplateQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Service
@Slf4j
public class TemplateQueryServiceImpl implements TemplateQueryService {

    @Autowired
    private MshopPageTemplateDao pageTemplateDao;

    @Override
    public PageTemplateResult queryByTemplateCode (String templateCode) {
        MshopPageTemplateDo mshopPageTemplateDo = pageTemplateDao.getByTemplateCode(templateCode);
        return convertToPageTemplateResult(mshopPageTemplateDo).get();
    }

    @Override
    public List<PageTemplateResult> list(String ownerId, String ownerType, String pageUseType) {
        List<MshopPageTemplateDo> templates = pageTemplateDao.getByOwnerId(ownerId, ownerType);
        if (CollectionUtils.isEmpty(templates)) {
            return Collections.emptyList();
        }
        return templates.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getPageUseType()) && Arrays.asList(t.getPageUseType().split(",")).contains(pageUseType))
                .map(this::convertToPageTemplateResult)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private Optional<PageTemplateResult> convertToPageTemplateResult(MshopPageTemplateDo template) {
        PageTemplateResult pageTemplateResult = new PageTemplateResult();
        pageTemplateResult.setTemplateCode(template.getTemplateCode());
        pageTemplateResult.setTemplateName(template.getTemplateName());
        pageTemplateResult.setTemplateDesc(template.getTemplateDesc());
        pageTemplateResult.setTemplateImageUrl(template.getTemplateImageUrl());
        pageTemplateResult.setPageUseType(template.getPageUseType());
        if (StringUtils.isEmpty(template.getComponentUseRule())) {
            return Optional.of(pageTemplateResult);
        }
        JSONArray array = JSONUtil.parseArray(template.getComponentUseRule());
        List<PageTemplateResult.TemplateComponentUseRule> componentUseRules = JSONUtil.toList(array, PageTemplateResult.TemplateComponentUseRule.class);
        pageTemplateResult.setComponentUseRule(componentUseRules);
        return Optional.of(pageTemplateResult);
    }



}
