package com.mengxiang.mshop.cms.core.service.job;

import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 页面缓存预热、稽核
 * <AUTHOR>
 */
@Component
@Slf4j
public class PageCacheJobHandler {

    @Autowired
    private PageCacheService pageCacheService;

    @XxlJob("pageCacheJobHandler")
    public ReturnT<String> pageCacheJobHandler(String params) {
        try {
            pageCacheService.compensatePageCache();
        } catch (Exception e) {
            log.error("pageCacheJobHandler 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }
}
