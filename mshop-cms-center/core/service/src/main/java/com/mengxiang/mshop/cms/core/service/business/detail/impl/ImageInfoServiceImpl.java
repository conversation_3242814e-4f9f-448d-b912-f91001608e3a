package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImageComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImgComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImgComponentHoleConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentDetailTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 图片组件
 * <AUTHOR>
 */
@Service
public class ImageInfoServiceImpl extends AbstractComponentInfoService<ImageComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    public ImageComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        ImageComponentBO image = JSON.parseObject(metaConfig, ImageComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),image);
        if (CollectionUtil.isNotEmpty(componentDetail)) {
            List<ImgComponentConfigDetailBO> hotConfigDetails = componentDetail.stream()
                    .filter(detailDo -> ComponentDetailTypeEnum.HOT.getCode().equals(detailDo.getComponentDetailType()))
                    .map(detailDo -> detailBase(detailDo, ImgComponentConfigDetailBO.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            image.setHotConfigDetails(hotConfigDetails);

            List<ImgComponentHoleConfigDetailBO> holeConfigDetails = componentDetail.stream()
                    .filter(detailDo -> ComponentDetailTypeEnum.HOLE.getCode().equals(detailDo.getComponentDetailType()))
                    .map(detailDo -> detailBase(detailDo, ImgComponentHoleConfigDetailBO.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            image.setHoleConfigDetails(holeConfigDetails);
        }
        return image;
    }

    @Override
    public ImageComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        ImageComponentBO image = JSON.parseObject(metaConfig, ImageComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),image);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(image.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(image.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        if (CollectionUtil.isNotEmpty(componentDetail)) {
            List<ImgComponentConfigDetailBO> hotConfigDetails = componentDetail.stream()
                    .filter(detailDo -> ComponentDetailTypeEnum.HOT.getCode().equals(detailDo.getComponentDetailType()))
                    .map(detailDo -> detailBase(detailDo, ImgComponentConfigDetailBO.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            image.setHotConfigDetails(hotConfigDetails);

            List<ImgComponentHoleConfigDetailBO> holeConfigDetails = componentDetail.stream()
                    .filter(detailDo -> ComponentDetailTypeEnum.HOLE.getCode().equals(detailDo.getComponentDetailType()))
                    .map(detailDo -> detailBase(detailDo, ImgComponentHoleConfigDetailBO.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            image.setHoleConfigDetails(holeConfigDetails);
        }
        return image;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public ImageComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        ImageComponentBO image = JSON.parseObject(componentStr, ImageComponentBO.class);
        if (Objects.nonNull(image.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(image.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(image.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(image.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        //过滤无效配置详情
        if (CollectionUtil.isNotEmpty(image.getHotConfigDetails())) {
            List<ImgComponentConfigDetailBO> list = this.queryEffectiveDetails(image.getHotConfigDetails());
            if (CollectionUtil.isEmpty(list)) {
                return null;
            }
            image.setHotConfigDetails(list);
        }

        if (CollectionUtil.isNotEmpty(image.getHoleConfigDetails())) {

            List<ImgComponentHoleConfigDetailBO> holeList = this.queryEffectiveDetails(image.getHoleConfigDetails());
            if (CollectionUtil.isEmpty(holeList)) {
                return null;
            }
            image.setHoleConfigDetails(holeList);
        }
        return image;
    }
    
    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.IMAGE;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
