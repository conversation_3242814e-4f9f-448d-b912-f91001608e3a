package com.mengxiang.mshop.cms.core.service.job;

import cn.hutool.core.collection.CollectionUtil;
import com.aikucun.common2.base.Result;
import com.aikucun.dc.aiward.facade.stub.rule.enumfile.SourceCode;
import com.aikucun.dc.aiward.facade.stub.rule.sell.RuleProductsDto;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleFacade;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDetailDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.PageQueryVO;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.MarketTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.service.business.PrivateMarketService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.business.detail.ComponentDetailInfoContainer;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PrivateMarketExpireClearJobHandler {
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;
    @Resource
    private SellRuleFacade sellRuleFacade;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Autowired
    private PrivateMarketService privateMarketService;

    @XxlJob("privateMarketExpireClearJobHandler")
    public ReturnT<String> execute(String params) {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(PageInstanceStatusEnum.PUBLISH.getCode());
        statusList.add(PageInstanceStatusEnum.EXECUTORY.getCode());
        List<MshopPageInstanceDo> instanceDos = mshopPageInstanceDao.queryByStatusList(statusList);
        if (CollectionUtil.isEmpty(instanceDos)) {
            return ReturnT.SUCCESS;
        }
        for (MshopPageInstanceDo page : instanceDos) {
            if (StringUtils.isEmpty(page.getVersion())) {
                continue;
            }
            //只处理私密会场
            if (MarketTypeEnum.NORMAL.getCode().equals(page.getMarketType())){
                continue;
            }
            MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getByPageCode(page.getPageCode(),page.getVersion());
            if (Objects.isNull(mshopPageDraftDo) || StringUtils.isBlank(mshopPageDraftDo.getPrivateMarketConfig())) {
                continue;
            }
            PrivateMarketConfigBO privateMarketConfigBO = JSON.parseObject(mshopPageDraftDo.getPrivateMarketConfig(), PrivateMarketConfigBO.class);
            String endTimeStr = privateMarketConfigBO.getEndTime();
            //如果结束时间小于当前时间，说明活动已经结束
            LocalDateTime endDate = LocalDateTime.parse(endTimeStr, formatter);
            if (endDate.isBefore(LocalDateTime.now())){
                //执行失效
                privateMarketService.expirePrivateRule(page.getPageCode());
            }
        }
        return ReturnT.SUCCESS;
    }
}
