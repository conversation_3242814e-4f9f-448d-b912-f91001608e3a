package com.mengxiang.mshop.cms.core.service.business.detail.impl;

import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.text.SeparatorComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/4/4
 * @Description:
 */
@Service
public class SeparatorInfoServiceImpl extends AbstractComponentInfoService<SeparatorComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    protected SeparatorComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        return JSON.parseObject(metaConfig, SeparatorComponentBO.class);
    }

    @Override
    protected SeparatorComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        return JSON.parseObject(metaConfig, SeparatorComponentBO.class);
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public SeparatorComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        SeparatorComponentBO separator = JSON.parseObject(componentStr, SeparatorComponentBO.class);
        if (Objects.nonNull(separator.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(separator.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }

        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(separator.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(separator.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        return separator;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.SEPARATOR;
    }


    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
