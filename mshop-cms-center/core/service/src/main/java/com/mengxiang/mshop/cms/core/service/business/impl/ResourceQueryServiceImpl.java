package com.mengxiang.mshop.cms.core.service.business.impl;

import com.akucun.mshop.common.util.BeanCopyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.common.dal.dao.MshopResourceComponentDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.ResourceQueryVO;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.resource.BannerResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.DiamondResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.StartupAdvertisementBO;
import com.mengxiang.mshop.cms.core.model.enums.ResourceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import com.mengxiang.mshop.cms.core.service.business.ResourceQueryService;
import com.mengxiang.mshop.cms.core.service.business.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ResourceQueryServiceImpl implements ResourceQueryService {

    @Autowired
    private MshopResourceComponentDao resourceComponentDao;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 查询开机广告
     */
    @Override
    public StartupAdvertisementBO findStartupAdvertisement(ResourceRequest req){
        String key = String.format(PageConstant.RESOURCE_CACHE_KEY,ResourceTypeEnum.STARTUPADVERTISEMENT, req.getOwnerType(),req.getOwnerId(),req.getTenantId());
        String jsonString =  redisTemplate.opsForValue().get(key);
        if(StringUtils.isNotBlank(jsonString)){
            return JSON.parseObject(jsonString,StartupAdvertisementBO.class);
        }
        // 获取查询对象
        ResourceQueryVO pageQueryVO = BeanCopyUtil.copy(req, ResourceQueryVO.class);
        pageQueryVO.setStatus(ResourceStatusEnum.PUBLISH.getCode());
        pageQueryVO.setCategoryId(null);
        pageQueryVO.setResourcePageType(null);
        pageQueryVO.setResourceType(ResourceTypeEnum.STARTUPADVERTISEMENT.getCode());
        List<MshopResourceComponentDo> list = resourceComponentDao.selectList(pageQueryVO);
        if(CollectionUtils.isNotEmpty(list)){
            StartupAdvertisementBO result = BeanCopyUtil.copy(list.get(0),StartupAdvertisementBO.class);
            StartupAdvertisementBO detail = JSONObject.parseObject(list.get(0).getConfigDetail()
                    , StartupAdvertisementBO.class);
            result.setCarouselConfigDetails(detail.getCarouselConfigDetails());
            result.setIosImgUrl1(detail.getIosImgUrl1());
            result.setIosImgUrl2(detail.getIosImgUrl2());
            result.setAndroidImgUrl(detail.getAndroidImgUrl());
            redisTemplate.opsForValue().set(key, JSON.toJSONString(result), 120, TimeUnit.SECONDS);
            return result;
        }else{
            //防止缓存穿透
            redisTemplate.opsForValue().set(key, JSON.toJSONString(new StartupAdvertisementBO()), 30, TimeUnit.SECONDS);
        }
        return null;
    }


    /**
     * 查询banner
     */
    @Override
    public List<BannerResourceBO> findBannerList(ResourceRequest req){
        String key = String.format(PageConstant.RESOURCE_CACHE_KEY,ResourceTypeEnum.BANNER.getCode()+":"+req.getCategoryId()+":"+req.getResourcePageType(), req.getOwnerType(),req.getOwnerId(),req.getTenantId());
        String jsonString =  redisTemplate.opsForValue().get(key);
        if(StringUtils.isNotBlank(jsonString)){
            return JSONArray.parseArray(jsonString,BannerResourceBO.class);
        }
        // 获取查询对象
        ResourceQueryVO pageQueryVO = BeanCopyUtil.copy(req, ResourceQueryVO.class);
        pageQueryVO.setStatus(ResourceStatusEnum.PUBLISH.getCode());
        pageQueryVO.setResourceType(ResourceTypeEnum.BANNER.getCode());
        List<MshopResourceComponentDo> list = resourceComponentDao.selectList(pageQueryVO);
        if(CollectionUtils.isNotEmpty(list)){
            List<BannerResourceBO> result =list.stream().map(x->{
                BannerResourceBO bo = BeanCopyUtil.copy(x,BannerResourceBO.class);
                bo.setCarouselConfigDetails(JSONObject.parseArray(x.getConfigDetail()
                        , BannerResourceBO.BannerResourceConfig.class));
                return bo;
            }).collect(Collectors.toList());
            redisTemplate.opsForValue().set(key, JSON.toJSONString(result), 120, TimeUnit.SECONDS);
            return result;
        }else{
            //防止缓存穿透
            redisTemplate.opsForValue().set(key, JSON.toJSONString(Lists.newArrayList()), 30, TimeUnit.SECONDS);
        }
        return null;
    }


    /**
     * 查询banner
     */
    @Override
    public List<DiamondResourceBO.DiamondResourceConfig> findDiamond(ResourceRequest req){
        String key = String.format(PageConstant.RESOURCE_CACHE_KEY,ResourceTypeEnum.DIAMOND, req.getOwnerType(),req.getOwnerId(),req.getTenantId());
        String jsonString =  redisTemplate.opsForValue().get(key);
        if(StringUtils.isNotBlank(jsonString)){
            return JSONArray.parseArray(jsonString,DiamondResourceBO.DiamondResourceConfig.class);
        }
        // 获取查询对象
        DiamondResourceBO diamondResourceBO = BeanCopyUtil.copy(req,DiamondResourceBO.class);
        List<DiamondResourceBO.DiamondResourceConfig> list = resourceService.findDiamondResourceList(diamondResourceBO);
        if(CollectionUtils.isNotEmpty(list)){
            redisTemplate.opsForValue().set(key, JSON.toJSONString(list), 120, TimeUnit.SECONDS);
        }else{
            //防止缓存穿透
            redisTemplate.opsForValue().set(key, JSON.toJSONString(Lists.newArrayList()), 30, TimeUnit.SECONDS);
        }
        return Lists.newArrayList();
    }


    /**
     * 查询导航
     */
    @Override
    public List<NavigationResourceBO.ResourceConfig> findNavigation(ResourceRequest req){
        String key = String.format(PageConstant.RESOURCE_CACHE_KEY,ResourceTypeEnum.NAVIGATION, req.getOwnerType(),req.getOwnerId(),req.getTenantId());
        String jsonString =  redisTemplate.opsForValue().get(key);
        if(StringUtils.isNotBlank(jsonString)){
            return JSONArray.parseArray(jsonString, NavigationResourceBO.ResourceConfig.class);
        }
        return Lists.newArrayList();
    }
}
