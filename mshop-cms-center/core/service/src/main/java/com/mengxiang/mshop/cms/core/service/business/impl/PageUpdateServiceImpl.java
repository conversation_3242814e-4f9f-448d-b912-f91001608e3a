package com.mengxiang.mshop.cms.core.service.business.impl;

import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.mengxiang.mshop.cms.core.service.business.PageUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PageUpdateServiceImpl implements PageUpdateService {
    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private PageCacheService pageCacheService;

    @Transactional
    @Override
    public void updateOnlyPageByPageType(String pageCode,String currentPageCode,String ownerId,String ownerType){
//        if(StringUtils.isNotBlank(currentPageCode)){
//            把当前主页设置成店铺会场类型
//            mshopPageInstanceDao.updatePageType(currentPageCode, PageType.SHOP_MICRO_PAGE.getType());
//        }
        //保证主页唯一,修改其他页面类型
        mshopPageInstanceDao.updatePageTypeByOwnerId(ownerId, PageType.SHOP_MICRO_PAGE.getType(),ownerType,PageType.SHOP_PAGE.getType());
        //设置主页
        mshopPageInstanceDao.updatePageType(pageCode, PageType.SHOP_PAGE.getType());
        //删除之前的数据
        pageCacheService.clearPageJsonByTypeAndOwnerId(PageType.SHOP_PAGE.getType(),ownerId);
        //更新缓存
        pageCacheService.syncPageJsonByTypeAndOwnerId(PageType.SHOP_PAGE.getType(),ownerId,pageCode);
    }
}
