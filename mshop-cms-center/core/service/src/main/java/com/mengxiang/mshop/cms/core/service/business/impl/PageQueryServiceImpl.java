package com.mengxiang.mshop.cms.core.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.akucun.mshop.common.util.BeanCopyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.*;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.*;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.*;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ProcessStatusRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ProcessStatusResp;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.utils.ComponentUtils;
import com.mengxiang.mshop.cms.core.service.business.*;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.business.detail.ComponentDetailInfoContainer;
import com.mengxiang.mshop.cms.core.service.enums.ProcessStatusEnum;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Service
@Slf4j
public class PageQueryServiceImpl implements PageQueryService {

    @Autowired
    private ComponentService componentService;
    
    @Autowired
    private ComponentDetailService componentDetailService;
    
    @Autowired
    private ComponentDetailInfoContainer componentDetailInfoContainer;
    
    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;
    
    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Autowired
    private PageCacheService pageCacheService;

    @Autowired
    private ContentCheckService contentCheckService;

    @Autowired
    private WorkbenchService workbenchService;

    @ApolloJsonValue("${default.timeConfig.jsonStr}")
    private TimeConfigBO defaultTimeConfig;

    @Value("${mshop-cms-center.searchTitleNameV2Flag:true}")
    private Boolean searchTitleNameV2Flag;

    @Override
    public PageBO queryByTemplateCode (String templateCode,String ownerId,String ownerType) {
        MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.querySystemPageByTemplateCode(templateCode,ownerId,ownerType);
        if (Objects.isNull(mshopPageInstanceDo)) {
            return null;
        }
        if (StringUtils.isEmpty(mshopPageInstanceDo.getVersion())) {
            log.warn("queryByTemplateCode version is null, templateCode:{} pageCode:{}",templateCode,mshopPageInstanceDo.getPageCode());
            return null;
        }
        return detailToNewPage(mshopPageInstanceDo.getPageCode(),mshopPageInstanceDo.getVersion());
    }

    @Override
    public PageBO detailByCache (String pageCode,String version) {
        return Optional.ofNullable(version)
                .map(v -> pageCacheService.getPageJsonByVersion(pageCode, v))
                .orElse(pageCacheService.getPageJsonByCode(pageCode))
                .map(json -> JSONObject.parseObject(json, PageBO.class))
                .filter(Objects::nonNull)
                .filter(page -> TimeConfigUtils.checkRuleTime(page.getTimeConfig()) && PageInstanceStatusEnum.isShowStatus(page.getStatus()))
                .map(page -> {
                    String componentStr = convertEffectiveComponentJson(page.getComponents(),null);
                    page.setComponents(componentStr);
                    return page;
                })
                .orElseGet(() -> {
                    log.warn("detailByCache error pageCode:{}", pageCode);
                    return null;
                });
    }

    @Override
    public PageBO detailByDb (String pageCode,String version) {
        MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getByPageCode(pageCode, version);
        MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(pageCode, null);
        if (Objects.isNull(mshopPageDraftDo) || Objects.isNull(mshopPageInstanceDo)) {
            throw new BusinessException("页面不存在");
        }
        PageBO page = BeanCopyUtil.copy(mshopPageDraftDo,PageBO.class);
        page.setStatus(mshopPageInstanceDo.getStatus());
        Optional.ofNullable(mshopPageDraftDo.getShareConfig())
                .filter(StringUtils::isNotEmpty)
                .map(config -> JSONObject.parseObject(config, PageShareConfigBO.class))
                .ifPresent(page::setShareConfig);
        Optional.ofNullable(mshopPageDraftDo.getTimeConfig())
                .filter(StringUtils::isNotEmpty)
                .map(config -> JSONObject.parseObject(config, TimeConfigBO.class))
                .ifPresent(timeConfig -> {
                    page.setTimeConfig(timeConfig);
                });
        Optional.ofNullable(mshopPageDraftDo.getPrivateMarketConfig())
                .filter(StringUtils::isNotEmpty)
                .map(config -> JSONObject.parseObject(config, PrivateMarketConfigBO.class))
                .ifPresent(page::setPrivateMarketConfig);
        page.setMarketType(MarketTypeEnum.valueOf(mshopPageInstanceDo.getMarketType()));
        page.setOwnerId(mshopPageInstanceDo.getOwnerId());
        page.setOwnerType(mshopPageInstanceDo.getOwnerType());
        page.setChannel(mshopPageInstanceDo.getChannel());
        page.setTemplateCode(mshopPageInstanceDo.getTemplateCode());
        page.setType(mshopPageInstanceDo.getType());
        page.setTenantId(mshopPageInstanceDo.getTenantId());
        String components = queryComponentsByDb(pageCode,mshopPageDraftDo.getVersion());
        page.setComponents(components);
        return page;
    }
    @Override
    public PageBO detailByPreview (String pageCode,String version,AggrBaseReqModule aggrBaseReqModule) {
        MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getByPageCode(pageCode, version);
        MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(pageCode, null);
        if (Objects.isNull(mshopPageDraftDo) || Objects.isNull(mshopPageInstanceDo)) {
            throw new BusinessException("页面不存在");
        }
        PageBO page = BeanCopyUtil.copy(mshopPageDraftDo,PageBO.class);
        page.setStatus(mshopPageInstanceDo.getStatus());
        Optional.ofNullable(mshopPageDraftDo.getShareConfig())
                .filter(StringUtils::isNotEmpty)
                .map(config -> JSONObject.parseObject(config, PageShareConfigBO.class))
                .ifPresent(page::setShareConfig);
        Optional.ofNullable(mshopPageDraftDo.getTimeConfig())
                .filter(StringUtils::isNotEmpty)
                .map(config -> JSONObject.parseObject(config, TimeConfigBO.class))
                .ifPresent(timeConfig -> {
                    page.setTimeConfig(timeConfig);
                });
        Optional.ofNullable(mshopPageDraftDo.getPrivateMarketConfig())
                .filter(StringUtils::isNotEmpty)
                .map(config -> JSONObject.parseObject(config, PrivateMarketConfigBO.class))
                .ifPresent(page::setPrivateMarketConfig);
        page.setMarketType(MarketTypeEnum.valueOf(mshopPageInstanceDo.getMarketType()));
        page.setOwnerId(mshopPageInstanceDo.getOwnerId());
        page.setOwnerType(mshopPageInstanceDo.getOwnerType());
        page.setChannel(mshopPageInstanceDo.getChannel());
        page.setTemplateCode(mshopPageInstanceDo.getTemplateCode());
        page.setType(mshopPageInstanceDo.getType());
        page.setTenantId(mshopPageInstanceDo.getTenantId());
        String components = queryComponentsByPreview(pageCode,mshopPageDraftDo.getVersion(),aggrBaseReqModule);
        page.setComponents(components);
        return page;
    }

    @Override
    public String queryComponentsByDb(String pageCode, String version) {
        List<MshopComponentInstanceDo> componentInstance = componentService.queryByPageCode(pageCode, version);
        List<MshopComponentInstanceDetailDo> details = componentDetailService.queryByPageCode(pageCode, version);
        return convertComponents(componentInstance,details);
    }

    @Override
    public String queryComponentsByPreview(String pageCode, String version,AggrBaseReqModule aggrBaseReqModule) {
        List<MshopComponentInstanceDo> componentInstance = componentService.queryByPageCode(pageCode, version);
        List<MshopComponentInstanceDetailDo> details = componentDetailService.queryByPageCode(pageCode, version);
        return convertComponentsByPreview(componentInstance,details,aggrBaseReqModule);
    }

    @Override
    public String convertComponentsByPreview(List<MshopComponentInstanceDo> componentInstance,List<MshopComponentInstanceDetailDo> details, AggrBaseReqModule aggrBaseReqModule) {
        if (CollectionUtils.isEmpty(componentInstance)) {
            return "";
        }
        Map<String, List<MshopComponentInstanceDetailDo>> detailsMap = Optional.ofNullable(details)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(MshopComponentInstanceDetailDo::getComponentCode));
        Set<String> needDelComponentCodes = new HashSet<>();
        Set<String> validComponentCodes = new HashSet<>();

        List<JSONObject> list = componentInstance.stream()
                .map(instanceDo -> {
                    ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(instanceDo.getType());
                    String componentCode = instanceDo.getComponentCode();
                    AbstractComponentInfoService componentConvert = componentDetailInfoContainer.getComponentConvert(componentType);
                    String resp = componentConvert.getComponentByPreview(instanceDo, detailsMap.getOrDefault(instanceDo.getComponentCode(), Collections.emptyList()),aggrBaseReqModule);
                    if (componentType.equals(ComponentTypeEnum.NAVIGATION)) {
                        String metaConfig = instanceDo.getMetaConfig();
                        NavigationComponentBO navigation = JSON.parseObject(metaConfig, NavigationComponentBO.class);
                        if (navigation.getStyleType().equals(NavigationStyleTypeEnum.ARCHOR.getCode()) && StringUtils.isEmpty(resp)) {
                            needDelComponentCodes.addAll(navigation.getComponentCodes().stream().map(x -> x.getComponentCode()).collect(Collectors.toSet()));
                        }
                    }
                    if (StringUtils.isEmpty(resp)) {
                        return null;
                    }
                    validComponentCodes.add(componentCode);
                    return JSONObject.parseObject(resp);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<JSONObject> finalList = doNavigationComponents(list,validComponentCodes,needDelComponentCodes);
        if (CollectionUtil.isNotEmpty(list)) {
            finalList.sort(Comparator.comparing(obj -> obj.getIntValue("order")));
        }
        return finalList.toString();
    }

    @Override
    public String convertComponents(List<MshopComponentInstanceDo> componentInstance,List<MshopComponentInstanceDetailDo> details) {
        if (CollectionUtils.isEmpty(componentInstance)) {
            return "";
        }
        Map<String, List<MshopComponentInstanceDetailDo>> detailsMap = Optional.ofNullable(details)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(MshopComponentInstanceDetailDo::getComponentCode));
        JSONArray resp = componentInstance.stream()
                .map(instanceDo -> {
                    ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(instanceDo.getType());
                    AbstractComponentInfoService componentConvert = componentDetailInfoContainer.getComponentConvert(componentType);
                    return componentConvert.getComponent(instanceDo, detailsMap.getOrDefault(instanceDo.getComponentCode(), Collections.emptyList()));
                })
                .filter(StringUtils::isNotBlank)
                .map(JSONObject::parseObject)
                .collect(Collectors.toCollection(JSONArray::new));
        if (Objects.nonNull(resp) && !resp.isEmpty()) {
            resp.sort(Comparator.comparing(obj -> ((JSONObject) obj).getIntValue("order")));
        }
        return resp.toString();
    }

    @Override
    public PageBO detailByPageType(String ownerId,String pageType) {
        Preconditions.checkArgument(!StringUtils.isEmpty(ownerId), "所属者id 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(pageType), "页面类型 不能为空");

        PageType pageTypeEnum = PageType.translate(pageType);
        if (Objects.isNull(pageTypeEnum)) {
            log.warn("detailByPageType pageTypeEnum is null ownerId:{} pageType:{}", ownerId, pageType);
            return null;
        }
        return Optional.ofNullable(pageCacheService.getPageJsonByTypeAndOwnerId(pageType, ownerId)
                .map(pageInfo -> JSONObject.parseObject(pageInfo, PageBO.class))
                .filter(pageResult -> TimeConfigUtils.checkRuleTime(pageResult.getTimeConfig()) && PageInstanceStatusEnum.isShowStatus(pageResult.getStatus()))
                .map(pageResult -> {
                    String componentStr = convertEffectiveComponentJson(pageResult.getComponents(),null);
                    pageResult.setComponents(componentStr);
                    return pageResult;
                })
                .orElse(null))
                .orElseGet(() -> {
                    log.warn("detailByPageType pageInfo is null ownerId:{} pageType:{}", ownerId, pageType);
                    return null;
                });
    }

    /**
     * 包含过滤无效 组件
     * @param componentsStr
     * @return
     */
    @Override
    public String convertEffectiveComponentJson(String componentsStr, AggrBaseReqModule aggrBaseReqModule) {
        Set<String> needDelComponentCodes = new HashSet<>();
        Set<String> validComponentCodes = new HashSet<>();
        List<JSONObject> list = Optional.ofNullable(componentsStr)
                .map(JSONArray::parseArray)
                .map(components -> components.stream()
                        .map(component -> {
                            JSONObject componentObj = (JSONObject) component;
                            String type = componentObj.getString("type");
                            String componentCode = componentObj.getString("componentCode");
                            ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(type);
                            if (Objects.isNull(componentType)) {
                                log.warn("pageInfoParse 不支持的组件类型 type:{}", type);
                                return null;
                            }
                            AbstractComponentInfoService componentConvert = componentDetailInfoContainer.getComponentConvert(componentType);
                            String resp = componentConvert.componentParse(component.toString(), aggrBaseReqModule);
                            if (componentType.equals(ComponentTypeEnum.NAVIGATION)) {
                                NavigationComponentBO navigationComponentBO = JSONObject.toJavaObject(componentObj,NavigationComponentBO.class);
                                if (navigationComponentBO.getStyleType().equals(NavigationStyleTypeEnum.ARCHOR.getCode()) && StringUtils.isEmpty(resp)) {
                                    needDelComponentCodes.addAll(navigationComponentBO.getComponentCodes().stream().map(x -> x.getComponentCode()).collect(Collectors.toSet()));
                                }
                            }
                            if (StringUtils.isEmpty(resp)) {
                                return null;
                            }
                            validComponentCodes.add(componentCode);
                            return JSONObject.parseObject(resp);
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        List<JSONObject> finalList = doNavigationComponents(list,validComponentCodes,needDelComponentCodes);
        if (CollectionUtil.isNotEmpty(finalList)) {
            finalList.sort(Comparator.comparing(obj -> obj.getIntValue("order")));
        }
        return finalList.toString();
    }

    /**
     * 处理电梯导航
     */
    //private List<JSONObject> doNavigationComponents (List<JSONObject> list,Map<String,JSONObject> componentJsonMap,Map<String, List<String>> needDelComponentCodesMap) {
    //    if (CollectionUtils.isEmpty(list)) {
    //        return new ArrayList<>();
    //    }
    //    List<JSONObject> firstFilterList = new ArrayList<>();
    //    List<JSONObject> finalList = new ArrayList<>();
    //    List<String> needDelComponentCodes = new ArrayList<>();
    //    if (!needDelComponentCodesMap.isEmpty()) {
    //        list = list.stream().filter(x -> {
    //            String componentCode = x.getString("componentCode");
    //            List<String> componentCodesBOS = needDelComponentCodesMap.get(componentCode);
    //            if (componentCodesBOS.contains(componentCode)) {
    //                return false;
    //            }
    //            return true;
    //        }).collect(Collectors.toList());
    //    }
    //
    //    for (JSONObject jsonObject : list) {
    //        String type = jsonObject.getString("type");
    //        ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(type);
    //        if (Objects.isNull(componentType)) {
    //            firstFilterList.add(jsonObject);
    //            continue;
    //        }
    //        if (!componentType.equals(ComponentTypeEnum.NAVIGATION)) {
    //            firstFilterList.add(jsonObject);
    //            continue;
    //        }
    //        NavigationComponentBO navigationComponent = JSONObject.toJavaObject(jsonObject,NavigationComponentBO.class);
    //        if (Objects.isNull(navigationComponent) || navigationComponent.getStyleType().equals("0")) {
    //            firstFilterList.add(jsonObject);
    //            continue;
    //        }
    //        List<NavigationComponentConfigDetailBO> details = navigationComponent.getNavigationConfigDetails().stream().map(detail -> {
    //            String archorComponentCode = detail.getArchorComponentCode();
    //            JSONObject obj = componentJsonMap.get(archorComponentCode);
    //            if (Objects.isNull(obj)) {
    //                return null;
    //            }
    //            return detail;
    //        }).filter(Objects::nonNull).collect(Collectors.toList());
    //        if (CollectionUtil.isEmpty(details)) {
    //            List<String> componentCodes = navigationComponent.getComponentCodes().stream().map(x -> x.getComponentCode()).collect(Collectors.toList());
    //            needDelComponentCodes.addAll(componentCodes);
    //            continue;
    //        }
    //        navigationComponent.setNavigationConfigDetails(details);
    //        JSONObject componentJson = (JSONObject) JSONObject.toJSON(navigationComponent);
    //        firstFilterList.add(componentJson);
    //    }
    //    if (CollectionUtil.isNotEmpty(needDelComponentCodes)) {
    //        for (JSONObject jsonObject : firstFilterList) {
    //            String componentCode = jsonObject.getString("componentCode");
    //            if (needDelComponentCodes.contains(componentCode)) {
    //                continue;
    //            }
    //            finalList.add(jsonObject);
    //        }
    //    } else {
    //        finalList = firstFilterList;
    //    }
    //    return finalList;
    //}

    private List<JSONObject> doNavigationComponents(List<JSONObject> list, Set<String> validComponentCodes,Set<String> needDelComponentCodes) {
        List<JSONObject> finalList = list.stream()
                .filter(jsonObject -> {
                    String componentCode = jsonObject.getString("componentCode");
                    return !needDelComponentCodes.contains(componentCode);
                })
                .map(jsonObject -> {
                    String type = jsonObject.getString("type");
                    ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(type);
                    if (componentType != null && componentType.equals(ComponentTypeEnum.NAVIGATION)) {
                        NavigationComponentBO navigationComponent = jsonObject.toJavaObject(NavigationComponentBO.class);
                        if (navigationComponent != null && navigationComponent.getStyleType().equals(NavigationStyleTypeEnum.ARCHOR.getCode())) {
                            List<NavigationComponentConfigDetailBO> validDetails = navigationComponent.getNavigationConfigDetails().stream()
                                    .filter(detail -> validComponentCodes.contains(detail.getArchorComponentCode()))
                                    .collect(Collectors.toList());
                            if (validDetails.isEmpty()) {
                                needDelComponentCodes.addAll(navigationComponent.getComponentCodes().stream().map(x -> x.getComponentCode()).collect(Collectors.toSet()));
                                return null;
                            }
                            navigationComponent.setNavigationConfigDetails(validDetails);
                            jsonObject = (JSONObject) JSONObject.toJSON(navigationComponent);
                        }
                    }
                    return jsonObject;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return finalList.stream().filter(jsonObject -> {
            String componentCode = jsonObject.getString("componentCode");
            return !needDelComponentCodes.contains(componentCode);
        }).collect(Collectors.toList());
    }


    @Override
    public List<Long> getComponentRuleIds(String componentsStr) {
        if (StringUtils.isEmpty(componentsStr)) {
            return Collections.emptyList();
        }
        List<Long> ruleIds = new ArrayList<>();
        JSONArray arr = JSONArray.parseArray(componentsStr);
        arr.stream().forEach(component -> {
            JSONObject componentObj = (JSONObject) component;
            String type = componentObj.getString("type");
            ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(type);
            if (Objects.nonNull(componentType)) {
                AbstractComponentInfoService componentConvert = componentDetailInfoContainer.getComponentConvert(componentType);
                List<Long> list = componentConvert.doGetRuleIds(component.toString());
                ruleIds.addAll(list);
            }
        });
        return ruleIds;
    }

    @Override
    public List<Long> getComponentRuleIds(String componentsStr, ComponentTypeEnum componentType) {

        if (StringUtils.isEmpty(componentsStr)) {
            return Collections.emptyList();
        }
        List<Long> ruleIds = new ArrayList<>();
        JSONArray arr = JSONArray.parseArray(componentsStr);
        arr.stream().filter(component->{
            JSONObject componentObj = (JSONObject) component;
            String type = componentObj.getString("type");
            return Objects.equals(ComponentTypeEnum.getEnumByCode(type), componentType);
        }).forEach(component -> {
            if (Objects.nonNull(componentType)) {
                AbstractComponentInfoService componentConvert = componentDetailInfoContainer.getComponentConvert(componentType);
                List<Long> list = componentConvert.doGetRuleIds(component.toString());
                ruleIds.addAll(list);
            }
        });
        return ruleIds;
    }

    @Override
    public List<TimeConfigBO> getComponentEffectiveTime(String componentsStr, ComponentTypeEnum componentType) {
        JSONArray arr = JSONArray.parseArray(componentsStr);
        List<ComponentDetailBO> componentDetailBOS = new ArrayList<>();


        arr.stream().filter(component -> {
            JSONObject componentObj = (JSONObject) component;
            String type = componentObj.getString("type");
            return Objects.equals(ComponentTypeEnum.getEnumByCode(type), componentType);
        }).forEach(component -> {
            JSONObject componentObj = (JSONObject) component;
            String type = componentObj.getString("type");
            ComponentTypeEnum componentTypeEnum = ComponentTypeEnum.getEnumByCode(type);
            if (Objects.nonNull(componentTypeEnum)) {
                AbstractComponentInfoService componentConvert = componentDetailInfoContainer.getComponentConvert(componentTypeEnum);
                List<ComponentDetailBO> list = componentConvert.doGetComponentDetail(component.toString());
                componentDetailBOS.addAll(list);
            }
        });
        return componentDetailBOS.stream().map(ComponentDetailBO::getTimeConfig).collect(Collectors.toList());
    }


    @Override
    public PageBO detailToBeforePublished(String pageCode) {
        return Optional.ofNullable(mshopPageInstanceDao.getByPageCode(pageCode, null))
                .map(mshopPageInstanceDo -> {
                    if (StringUtils.isEmpty(mshopPageInstanceDo.getVersion())) {
                        throw new BusinessException("没有已发布版本");
                    }
                    if (mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.APPROVAL.getCode()) || mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.DISABLED.getCode())) {
                        throw new BusinessException("此页面状态不支持操作");
                    }
                    String version = mshopPageInstanceDo.getVersion();
                    if (mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.FAIL.getCode()) ||mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.EXECUTORY.getCode())) {
                        MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getLastByPageCode(pageCode,PageInstanceStatusEnum.PUBLISH.getCode());
                        if (Objects.isNull(mshopPageDraftDo)) {
                            throw new BusinessException("没有已发布版本");
                        }
                        version = mshopPageDraftDo.getVersion();
                    }
                    PageBO pageBO = detailByDb(mshopPageInstanceDo.getPageCode(), version);
                    pageBO.setTimeConfig(defaultTimeConfig);
                    pageBO.setStatus(PageInstanceStatusEnum.DRAFT.getCode());
                    return pageBO;
                })
                .orElse(null);
    }
    
    @Override
    public PageBO detailToNewPage(String pageCode, String version) {
        PageBO source = detailByDb(pageCode, version);
        source.setPageCode("");
        source.setTimeConfig(defaultTimeConfig);
        source.setStatus(PageInstanceStatusEnum.DRAFT.getCode());
        if (StringUtils.isEmpty(source.getComponents())) {
            return source;
        }
        JSONArray jsonArray = JSONArray.parseArray(source.getComponents());
        jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .map(jsonObject -> {
                    String componentId = jsonObject.getString("componentCode");
                    jsonObject.put("componentCode", "");
                    jsonObject.put("componentId", componentId);
                    String componentTypeStr = jsonObject.getString("type");
                    ComponentTypeEnum componentTypeEnum = ComponentTypeEnum.getEnumByCode(componentTypeStr);
                    if(componentTypeEnum.equals(ComponentTypeEnum.NAVIGATION)) {
                        JSONArray navigationJsonArray = jsonObject.getJSONArray("componentCodes");
                        if(Objects.nonNull(navigationJsonArray)) {
                            for (Object nObj : navigationJsonArray) {
                                JSONObject nJsonObject = (JSONObject) nObj;
                                String nComponentCode = nJsonObject.getString("componentCode");
                                nJsonObject.put("componentCode", "");
                                nJsonObject.put("componentId", nComponentCode);
                            }
                        }
                    }
                    return Optional.ofNullable(componentTypeEnum)
                            .map(ComponentUtils.COMPONENT_DETAIL_MAP::get)
                            .filter(StringUtils::isNotBlank)
                            .map(jsonObject::getJSONArray)
                            .orElse(new JSONArray())
                            .stream()
                            .map(obj -> (JSONObject) obj)
                            .peek(detailJson -> {
                                detailJson.put("componentDetailCode", "");
                                if(componentTypeEnum.equals(ComponentTypeEnum.NAVIGATION)) {
                                    String archorComponentCode = detailJson.getString("archorComponentCode");
                                    detailJson.put("archorComponentCode", "");
                                    detailJson.put("componentId", archorComponentCode);
                                }
                            })
                            .collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        source.setComponents(jsonArray.toJSONString());
        return source;
    }
    
    @Override
    public Result<Pagination<PageSelectResult>> pageSelect(PageSearchRequest req) {
        List<String> draftPageCodeList = null;
        if (CollectionUtil.isEmpty(req.getPageCodeList())) {
            draftPageCodeList = this.getDraftPageCodes(req);
            if (CollectionUtil.isEmpty(draftPageCodeList)) {
                log.info("[pageSelect]没有查询到数据");
                return Result.success(null);
            }
        }

        // 获取查询对象
        PageQueryVO pageQueryVO = this.getPageQueryVO(req);

        Long count = mshopPageInstanceDao.queryPageCount(pageQueryVO,draftPageCodeList);
        if (count == null || count.intValue() == 0) {
            log.info("[pageSelect]没有查询到数据");
            return Result.success(null);
        }

        // 查询主表获取id和固定信息
        Integer pageSize = (req.getPageSize() == null) ? 10 : req.getPageSize();
        Integer pageIndex = (req.getPageIndex() == null || req.getPageIndex() < 1) ? 1 : req.getPageIndex();
        int startIndex = (pageIndex - 1) * pageSize;
        List<MshopPageInstanceDo> list = mshopPageInstanceDao.selectPageList(pageSize, startIndex, pageQueryVO,draftPageCodeList);

        // 查询草稿表获取最新信息，时间大于等于主表时间的记录
        Map<String, MshopPageDraftDo> draftMap = list.stream()
                .map(MshopPageInstanceDo::getPageCode)
                .distinct()
                .map(pageCode -> {
                    return mshopPageDraftDao.getLastByPageCode(pageCode,null);
                }).filter(Objects::nonNull)
                .collect(Collectors.toMap(MshopPageDraftDo::getPageCode, v -> v, (v1, v2) -> v1));

        // 查询是否有未发布版本
        List<PageSelectResult> resultList = list.stream()
                .map(instanceDo -> draftMap.get(instanceDo.getPageCode()))
                .filter(Objects::nonNull)
                .map(draftDo -> this.buildPageResult(draftDo, list.stream()
                        .filter(instanceDo -> instanceDo.getPageCode().equals(draftDo.getPageCode()))
                        .findFirst()
                        .orElse(null)))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(PageSelectResult::getUpdateTime).reversed())
                .collect(Collectors.toList());
        Pagination<PageSelectResult> pageData = new Pagination<>(req, count, resultList);
        return Result.success(pageData);
    }

    @Override
    public Result<Pagination<PageSelectResult>> pageSelectV2(PageSearchRequest req) {
        //避免全表查询需要增加查询条件的校验
        log.info("pageSelectV2 {}, req:{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
        Pagination<PageSelectResult> pageData = new Pagination<>();
        if(StringUtils.isNotBlank(req.getName()) || StringUtils.isNotBlank(req.getTitle())){
            //有title和name需要查询草稿，走特殊处理
            List<String> searchPageCode;
            if(searchTitleNameV2Flag){
                searchPageCode = pageSelectByDraftV2(req);
                log.info("pageSelectByDraftV2 searchPageCode :{}",JSON.toJSONString(searchPageCode));
            }else{
                searchPageCode = pageSelectByDraft(req);
                log.info("pageSelectByDraft searchPageCode :{}",JSON.toJSONString(searchPageCode));
            }
            if(!CollectionUtils.isEmpty(searchPageCode)){
                if(CollectionUtils.isEmpty(req.getPageCodeList())){
                    req.setPageCodeList(searchPageCode);
                }else{
                    req.getPageCodeList().addAll(searchPageCode);
                }
            }else{
                //查询不到名称直接返回空
                log.info("pageSelectByDraftV2 return end req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
                return Result.success(pageData);
            }
        }


        try {
            PageHelper.startPage(req.getPageIndex(), req.getPageSize());
            // 获取查询对象
            PageQueryVO pageQueryVO = this.getPageQueryVO(req);
            List<MshopPageInstanceDo> list = Lists.newArrayList();
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(req.getOwnerIdList())
                    && StringUtils.isNotBlank(req.getOwnerType())){
                // 多个条件共存特殊处理
                log.info("pageSelectByDraftV2 selectListByOwnerIdList time start req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
                list = mshopPageInstanceDao.selectListByOwnerIdList(pageQueryVO);
                log.info("pageSelectByDraftV2 selectListByOwnerIdList time end req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
            }else{
                log.info("pageSelectByDraftV2 selectList time start req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
                list = mshopPageInstanceDao.selectList(pageQueryVO);
                log.info("pageSelectByDraftV2 selectList time end req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
            }

            PageInfo<MshopPageInstanceDo> doPageInfo = new PageInfo(list);

            // 查询草稿表获取最新信息，时间大于等于主表时间的记录
            Map<String, MshopPageDraftDo> draftMap = list.stream()
                    .map(MshopPageInstanceDo::getPageCode)
                    .distinct()
                    .map(pageCode -> {
                        return mshopPageDraftDao.getLastByPageCode(pageCode,null);
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toMap(MshopPageDraftDo::getPageCode, v -> v, (v1, v2) -> v1));

            // 查询是否有未发布版本
            List<MshopPageInstanceDo> finalList = list;
            List<PageSelectResult> resultList = list.stream()
                    .map(instanceDo -> draftMap.get(instanceDo.getPageCode()))
                    .filter(Objects::nonNull)
                    .map(draftDo -> this.buildPageResult(draftDo, finalList.stream()
                            .filter(instanceDo -> instanceDo.getPageCode().equals(draftDo.getPageCode()))
                            .findFirst()
                            .orElse(null)))
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(PageSelectResult::getUpdateTime).reversed())
                    .collect(Collectors.toList());

            pageData = new Pagination<>(req, doPageInfo.getTotal(), resultList);
        } catch (Exception ex) {
            log.error("pageSelectV2 error req:{}",JSON.toJSONString(req),ex);
        } finally {
            PageHelper.clearPage();
        }

        return Result.success(pageData);
    }

    private List<String> pageSelectByDraft(PageSearchRequest req){
        //查询全部数据
        PageQueryVO pageQueryVO = this.getPageQueryVO(req);
        List<MshopPageInstanceDo> list = Lists.newArrayList();
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(req.getOwnerIdList())
                && StringUtils.isNotBlank(req.getOwnerType())){
            // 多个条件共存特殊处理
            list = mshopPageInstanceDao.selectListByOwnerIdList(pageQueryVO);
        }else{
            list = mshopPageInstanceDao.selectList(pageQueryVO);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //查询草稿表的数据
        List<String> instancePageCodeList = list.stream().map(MshopPageInstanceDo::getPageCode).collect(Collectors.toList());
        if(StringUtils.isNotBlank(req.getName()) || StringUtils.isNotBlank(req.getTitle())){
            List<MshopPageDraftDo> draftDoList = mshopPageDraftDao.getByNameTitle(req.getName(), req.getTitle(),null, instancePageCodeList);
            if (!CollectionUtils.isEmpty(draftDoList)) {
                List<String> pageCodeList = draftDoList.stream().map(MshopPageDraftDo::getPageCode).collect(Collectors.toList());
                return pageCodeList;
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 不用in查询数据，内存匹配
     * @param req
     * @return
     */
    private List<String> pageSelectByDraftV2(PageSearchRequest req){
        //查询全部数据
        PageQueryVO pageQueryVO = this.getPageQueryVO(req);
        List<MshopPageInstanceDo> list = Lists.newArrayList();
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(req.getOwnerIdList())
                && StringUtils.isNotBlank(req.getOwnerType())){
            // 多个条件共存特殊处理
            log.info("pageSelectByDraftV2 selectListByOwnerIdList time start req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
            list = mshopPageInstanceDao.selectListByOwnerIdList(pageQueryVO);
            log.info("pageSelectByDraftV2 selectListByOwnerIdList time end req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
        }else{
            log.info("pageSelectByDraftV2  selectList time start req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
            list = mshopPageInstanceDao.selectList(pageQueryVO);
            log.info("pageSelectByDraftV2  selectList time end req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
        }
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //查询草稿表的数据
        List<String> instancePageCodeList = list.stream().map(MshopPageInstanceDo::getPageCode).collect(Collectors.toList());
        log.info("pageSelectByDraftV2 instancePageCodeList:{}",JSON.toJSONString(instancePageCodeList));
        if(StringUtils.isNotBlank(req.getName()) || StringUtils.isNotBlank(req.getTitle())){
            log.info("pageSelectByDraftV2  time start req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
            List<MshopPageDraftDo> draftDoList = mshopPageDraftDao.getByNameTitleJoin(req.getName(), req.getTitle());
            log.info("pageSelectByDraftV2  time end req:{},{}",""+System.currentTimeMillis(),JSON.toJSONString(req));
            if (!CollectionUtils.isEmpty(draftDoList)) {
//                if(Objects.nonNull(req.getMarketType())){
//                    //过滤会场类型为私密会场的数据
//                    draftDoList = draftDoList.stream().filter(draft ->
//                            draft.getMarketType().equals(req.getMarketType().toString())).collect(Collectors.toList());
//                }
                List<String> pageCodeList = draftDoList.stream().map(MshopPageDraftDo::getPageCode).collect(Collectors.toList());
                log.info("pageSelectByDraftV2  pageCodeList:{}",JSON.toJSONString(instancePageCodeList));
                pageCodeList = pageCodeList.stream().filter(pageCode -> instancePageCodeList.contains(pageCode)).collect(Collectors.toList());
                return pageCodeList;
            }
        }
        return Lists.newArrayList();
    }


    /**
     * 查询草稿表，获取pageCode
     * @param req
     * @return
     */
    private List<String> getDraftPageCodes(PageSearchRequest req) {
        List<MshopPageInstanceDo> instanceDoList = mshopPageInstanceDao.selectPageListByOwner(req.getOwnerId(), req.getOwnerType());
        if (CollectionUtils.isEmpty(instanceDoList)) {
            return null;
        }
        List<String> instancePageCodeList = instanceDoList.stream().map(MshopPageInstanceDo::getPageCode).collect(Collectors.toList());
        if(StringUtils.isNotBlank(req.getName()) || StringUtils.isNotBlank(req.getTitle())){
            List<MshopPageDraftDo> draftDoList = mshopPageDraftDao.getByNameTitle(req.getName(), req.getTitle(),null, instancePageCodeList);
            if (!CollectionUtils.isEmpty(draftDoList)) {
                List<String> pageCodeList = draftDoList.stream().map(MshopPageDraftDo::getPageCode).collect(Collectors.toList());
                return pageCodeList;
            }
        } else {
            return instancePageCodeList;
        }
        return null;
    }

    /**
     * 分页查询对象赋值
     *
     * @param req
     * @return
     */
    private PageQueryVO getPageQueryVO(PageSearchRequest req) {
        //赋值
        PageQueryVO pageQueryVO = new PageQueryVO();
        pageQueryVO.setPageCodeList(req.getPageCodeList());
        pageQueryVO.setTenantId(req.getTenantId());
        pageQueryVO.setOwnerType(req.getOwnerType());
        pageQueryVO.setOwnerId(req.getOwnerId());
        pageQueryVO.setVersion(req.getVersion());
        pageQueryVO.setChannel(req.getChannel());
        pageQueryVO.setStatus(req.getStatus());
        pageQueryVO.setCreateTimeEnd(req.getCreateTimeEnd());
        pageQueryVO.setCreateTimeStart(req.getCreateTimeStart());
        pageQueryVO.setUpdateBy(req.getUpdateBy());
        pageQueryVO.setCreateBy(req.getCreateBy());
        pageQueryVO.setUpdateTimeEnd(req.getUpdateTimeEnd());
        pageQueryVO.setUpdateTimeStart(req.getUpdateTimeStart());
        pageQueryVO.setType(req.getType());
        pageQueryVO.setTypeList(req.getTypeList());
        pageQueryVO.setOwnerIdList(req.getOwnerIdList());
        //如果是会场，默认查询普通会场
        pageQueryVO.setMarketType(req.getMarketType()==null?MarketTypeEnum.NORMAL.getCode():req.getMarketType().toString());
        return pageQueryVO;
    }


    private PageSelectResult buildPageResult(MshopPageDraftDo draftDo, MshopPageInstanceDo instanceDo) {
        if (Objects.isNull(draftDo) || Objects.isNull(instanceDo)) {
            return null;
        }
        PageSelectResult pageResult = new PageSelectResult();
        pageResult.setPageCode(instanceDo.getPageCode());
        pageResult.setName(draftDo.getName());
        pageResult.setStatus(instanceDo.getStatus());
        int publishFlag = Objects.equals(instanceDo.getVersion(), draftDo.getVersion()) ? 0 : 1;
        pageResult.setPublishFlag(publishFlag);
        pageResult.setTitle(draftDo.getTitle());
        pageResult.setBizType(draftDo.getBizType());
        pageResult.setVersion(draftDo.getVersion());
        pageResult.setSubTitle(draftDo.getSubTitle());
        pageResult.setCreateBy(instanceDo.getCreateBy());
        pageResult.setCreateTime(instanceDo.getCreateTime());
        pageResult.setUpdateBy(draftDo.getCreateBy());
        pageResult.setUpdateTime(draftDo.getUpdateTime());
        pageResult.setPageType(instanceDo.getType());
        pageResult.setDeleteFlag(instanceDo.getDeleteFlag());
        pageResult.setOwnerId(instanceDo.getOwnerId());
        pageResult.setOwnerType(instanceDo.getOwnerType());
        if(StringUtils.isNotBlank(draftDo.getShareConfig())){
            PageShareConfigBO configBO = JSONObject.parseObject(draftDo.getShareConfig(), PageShareConfigBO.class);
            if(Objects.nonNull(configBO)){
                pageResult.setForwardTitle(configBO.getForwardTitle());
                pageResult.setForwardSubTitle(configBO.getForwardSubTitle());
                pageResult.setShareSwitch(configBO.getShareSwitch());
                pageResult.setShareCardImg(configBO.getShareCardImg());
            }
        }
        //生效时间
        if(StringUtils.isNotBlank(draftDo.getTimeConfig())){
            pageResult.setTimeConfig(JSONObject.parseObject(draftDo.getTimeConfig(), TimeConfigBO.class));
        }

        if (pageResult.getStatus().equals(PageInstanceStatusEnum.FAIL.getCode()) && StringUtils.isNotEmpty(instanceDo.getVersion())) {
            String status = contentCheckService.queryContentCheckStatus(instanceDo.getPageCode(), instanceDo.getVersion());
            ProcessStatusRequest request = new ProcessStatusRequest();
            request.setPageCode(instanceDo.getPageCode());
            request.setVersion(instanceDo.getVersion());
            ProcessStatusResp processStatusResp = workbenchService.findProcessStatus(request);
            Integer workflowStatus = Objects.nonNull(processStatusResp) ? processStatusResp.getProcessStatus() : ProcessStatusEnum.AUDIT_SUCCESS.getCode();
            String contentFailMessage = PageConstant.PAGE_FAIL_CONTENT_MESSAGE;
            String workFailMessage = PageConstant.PAGE_FAIL_WORK_MESSAGE;

            String failureMessage = Stream.of(status.equals(ContentSuggestTypeEnum.BLOCK.getSuggesst()) ? contentFailMessage : null,
                    workflowStatus.equals(ProcessStatusEnum.AUDIT_FAIL.getCode()) ? workFailMessage : null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));
            pageResult.setFailMessage(failureMessage);
        }
        return pageResult;
    }

    /**
     * 批量查询页面信息
     * @param pageCodeList
     * @return
     */
    @Override
    public Map<String,PageBO> findDetailByCache(List<String> pageCodeList) {
        if(CollectionUtils.isEmpty(pageCodeList) || pageCodeList.size()>20){
            throw new BusinessException("最多查询20条记录");
        }

        Map<String,PageBO> result = Maps.newHashMap();
        for(String pageCode:pageCodeList){
            PageBO pageBO =  detailByCache(pageCode,null);
            if(Objects.nonNull(pageBO)){
                result.put(pageCode,pageBO);
            }
        }
        return result;
    }
}
