package com.mengxiang.mshop.cms.core.service.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.mengxiang.base.common.process.model.BusinessContext;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.base.common.process.processor.AbstractBusinessProcessor;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @author: zhangmoxun
 * @Date: 2023/3/8
 * @Description: 业务转换抽象层
 * @param <C>
 * @param <M>
 */
public abstract class AbstractBusinessConverter<C extends BusinessContext, M extends BusinessModel> extends
        AbstractBusinessProcessor<C, M> {

    /**
     * 业务执行前处理 校验器无逻辑
     *
     * @param context 业务上下文
     * @return 业务执行结果
     */
    @Override
    protected InnerResult<M> beforeProcess(C context) {
        return new InnerResult<M>(Boolean.TRUE, null);
    }

    /**
     * 获取执行器名称
     *
     * @return
     */
    @Override
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 业务执行后处理
     *
     * @param context 业务上下文
     * @return 业务执行结果
     */
    @Override
    protected InnerResult<M> doProcess(C context) {
        InnerResult<BusinessModel> innerResult = this.convert(context);
        if (innerResult.getSuccess() && !context.getNeedInterrupt()) {
            return new InnerResult<>(Boolean.TRUE, null);
        } else {
            return new InnerResult(innerResult.getErrorCode(), CmsProdConstant.APP_ID);
        }
    }

    /**
     * 转换器逻辑 遇到失败则抛出异常
     *
     * @param context 上下文
     * @return
     */
    protected abstract InnerResult<BusinessModel> convert(C context);

    public <T> MshopComponentInstanceDo getComponentInstance(String pageCode, String version, ComponentTypeEnum componentType , ComponentBO componentBO, Class<T> resp) {
        MshopComponentInstanceDo instanceDo = new MshopComponentInstanceDo();
        instanceDo.setComponentCode(componentBO.getComponentCode());
        instanceDo.setPageCode(pageCode);
        if (CollectionUtil.isNotEmpty(componentBO.getAuthorizationList())) {
            instanceDo.setUseRule(String.join(",", componentBO.getAuthorizationList()));
        }
        instanceDo.setVersion(version);
        instanceDo.setType(componentType.getCode());
        instanceDo.setOrderValue(componentBO.getOrder());
        Optional.ofNullable(componentBO.getTimeConfig())
                .map(timeConfig -> JSONObject.toJSONString(timeConfig))
                .ifPresent(instanceDo::setTimeConfig);
        Optional.ofNullable(componentBO.getDirectUserGroup())
                .map(directUserGroup -> JSONObject.toJSONString(directUserGroup))
                .ifPresent(instanceDo::setDirectUserGroup);
        T cast = resp.cast(componentBO);
        String metaConfig = getJsonStrConfig(cast);
        instanceDo.setMetaConfig(metaConfig);
        return instanceDo;
    }

    public <T> MshopComponentInstanceDetailDo getComponentInstanceDetail(MshopComponentInstanceDo componentInstanceDo, ComponentDetailBO componentDetail, Class<T> resp) {
        MshopComponentInstanceDetailDo instanceDetail = new MshopComponentInstanceDetailDo();
        instanceDetail.setComponentCode(componentInstanceDo.getComponentCode());
        instanceDetail.setPageCode(componentInstanceDo.getPageCode());
        instanceDetail.setVersion(componentInstanceDo.getVersion());
        instanceDetail.setComponentType(componentInstanceDo.getType());
        instanceDetail.setConfigDetailCode(componentDetail.getComponentDetailCode());
        instanceDetail.setOrderValue(componentDetail.getOrder());
        Optional.ofNullable(componentDetail.getTimeConfig())
                .map(timeConfig -> JSONObject.toJSONString(timeConfig))
                .ifPresent(instanceDetail::setTimeConfig);
        T cast = resp.cast(componentDetail);
        String configDetail = getJsonStrConfig(cast);
        instanceDetail.setConfigDetail(configDetail);
        return instanceDetail;
    }

    public <T> String getJsonStrConfig(T object) {
        String metaConfig = JSON.toJSONString(object, new PropertyFilter() {
            @Override
            public boolean apply(Object o, String name, Object value) {
                List<Field> allFields = new ArrayList<>();
                allFields.addAll(Arrays.asList(o.getClass().getSuperclass().getDeclaredFields()));
                allFields.addAll(Arrays.asList(o.getClass().getDeclaredFields()));
                return allFields.stream()
                        .filter(e -> e.getName().equals(name))
                        .filter(e -> e.isAnnotationPresent(IgnorePropertyJson.class))
                        .findFirst()
                        .map(e -> false)
                        .orElse(true);
            }
        });
        return metaConfig;
    }
}
