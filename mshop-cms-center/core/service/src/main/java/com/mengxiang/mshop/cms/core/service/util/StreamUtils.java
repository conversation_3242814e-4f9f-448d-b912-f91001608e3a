package com.mengxiang.mshop.cms.core.service.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.mengxiang.mshop.cms.core.model.annotate.IgnorePropertyJson;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @Date: 2023/5/25
 * @Description:
 */
public class StreamUtils {

    public static  <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object,Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    public static  <T> String getJsonStrConfig(T object) {
        String config = JSON.toJSONString(object, (PropertyFilter) (o, name, value) -> {
            List<Field> allFields = new ArrayList<>();
            allFields.addAll(Arrays.asList(o.getClass().getSuperclass().getDeclaredFields()));
            allFields.addAll(Arrays.asList(o.getClass().getDeclaredFields()));
            return allFields.stream()
                    .filter(e -> e.getName().equals(name))
                    .filter(e -> e.isAnnotationPresent(IgnorePropertyJson.class))
                    .findFirst()
                    .map(e -> false)
                    .orElse(true);
        });
        return config;
    }
}
