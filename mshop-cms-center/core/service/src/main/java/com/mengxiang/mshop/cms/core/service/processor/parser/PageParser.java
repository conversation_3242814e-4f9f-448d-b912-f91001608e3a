package com.mengxiang.mshop.cms.core.service.processor.parser;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageWorkbenchDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo;
import com.mengxiang.mshop.cms.core.model.enums.*;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ProcessStatusRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.ProcessStatusResp;
import com.mengxiang.mshop.cms.core.service.business.SequenceGeneratorService;
import com.mengxiang.mshop.cms.core.service.business.WorkbenchService;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.ProcessStatusEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessParser;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 页面解析
 * <AUTHOR>
 */
@Component
public class PageParser extends AbstractBusinessParser<PageContext, BusinessModel> {

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Autowired
    private WorkbenchService workbenchService;

    @Autowired
    private MshopPageWorkbenchDao mshopPageWorkbenchDao;

    @Value("${workBench.enable:true}")
    private Boolean enable;

    @Override
    protected InnerResult<BusinessModel> parse(PageContext context){
        SavePageRequest request = context.getRequest();
        MshopPageDraftDo featurePageDraft = BeanUtil.copyProperties(request,MshopPageDraftDo.class);
        MshopPageInstanceDo featurePageInstance = BeanUtil.copyProperties(request,MshopPageInstanceDo.class);
        String pageCode = request.getPageCode();
        if (StringUtils.isEmpty(pageCode)) {
            pageCode = sequenceGeneratorService.getSequence(request.getOwnerId());
        }
        if (Objects.nonNull(request.getTimeConfig())) {
            featurePageDraft.setTimeConfig(JSONObject.toJSONString(request.getTimeConfig()));
        }
        if (Objects.nonNull(request.getShareConfig())) {
            featurePageDraft.setShareConfig(JSONObject.toJSONString(request.getShareConfig()));
        }
        if (MarketTypeEnum.PRIVATE.equals(request.getMarketType()) && Objects.nonNull(request.getPrivateMarketConfig())){
            featurePageDraft.setPrivateMarketConfig(JSONObject.toJSONString(request.getPrivateMarketConfig()));
        }
        Integer status = PageInstanceStatusEnum.DRAFT.getCode();
        String featureVersion = String.valueOf(System.currentTimeMillis());
        boolean hasVideo = CollectionUtils.isNotEmpty(context.getVideoComponents());
        if(request.getOperateType().equals(PageOperateType.PUBLISH.getCode())) {
            boolean needWorkBench = workBenchType(context);
            if (hasVideo || needWorkBench) {
                status = PageInstanceStatusEnum.APPROVAL.getCode();
            } else {
                status = PageInstanceStatusEnum.PUBLISH.getCode();
                boolean isFeature = TimeConfigUtils.checkRuleTimeByStartTime(request.getTimeConfig());
                if (isFeature) {
                    status = PageInstanceStatusEnum.EXECUTORY.getCode();
                }
            }
            featurePageInstance.setVersion(featureVersion);
        }
        featurePageDraft.setStatus(status);
        featurePageInstance.setStatus(status);
        context.setFeaturePageInstance(featurePageInstance);
        featurePageDraft.setPageCode(pageCode);
        featurePageInstance.setPageCode(pageCode);
        featurePageDraft.setVersion(featureVersion);
        context.setFeatureVersion(featureVersion);
        context.setFeaturePageDraft(featurePageDraft);
        return new InnerResult<BusinessModel>(true, null);
    }

    /**
     * 审核流操作类型
     * @param context
     * @return
     */
    private boolean workBenchType (PageContext context) {
        if (!enable) {
            context.setWorkBenchType(0);
            return false;
        }
        SavePageRequest request = context.getRequest();
//        if (!request.getOwnerType().equals(PageOwnerType.MENGXIANG.getOwnerType())) {
        //判断当前页面是否需要走审批流
        if (StringUtils.isBlank(WorkbenchType.checkWorkBenchByPage(request))) {
            context.setWorkBenchType(0);
            return false;
        }
        MshopPageInstanceDo currentPage = context.getCurrentPage();
        if (StringUtils.isEmpty(request.getPageCode()) || (Objects.isNull(currentPage) || StringUtils.isEmpty(currentPage.getVersion()))) {
            context.setWorkBenchType(1);
            return true;
        }
        //校验是否有审核记录
        List<MshopPageWorkbenchDo> wblist = mshopPageWorkbenchDao.selectList(request.getPageCode(),null);
        if (CollectionUtils.isEmpty(wblist)) {
            context.setWorkBenchType(1);
            return true;
        }

        boolean auditSuccess = wblist.stream().anyMatch(w -> w.getProcessStatus().equals(ProcessStatusEnum.AUDIT_SUCCESS.getCode()));
        if(Objects.equals(request.getOwnerType(), PageOwnerType.MENGXIANG.getOwnerType()) && auditSuccess){
            //饷店会场3.0 并且 有审批通过的记录可以免审
            context.setWorkBenchType(0);
            return false;
        }
        MshopPageWorkbenchDo wbdo = wblist.get(0);
        if (wbdo.getProcessStatus().equals(ProcessStatusEnum.AUDIT_FAIL.getCode())) {
            context.setWorkBenchType(2);
            context.setBusinessKey(wbdo.getBusinessKey());
            return true;
        }
        if(Objects.equals(request.getOwnerType(), PageOwnerType.SUPPLIER.getOwnerType())
                && Objects.equals(request.getType(), PageType.MARKET_PAGE.getType()) ){
            //商家的会场3.0需要每次都发起审批
            context.setWorkBenchType(1);
            return true;
        }

        context.setWorkBenchType(0);
        return true;
    }

}
