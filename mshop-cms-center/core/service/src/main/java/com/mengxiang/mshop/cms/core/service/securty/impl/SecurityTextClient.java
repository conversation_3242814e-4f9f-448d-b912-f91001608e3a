package com.mengxiang.mshop.cms.core.service.securty.impl;


import com.aikucun.common2.base.Result;
import com.aikucun.security.ugc.api.dto.request.TextScanReq2DTO;
import com.aikucun.security.ugc.api.dto.response.ScanResultSuggestion2Resp;
import com.aikucun.security.ugc.api.dto.response.TextScanApiResult2Resp;
import com.aikucun.security.ugc.api.enums.TextSceneEnum;
import com.aikucun.security.ugc.api.feign.SecurityTextScanApiClient;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.core.model.enums.ContentSuggestTypeEnum;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文本校验
 */
@Component
@Slf4j
public class SecurityTextClient {
    
    @Value("${risk.check.application.name:mshop-cms-center}")
    private String appId;
    
    @Value("${risk.check.app-secret:65527753358b41e28243a05d5a6f75b7}")
    private String appSecret;
    
    @Value("${risk.check.application.name:mshop-cms-center}")
    private String bizType;
    
    @Resource
    private SecurityTextScanApiClient securityTextScanApiClient;
    
    public List<ContentCheckResponse> riskTextCheck(List<String> contents, String userId) {
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }
        TextScanReq2DTO textScanReqDTO = buildTextScanReq(userId, contents);
        
        List<ContentCheckResponse> resps = contents.stream().map(e -> {
            ContentCheckResponse responseParam = new ContentCheckResponse();
            responseParam.setContent(e);
            responseParam.setQualifiedFlag(false);
            return responseParam;
        }).collect(Collectors.toList());
        
        try {
            Result<List<TextScanApiResult2Resp>> result = securityTextScanApiClient.contentScanSync2(textScanReqDTO);
            if (null == result || !result.getSuccess()) {
                log.error("[[riskTextCheck]] 调用文本内容安全校验出现异常 contents:{}", JSON.toJSONString(result));
                throw new BusinessException("调用风控返回失败");
            }
            List<TextScanApiResult2Resp> checkResult = result.getData();
            if (CollectionUtils.isEmpty(result.getData())) {
                return resps;
            }
            //查询被风控
            Map<String, TextScanApiResult2Resp> contentMap = checkResult.stream()
                    .collect(Collectors.toMap(TextScanApiResult2Resp::getContent, v -> v, (v1, v2) -> v1));
            
            for (ContentCheckResponse resp : resps) {
                TextScanApiResult2Resp textResult = contentMap.get(resp.getContent());
                if (null == textResult) {
                    resp.setCheckDesc(Lists.newArrayList("没有返回检测结果"));
                    continue;
                }
                resp.setDateId(textResult.getDataId());
                if (ContentSuggestTypeEnum.PASS.getSuggesst().equals(textResult.getSuggestion()) || ContentSuggestTypeEnum.REVIEW.equals(textResult.getSuggestion())) {
                    resp.setQualifiedFlag(true);
                    continue;
                }
                //提示词
                resp.setSuggestionTip(textResult.getSuggestionTip());
                if (CollectionUtils.isEmpty(textResult.getResults())) {
                    continue;
                }
                List<String> suggest = new ArrayList<>();
                for (ScanResultSuggestion2Resp suggestTion : textResult.getResults()) {
                    if (TextSceneEnum.ANTISPAM.getCode().equals(suggestTion.getScene())) {
                        suggest.add(TextSceneEnum.ANTISPAM.getDesc());
                    }
                }
                resp.setCheckDesc(suggest);
            }
            return resps;
        } catch (Exception e) {
            log.error("[[riskTextCheck]] 调用文本内容安全校验出现异常 req:{}", contents, e);
        }
        return null;
    }
    
    private TextScanReq2DTO buildTextScanReq(String userId, List<String> contents) {
        TextScanReq2DTO textScanReqDTO = new TextScanReq2DTO();
        textScanReqDTO.setContents(contents);
        textScanReqDTO.setUserId(userId);
        textScanReqDTO.setAppId(appId);
        textScanReqDTO.setAppSecret(appSecret);
        textScanReqDTO.setBizType(bizType);
        return textScanReqDTO;
    }
}
