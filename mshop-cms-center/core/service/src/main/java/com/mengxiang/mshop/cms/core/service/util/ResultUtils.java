package com.mengxiang.mshop.cms.core.service.util;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.base.common.process.error.ErrorCode;
import com.mengxiang.base.common.process.model.ServiceResult;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 */
public class ResultUtils {

    public static <T> Result<T> convertToResult(ServiceResult result, T t) {
        if (result.getSuccess()) {
            return Result.success(t);
        } else {
            ErrorCode errorCode = result.getErrorCode();
            return Result.error(errorCode.getErrorCode(), errorCode.getDisplayMsg());
        }
    }
}
