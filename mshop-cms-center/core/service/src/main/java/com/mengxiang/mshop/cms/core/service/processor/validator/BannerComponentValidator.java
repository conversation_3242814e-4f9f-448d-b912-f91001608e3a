package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.TargetType;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * banner 组件校验
 *
 * <AUTHOR>
 */
@Component
public class BannerComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;


    @Value("${banner.configDetails.max:10}")
    private Integer configDetailsMax;


    @Override
    protected InnerResult<BusinessModel> validator(PageContext context) {
        //解析组件
        if (CollectionUtils.isEmpty(context.getBannerComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        List<BannerComponentBO> banners = context.getBannerComponents();
        for (BannerComponentBO banner : banners) {
            paramVerify(banner);
        }
        return new InnerResult<BusinessModel>(true, null);
    }
    
    
    /**
     * 参数校验
     */
    private void paramVerify(BannerComponentBO banner) {
        if (CollectionUtils.isEmpty(banner.getCarouselConfigDetails())
                || banner.getCarouselConfigDetails().size() > configDetailsMax) {
            Preconditions.checkArgument(false, "banner组件 轮播页数量 不能大于"+configDetailsMax);
        }
        //轮播页内容校验
        List<BannerComponentConfigDetailBO> carouselConfigDetails = banner.getCarouselConfigDetails();
        if (CollectionUtils.isNotEmpty(carouselConfigDetails)) {
            for (BannerComponentConfigDetailBO detail : carouselConfigDetails) {
                Preconditions.checkArgument(Objects.nonNull(TargetType.getByType(detail.getTargetType())), "banner组件 目标类型 不能为空");
                Preconditions.checkArgument(StringUtils.isNotEmpty(detail.getTargetId()), "banner组件 目标参数 不能为空");
                Preconditions.checkArgument(Objects.nonNull(detail.getOrder()), "banner组件 排序 不能为空");
            }
            List<String> pageCodes = carouselConfigDetails.stream().filter(c -> TargetType.SHOPMICRO.getType().equals(c.getTargetType())).map(x -> x.getTargetId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pageCodes)) {
                pageCodes.stream().forEach(pageCode ->{
                    MshopPageInstanceDo page = mshopPageInstanceDao.getByPageCode(pageCode, null);
                    if (Objects.isNull(page)) {
                        throw new BusinessException("图片组件 目标页面不存在");
                    }
                    MshopPageDraftDo mshopPageDraftDo;
                    if (StringUtils.isEmpty(page.getVersion())) {
                        mshopPageDraftDo = mshopPageDraftDao.getLastByPageCode(page.getPageCode(),null);
                    } else {
                        mshopPageDraftDo = mshopPageDraftDao.getByPageCode(page.getPageCode(),page.getVersion());
                    }
                    if (Objects.nonNull(mshopPageDraftDo) && (StringUtils.isEmpty(page.getVersion()) || !page.getStatus().equals(PageInstanceStatusEnum.PUBLISH.getCode()))) {
                        throw new BusinessException("banner组件 目标页面【"+mshopPageDraftDo.getName()+"】 必须是已发布已生效的页面");
                    }
                });
            }
        }
    }
}
