package com.mengxiang.mshop.cms.core.service.securty.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.aikucun.security.ugc.api.dto.response.VideoScanApiResult2Resp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Preconditions;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageContentCheckDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo;
import com.mengxiang.mshop.cms.core.model.enums.ContentSuggestTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.ContextTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.request.content.PageContentRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.model.result.content.ContentQueryResponse;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 内容检测 stable环境： appId：mshop-cms-center appSecret：65527753358b41e28243a05d5a6f75b7
 * <p>
 * release环境： appId：mshop-cms-center appSecret：a8554925e3de47d2916633fd2cbea39b
 * <p>
 * 生产环境： appId：mshop-cms-center appSecret：0aab8cf8b96c444785b0e933f8aef190
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContentCheckServiceImpl implements ContentCheckService {
    
    @Autowired
    private SecurityTextClient securityTextClient;
    
    @Autowired
    private SecurityImageClient securityImageClient;
    
    @Autowired
    private SecurityVideoClient securityVideoClient;
    
    @Autowired
    private MshopPageContentCheckDao pageContentCheckDao;

    @Override
    public List<ContentCheckResponse> contextCheck(ContentCheckRequest req) {
        Preconditions.checkArgument(!CollectionUtils.isEmpty(req.getContents()), "校验内容 不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getType()), "检测类型 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getOperateBy()), "操作人 不能为空");
        List<ContentCheckResponse> responses = null;
        if (ContextTypeEnum.TEXT.val.equals(req.getType())) {
            //文本
            responses = securityTextClient.riskTextCheck(req.getContents(), req.getOperateBy());
        } else if (ContextTypeEnum.IMG.val.equals(req.getType())) {
            //图片
            responses = securityImageClient.riskImageCheck(req.getContents(), req.getOperateBy());
        } else if (ContextTypeEnum.VIDEO.val.equals(req.getType())) {
            //视频
            responses = securityVideoClient.riskVideoCheck(req.getContents(), req.getOperateBy());
        }
        return responses;
    }
    
    @Override
    public List<ContentQueryResponse> videoContextQuery(List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return Lists.newArrayList();
        }
        List<ContentQueryResponse> contentQueryResponses = securityVideoClient.riskVideoQuery(dataIds);
        return contentQueryResponses;
    }
    
    @Override
    public List<ContentCheckResponse> pageContentCheck(String pageCode, String version,
            List<PageContentRequest> componentUrls) {
        Preconditions.checkArgument(!StringUtils.isEmpty(pageCode), "页面code 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(version), "版本 不能为空");
        Preconditions.checkArgument(!CollectionUtils.isEmpty(componentUrls), "校验url 不能为空");
        
        //校验的url
        List<String> urls = componentUrls.stream().map(PageContentRequest::getUrl).filter(e -> !StringUtils.isEmpty(e))
                .distinct().collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(urls)) {
            Preconditions.checkArgument(CollectionUtils.isEmpty(urls), "校验url 不能为空");
        }
        //校验
        List<ContentCheckResponse> videoChecks = securityVideoClient.riskVideoCheck(urls, "sys");
        if (CollectionUtils.isEmpty(videoChecks)) {
            //校验失败
            log.warn("[[pageVideoCheck]] 视频校验 返回为空 或 校验个数不一致");
            throw new BusinessException("调用风控校验视频异常,视频校验个数不一致");
        }
        //存在失败直接返回。
        boolean existFail = videoChecks.stream().anyMatch(e -> !e.isQualifiedFlag());
        if (existFail) {
            log.warn("[[pageVideoCheck]] 视频check 存在失败 pageCode:{},version:{},componentUrls:{}", pageCode, version,
                    JSON.toJSONString(videoChecks));
            return videoChecks;
        }
        
        Map<String, ContentCheckResponse> contentCheck = videoChecks.stream()
                .collect(Collectors.toMap(ContentCheckResponse::getContent, v -> v, (v1, v2) -> v1));
        
        List<MshopPageContentCheckDo> resp = new ArrayList<>(urls.size());
        for (PageContentRequest componentUrl : componentUrls) {
            String componentCode = componentUrl.getComponentCode();
            String componentDetailCode = componentUrl.getComponentDetailCode();
            String url = componentUrl.getUrl();
            ContentCheckResponse checkResponse = contentCheck.get(url);
            if (checkResponse == null) {
                log.warn(
                        "[[pageVideoCheck]] 未匹配到视频check pageCode:{},version:{},componentCode:{},componentDetailCode:{},url:{}",
                        pageCode, version, componentCode, componentDetailCode, url);
                throw new BusinessException("调用风控校验视频异常,没有匹配到校验结果 url:{}", url);
            }
            MshopPageContentCheckDo checkDo = new MshopPageContentCheckDo();
            checkDo.setPageCode(pageCode);
            checkDo.setVersion(version);
            checkDo.setContent(url);
            checkDo.setComponentCode(componentCode);
            checkDo.setComponentConfigCode(componentDetailCode);
            checkDo.setCheckStatus(ContentSuggestTypeEnum.PROCESSING.getSuggesst());
            checkDo.setCheckResult("");
            checkDo.setDataId(checkResponse.getDateId());
            checkDo.setContentType(ContextTypeEnum.VIDEO.val);
            resp.add(checkDo);
        }
        //入库
        pageContentCheckDao.insertBatch(resp);
        return videoChecks;
    }

    @Override
    public String queryContentCheckStatus (String pageCode, String version) {
        //查询页面内容
        List<MshopPageContentCheckDo> contentCheckDos = pageContentCheckDao.queryByPage(pageCode, version);
        if (CollectionUtils.isEmpty(contentCheckDos)) {
            //未查询到视频检测内容 直接通过
            return ContentSuggestTypeEnum.PASS.getSuggesst();
        }
        //查询是否还有检测中.
        List<MshopPageContentCheckDo> checkProcessing = contentCheckDos.stream()
                .filter(e -> ContentSuggestTypeEnum.PROCESSING.getSuggesst().equals(e.getCheckStatus()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(checkProcessing)) {
            return ContentSuggestTypeEnum.PROCESSING.getSuggesst();
        }
        //存在阻塞或者其他位置原因
        List<MshopPageContentCheckDo> blockOrUnknown = contentCheckDos.stream()
                .filter(e -> ContentSuggestTypeEnum.BLOCK.getSuggesst().equals(e.getCheckStatus())
                        || ContentSuggestTypeEnum.UN_KNOWN.getSuggesst().equals(e.getCheckStatus()))
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(blockOrUnknown)){
            //页面未通过
            return ContentSuggestTypeEnum.BLOCK.getSuggesst();
        } else {
            //页面通过、发布页面
            return ContentSuggestTypeEnum.PASS.getSuggesst();
        }
    }
    
    @Override
    public Boolean contentCheckResult(List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return true;
        }
        //查询全部
        List<ContentQueryResponse> contentQueryResponses = securityVideoClient.riskVideoQuery(dataIds);
        if (CollectionUtils.isEmpty(contentQueryResponses)) {
            log.info("[[contentCheckResult]] 未查询到 结果 data:{}", dataIds);
            return true;
        }
        //筛选未检测中的
        List<ContentQueryResponse> result = contentQueryResponses.stream()
                .filter(e -> ContentSuggestTypeEnum.PROCESSING != e.getSuggestion()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            return true;
        }
        List<MshopPageContentCheckDo> resps = new ArrayList<>(contentQueryResponses.size());
        for (ContentQueryResponse queryResp : result) {
            MshopPageContentCheckDo resp = new MshopPageContentCheckDo();
            resp.setDataId(queryResp.getDataId());
            //检测结果
            resp.setCheckStatus(queryResp.getSuggestion().getSuggesst());
            if(!CollectionUtils.isEmpty(queryResp.getCheckDesc())){
                String collect = queryResp.getCheckDesc().stream().collect(Collectors.joining(","));
                resp.setCheckResult(collect);
            }
            resps.add(resp);
        }
        //更新检测结果
        for (MshopPageContentCheckDo mshopPageContentCheckDo :resps) {
            pageContentCheckDao.updateByDataId(mshopPageContentCheckDo);
        }
        return true;
    }

    @Override
    public Boolean callBack(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return false;
        }
        //解析
        com.aikucun.common2.base.Result result = JSON.parseObject(msg, com.aikucun.common2.base.Result.class);
        String respStr = result.getData().toString();
        List<VideoScanApiResult2Resp> videoScanApiResult2Resps = JSONArray
                .parseArray(respStr, VideoScanApiResult2Resp.class);
        if (CollectionUtils.isEmpty(videoScanApiResult2Resps)) {
            log.info("[[callBack]] 视频审核接口回调 为空 msg:{}", msg);
            return false;
        }
        //处理
        List<MshopPageContentCheckDo> batchUpdate = new ArrayList<>();
        for (VideoScanApiResult2Resp resp : videoScanApiResult2Resps) {
            String dataId = resp.getDataId();
            String code = resp.getCode();
            //判断是否是处理中
            if ("600100".equals(code)) {
                continue;
            }
            MshopPageContentCheckDo mshopPageContentCheckDo = new MshopPageContentCheckDo();
            mshopPageContentCheckDo.setDataId(dataId);
            if ("200".equals(code)) {
                //处理成功
                ContentSuggestTypeEnum suggesst = ContentSuggestTypeEnum.getEnumBySuggesst(resp.getSuggestion());
                if (null == suggesst) {
                    mshopPageContentCheckDo.setCheckStatus(ContentSuggestTypeEnum.PASS.getSuggesst());
                } else {
                    mshopPageContentCheckDo.setCheckStatus(suggesst.getSuggesst());
                    mshopPageContentCheckDo.setCheckResult(resp.getMessage());
                }
            } else {
                //处理失败。
                mshopPageContentCheckDo.setCheckStatus(ContentSuggestTypeEnum.UN_KNOWN.getSuggesst());
                mshopPageContentCheckDo.setCheckResult(resp.getMessage());
            }
            batchUpdate.add(mshopPageContentCheckDo);
        }
        //更新
        if (!CollectionUtils.isEmpty(batchUpdate)) {
            for (MshopPageContentCheckDo mshopPageContentCheckDo :batchUpdate) {
                pageContentCheckDao.updateByDataId(mshopPageContentCheckDo);
            }
        }
        return true;
    }
    
    public static void main(String[] args) {
        //创建一个Table
        Table<String, String, Integer> tab = HashBasedTable.create();
        tab.put("1", "2", 22);
        tab.put("3", "4", 22);
        tab.put("5", "6", 98);
        tab.put("7", "8", 100);
    }
}


