package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/5/10
 * @Description: 支持用户分群
 */
public interface PageQueryV2Service {

    PageBO detailByPageType(PageInfoRequest request);

    PageBO detailByCache (PageInfoRequest request);

    List<PageRuleInfoResult> getRuleBatch(List<Long> ruleIds);

}
