package com.mengxiang.mshop.cms.core.service.business.impl;

import com.akucun.cms.aggregation.stub.feign.req.ResourceModuleReq;
import com.akucun.cms.model.dto.resource.AkcImgComponentRes;
import com.akucun.cms.model.vo.blocDay.BlocDayChannelRes;
import com.akucun.cms.model.vo.resourceModule.ResourceModuleBaseInfoVO;
import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.common.service.integration.feign.AkcCmsClient;
import com.mengxiang.mshop.cms.core.service.business.PageRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PageRelationServiceImpl implements PageRelationService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private AkcCmsClient akcCmsClient;

    /**
     * 更新页面和品牌的关联关系
     *
     */
    public void updatePageRelationBrandId(){
        List<String> pageCodeList = Lists.newArrayList();
        //查询顶部tab关联的会场3.0PageCode
        List<String> tabPageCode = findTabPageCode();
        if(CollectionUtils.isNotEmpty(tabPageCode)){
            pageCodeList.addAll(tabPageCode);
        }
        //查询图片组件关联的会场3.0PageCode
        List<String> imgListPageCode =findImgListPageCode();
        if(CollectionUtils.isNotEmpty(imgListPageCode)){
            pageCodeList.addAll(imgListPageCode);
        }
        //查询pageCode关联的品牌id

        //保存关联关系
    }


    /**
     * 检查租户能否显示会场
     * @param tenantId
     * @param pageCodeList
     */
    public void checkPageBrandByTenantId(String tenantId, List<String> pageCodeList){
        //查询租户关联会场的缓存
        //查询租户是否有黑名单
        //匹配黑名单和会场关联的品牌
        //命中返回

    }

    /**
     * 查询首页tab关联的会场3.0 PageCode
     * @return
     */
    private List<String> findTabPageCode(){
        List<String> pageCodeList = Lists.newArrayList();
        List<BlocDayChannelRes> list = akcCmsClient.queryChannelOnline(1);
        list.addAll(akcCmsClient.queryChannelOnline(2));
        if(CollectionUtils.isNotEmpty(list)){
            for(BlocDayChannelRes res:list){
                if(res.getRelationConferenceFlag()==5 && StringUtils.isNotBlank(res.getConferenceIdStr())){
                    pageCodeList.add(res.getConferenceIdStr());
                }
            }
        }
        return pageCodeList;
    }

    /**
     * 查询首页图片组件关联的会场3.0 PageCode
     * @return
     */
    private List<String> findImgListPageCode(){
        List<String> pageCodeList = Lists.newArrayList();
        ResourceModuleReq resourceModuleReq = new ResourceModuleReq();
        resourceModuleReq.setCategoryId("0");
        resourceModuleReq.setChannel(1);
        ResourceModuleBaseInfoVO resourceBaseInfo = akcCmsClient.queryResourceBaseInfo(resourceModuleReq);
        if(Objects.nonNull(resourceBaseInfo)){
            pageCodeList.addAll(findPageCodeByImgList(resourceBaseInfo.getImgComponentList()));
        }
        resourceModuleReq.setChannel(2);
        resourceBaseInfo = akcCmsClient.queryResourceBaseInfoByXd(resourceModuleReq);
        if(Objects.nonNull(resourceBaseInfo)){
            pageCodeList.addAll(findPageCodeByImgList(resourceBaseInfo.getImgComponentList()));
        }
        resourceModuleReq.setChannel(3);
        resourceBaseInfo = akcCmsClient.queryResourceBaseInfoByXd(resourceModuleReq);
        if(Objects.nonNull(resourceBaseInfo)){
            pageCodeList.addAll(findPageCodeByImgList(resourceBaseInfo.getImgComponentList()));
        }
        return pageCodeList;
    }


    private List<String> findPageCodeByImgList(List<AkcImgComponentRes> imgList){
        List<String> pageCodeList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(imgList)){
            return pageCodeList;
        }
        for(AkcImgComponentRes res:imgList){
            for(AkcImgComponentRes.AkcImgDTO akcImgDTO:res.getList()){
                if(13 == akcImgDTO.getSkipType()){
                    pageCodeList.add(akcImgDTO.getId());
                }
            }
        }
        return pageCodeList;
    }
}
