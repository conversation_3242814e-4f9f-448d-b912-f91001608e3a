package com.mengxiang.mshop.cms.core.service.processor.action;

import cn.hutool.core.collection.CollectionUtil;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDetailDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 组件详情
 * <AUTHOR>
 */
@Service
public class ComponetDetailCommitAction extends AbstractBusinessAction<PageContext, BusinessModel> {

    @Autowired
    private MshopComponentInstanceDetailDao componentDetailDao;


    @Override
    protected void beforeAction(PageContext context) {
   
    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        List<MshopComponentInstanceDetailDo> componentDetailsDos = context.getComponentInstanceDetails();
        if(CollectionUtil.isNotEmpty(componentDetailsDos)){
            int res = componentDetailDao.insertBatch(componentDetailsDos);
            if (res <= 0) {
                context.setNeedInterrupt(true);
                return new InnerResult<BusinessModel>(CmsErrorCodeEnum.SAVE_PAGE_ERROR.getErrorCode(), "mshop-cms-center");
            }
        }
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
