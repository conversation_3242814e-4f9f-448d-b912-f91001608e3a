package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.MarketTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * banner 页面校验
 * <AUTHOR>
 */
@Component
public class PageValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        SavePageRequest request = context.getRequest();
        Preconditions.checkArgument(Objects.nonNull(request.getTimeConfig()), "页面生效方式 不能为空");
        if(StringUtils.isNotEmpty(request.getPageCode())) {
            MshopPageInstanceDo mshopPageInstanceDo = mshopPageInstanceDao.getByPageCode(request.getPageCode(),null);
            if (Objects.isNull(mshopPageInstanceDo)) {
                throw new BusinessException(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
            }
            if (mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.APPROVAL.getCode()) || mshopPageInstanceDo.getStatus().equals(PageInstanceStatusEnum.DISABLED.getCode())) {
                throw new BusinessException(CmsErrorCodeEnum.PAGE_STATUS_ERROR.getErrorMsg());
            }
            MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getByPageCode(request.getPageCode(),null);
            if (Objects.isNull(mshopPageDraftDo)) {
                throw new BusinessException(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
            }
            context.setCurrentPageDraft(mshopPageDraftDo);
            context.setCurrentPage(mshopPageInstanceDo);
        }
        //添加判断，如果是私密会场，开始结束时间不能为空
        MarketTypeEnum marketType = request.getMarketType();
        PrivateMarketConfigBO privateMarketConfig = request.getPrivateMarketConfig();
        if (Objects.nonNull(marketType) && MarketTypeEnum.PRIVATE.equals(marketType)) {
            if (Objects.nonNull(privateMarketConfig)){
                String startTime = privateMarketConfig.getStartTime();
                String endTime = privateMarketConfig.getEndTime();
                if (Objects.isNull(privateMarketConfig.getChannelType())){
                    throw new BusinessException(CmsErrorCodeEnum.PAGE_PRIVATE_CONFIG_CHANNEL_EMPTY_ERROR.getErrorMsg());
                }
                if (CollectionUtils.isEmpty(privateMarketConfig.getUserGroups())){
                    throw new BusinessException(CmsErrorCodeEnum.PAGE_PRIVATE_CONFIG_GROUPS_EMPTY_ERROR.getErrorMsg());
                }

                //开始时间和结束时间不能为空
                if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
                    throw new BusinessException(CmsErrorCodeEnum.PAGE_TIME_EMPTY_ERROR.getErrorMsg());
                }
                //判断这两个时间的间隔不能超过半年
                if (!validateDateInterval(startTime, endTime, 6)) {
                    throw new BusinessException(CmsErrorCodeEnum.PAGE_TIME_INTERVAL_TOO_LONG_ERROR.getErrorMsg());
                }
            }else {
                throw new BusinessException(CmsErrorCodeEnum.PAGE_PRIVATE_CONFIG_EMPTY_ERROR.getErrorMsg());
            }
        }
        boolean isValid = TimeConfigUtils.checkRuleTimeByEndTime(request.getTimeConfig());
        if (!isValid) {
            throw new BusinessException(CmsErrorCodeEnum.PAGE_ENDTIME_ERROR.getErrorMsg());
        }
        if (StringUtils.isEmpty(request.getTemplateCode())) {
            request.setTemplateCode(CmsProdConstant.DEFAULT_TEMPLATE_CODE);
        }
        return new InnerResult<BusinessModel>(true, null);
    }

    /**
     * 校验时间间隔是否超过interval月
     * @param startTimeStr
     * @param endTimeStr
     * @param interval 月数
     * @return
     */
    private boolean validateDateInterval(String startTimeStr, String endTimeStr, int interval) {
        // 解析日期字符串为 LocalDate 对象
        LocalDateTime startDate = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endDate = LocalDateTime.parse(endTimeStr, formatter);
        // 计算两个日期之间的月份数
        long monthsBetween = ChronoUnit.MONTHS.between(startDate, endDate);
        // 判断间隔是否超过指定月数
        return monthsBetween <= interval;
    }
}
