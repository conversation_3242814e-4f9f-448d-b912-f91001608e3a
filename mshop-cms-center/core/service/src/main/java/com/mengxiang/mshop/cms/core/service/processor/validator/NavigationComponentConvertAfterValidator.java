package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.DirectUserGroupBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.NavigationStyleTypeEnum;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 导航组件校验
 *
 * <AUTHOR>
 */
@Component
public class NavigationComponentConvertAfterValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {
    
    @Override
    protected InnerResult<BusinessModel> validator(PageContext context) {
        //解析组件
        if (CollectionUtils.isEmpty(context.getNavigationComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        Map<String, MshopComponentInstanceDo> archorMap = context.getArchorCodeMap();
        List<MshopComponentInstanceDo> components = context.getComponentInstances();
        List<NavigationComponentBO> navigations = context.getNavigationComponents();
        for (NavigationComponentBO navigation : navigations) {
            if (navigation.getStyleType().equals(NavigationStyleTypeEnum.DEFAULT.getCode()) || CollectionUtils.isEmpty(navigation.getComponentCodes())) {
               continue;
            }
            MshopComponentInstanceDo navigationComponent = components.stream()
                    .filter(x -> x.getComponentCode().equals(navigation.getComponentCode()))
                    .findFirst()
                    .orElse(null);
            navigation.getComponentCodes().stream().forEach(code -> {
                if (Objects.isNull(navigationComponent) || StringUtils.isEmpty(navigationComponent.getDirectUserGroup())) {
                    return;
                }
                MshopComponentInstanceDo archorComponent = archorMap.get(code.getComponentCode());
                if (Objects.isNull(archorComponent) || StringUtils.isEmpty(archorComponent.getDirectUserGroup())) {
                    return;
                }
                DirectUserGroupBO navigationDirectUserGroup = JSONObject.parseObject(navigationComponent.getDirectUserGroup(), DirectUserGroupBO.class);
                Preconditions.checkArgument(Objects.nonNull(archorComponent) && StringUtils.isNotEmpty(archorComponent.getDirectUserGroup()), "导航组件配置的人群和组件内不一致！");
                DirectUserGroupBO archorDirectUserGroup = JSONObject.parseObject(archorComponent.getDirectUserGroup(), DirectUserGroupBO.class);
                Preconditions.checkArgument(navigationDirectUserGroup.equals(archorDirectUserGroup), "导航组件配置的人群和组件内不一致！");
            });
        }
        return new InnerResult<BusinessModel>(true, null);
    }
}
