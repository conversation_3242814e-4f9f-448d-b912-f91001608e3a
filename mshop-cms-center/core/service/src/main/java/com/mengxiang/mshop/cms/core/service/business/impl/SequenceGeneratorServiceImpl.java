package com.mengxiang.mshop.cms.core.service.business.impl;

import com.mengxiang.base.common.sequence.spring.SequenceGenerator;
import com.mengxiang.mshop.cms.core.service.business.SequenceGeneratorService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Component
public class SequenceGeneratorServiceImpl implements SequenceGeneratorService {
    private static final int STUFF_LENGTH = 4;

    private static final String PADDING_FORMAT = "%04d";

    @Resource
    private SequenceGenerator sequenceGenerator;

    /**
     * 唯一标识生成
     * http://wiki.aikucun.xyz/pages/viewpage.action?pageId=10552337
     * @return
     */
    @Override
    public String getSequence(String stuff) {
        if (Objects.isNull(stuff)) {
            throw new IllegalArgumentException("业务单号生成规则匹配失败");
        }
        String stuffStr = stuff.substring(stuff.length() - STUFF_LENGTH);
        String sequence = sequenceGenerator.getSequence();
        return Strings.left(sequence, 19) + stuffStr;
    }
}
