package com.mengxiang.mshop.cms.core.service.business;


import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.core.model.request.ComponetAggregationRequest;
import com.mengxiang.mshop.cms.core.model.request.ComponetDetailCreateRequest;

import java.util.List;

/**
 * 组件详情服务
 */
public interface ComponentDetailService {
    
    String create(ComponetDetailCreateRequest req);

    /**
     * 查询页面所有的详情
     */
    List<MshopComponentInstanceDetailDo> queryByPageCode(String pageCode, String version);
}
