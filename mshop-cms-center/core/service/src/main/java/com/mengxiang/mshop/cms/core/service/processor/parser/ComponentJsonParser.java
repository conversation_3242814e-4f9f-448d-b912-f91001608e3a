package com.mengxiang.mshop.cms.core.service.processor.parser;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.CouponComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.PointsCouponComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.cube.CubeComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImageComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.material.MaterialComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.AllProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.ProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.profit.ProfitAdditionalComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.promotion.UserIncentiveComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.seckill.SeckillComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.text.SeparatorComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.text.TextComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.toplist.TopListComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessParser;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 组件解析
 * <AUTHOR>
 */
@Component
@Slf4j
public class ComponentJsonParser extends AbstractBusinessParser<PageContext, BusinessModel> {

    @Override
    protected InnerResult<BusinessModel> parse(PageContext context) {
        //解析组件
        SavePageRequest request = context.getRequest();
        if(StringUtils.isNotEmpty(request.getComponents())) {
            JSONArray jsonArray = JSONArray.parseArray(request.getComponents());
            Map<String, List<JSONObject>> componentsMap = jsonArray.stream().map(obj -> (JSONObject) obj).collect(Collectors.groupingBy(obj -> obj.getString("type")));
            //图片组件
            context.setImageComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.IMAGE.getCode()), ImageComponentBO.class));
            //banner组件
            context.setBannerComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.BANNER.getCode()), BannerComponentBO.class));
            //商品组件
            context.setProductComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.PRODUCT.getCode()), ProductComponentBO.class));
            //档期活动组件
            context.setActivityComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.ACTIVITY.getCode()), ActivityComponentBO.class));
            //导航组件
            context.setNavigationComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.NAVIGATION.getCode()), NavigationComponentBO.class));
            //视频组件
            context.setVideoComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.VIDEO.getCode()), VideoComponentBO.class));
            //优惠券组件
            context.setCouponComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.COUPON.getCode()), CouponComponentBO.class));
            //积分优惠券组件
            context.setPointsCouponComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.POINTSCOUPON.getCode()), PointsCouponComponentBO.class));
            //高佣组件
            context.setProfitComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.PROFITADDITIONAL.getCode()), ProfitAdditionalComponentBO.class));
            //榜单组件
            context.setTopListComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.TOPLIST.getCode()), TopListComponentBO.class));
            //秒杀组件
            context.setSeckillComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.SECKILL.getCode()), SeckillComponentBO.class));
            //分隔符组件
            context.setSeparatorComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.SEPARATOR.getCode()), SeparatorComponentBO.class));
            //图片魔方组件
            context.setCubeComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.CUBE.getCode()), CubeComponentBO.class));
            //文本组件
            context.setTextComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.TEXT.getCode()), TextComponentBO.class));
            //素材组件
            context.setMaterialComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.MATERIAL.getCode()), MaterialComponentBO.class));
            //全部商品组件
            context.setAllProductComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.ALLPRODUCT.getCode()), AllProductComponentBO.class));
            //爱豆激励组件
            context.setUserIncentiveComponents(getDataByJsonObject(componentsMap.get(ComponentTypeEnum.USERINCENTIVE.getCode()), UserIncentiveComponentBO.class));
        }
        return new InnerResult<BusinessModel>(true, null);
    }

    
}
