package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeSlotBO;
import com.mengxiang.mshop.cms.core.model.enums.DataRuleTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.TimeEffectiveType;
import com.mengxiang.mshop.cms.core.model.enums.TimeType;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * banner 组件校验
 * <AUTHOR>
 */
@Component
public class ActivityComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        if (CollectionUtils.isEmpty(context.getActivityComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        context.getActivityComponents().stream().forEach(activity ->{
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(activity.getActivityRuleConfigDetails()), "档期组件 档期配置 不能为空");
            Preconditions.checkArgument(activity.getActivityRuleConfigDetails().size() <= 10, "档期组件 档期配置 不能超过10个");
            activity.getActivityRuleConfigDetails().stream().forEach(p ->{
                Preconditions.checkArgument(StringUtils.isNotEmpty(p.getRuleType()), "档期组件 选品规则 不能为空");
                if (DataRuleTypeEnum.SELF.getCode().equals(p.getRuleType())) {
                    Preconditions.checkArgument(StringUtils.isNotEmpty(p.getBusinessId()), "档期组件 自主选择的业务ID 不能为空");
                } else if (DataRuleTypeEnum.RULE.getCode().equals(p.getRuleType())) {
                    Preconditions.checkArgument(StringUtils.isNotEmpty(p.getRuleCode()), "档期组件 选品规则code 不能为空");
                    TimeConfigBO timeConfigBO = p.getTimeConfig();
                    Preconditions.checkArgument(Objects.nonNull(timeConfigBO), "档期组件 规则生效方式 不能为空");
                    if (timeConfigBO.getEffectiveType().equals(TimeEffectiveType.REGULAR_TIME.getCode()) && timeConfigBO.getTimeType().equals(TimeType.MULTIPLE.getCode())) {
                        Preconditions.checkArgument(timeConfigBO.getTimeList().stream().allMatch(t -> StringUtils.isNotEmpty(t.getStartTime()) && StringUtils.isNotEmpty(t.getEndTime())), "档期组件 规则生效方式多时段 不能为空");
                    }
                }
            });
            List<TimeSlotBO> timeList = activity.getActivityRuleConfigDetails().stream()
                    .map(config -> {
                        TimeConfigBO timeConfigBO = config.getTimeConfig();
                        if (config.getRuleType().equals(DataRuleTypeEnum.RULE.getCode()) && TimeConfigUtils.isMultiple.test(timeConfigBO)) {
                            return timeConfigBO.getTimeList();
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeList)) {
                Preconditions.checkArgument(!TimeConfigUtils.isTimeSlotsOverlap(timeList), "档期组件 多个规则时间段不能重叠");
            }
        });
        return new InnerResult<BusinessModel>(true, null);
    }
}
