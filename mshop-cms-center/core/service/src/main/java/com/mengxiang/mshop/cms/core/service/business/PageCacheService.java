package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.PageType;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date: 2023/4/12
 * @Description:
 */
public interface PageCacheService {

    /**
     * 缓存页面
     * @param page
     * @return
     */
    boolean syncCache (PageBO page);

    void compensatePageCacheByClosestVersion (String pageCode);

    boolean compensatePageCacheForFail ();

    boolean syncPageJsonByPage (PageBO page);

    boolean syncPageJsonByTypeAndOwnerId (String type, String ownerId,String syncPageCode);

    Optional<String>  getPageJsonByTypeAndOwnerId(String type, String ownerId);

    boolean clearPageJsonByTypeAndOwnerId (String type, String ownerId);

    Optional<String> getPageJsonByCode(String pageCode);

    boolean clearPageJsonByCode(String pageCode);

    Optional<String> getPageJsonByVersion(String pageCode, String version);

    boolean clearPageJsonByVersion(String pageCode, String version);



    boolean compensatePageCache();

    void putPageCaffeineCache(String pageCode, String version);

    void clearPageCaffeineCache(String pageCode, String version);

    String findCaffeineCacheByKey(String key);

    void buildCaffeineCache();

}
