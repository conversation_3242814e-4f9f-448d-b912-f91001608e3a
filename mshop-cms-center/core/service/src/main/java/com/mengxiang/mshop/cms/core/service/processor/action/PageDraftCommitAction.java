package com.mengxiang.mshop.cms.core.service.processor.action;

import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * 页面配置信息提交
 * <AUTHOR>
 */
@Service
public class PageDraftCommitAction extends AbstractBusinessAction<PageContext, BusinessModel> {

    @Autowired
    private MshopPageDraftDao pageDraftDao;


    @Override
    protected void beforeAction(PageContext context) {

    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        MshopPageDraftDo featurePageDraft = context.getFeaturePageDraft();
        if (Objects.nonNull(featurePageDraft)) {
            int res = pageDraftDao.insert(featurePageDraft);
            if (res <= 0) {
                context.setNeedInterrupt(true);
                return new InnerResult<BusinessModel>(CmsErrorCodeEnum.SAVE_PAGE_ERROR.getErrorCode(), "mshop-cms-center");
            }
        }
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
