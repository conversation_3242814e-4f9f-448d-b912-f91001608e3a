package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeSlotBO;
import com.mengxiang.mshop.cms.core.model.domain.product.ProductComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.DataRuleTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.TimeEffectiveType;
import com.mengxiang.mshop.cms.core.model.enums.TimeType;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * banner 组件校验
 * <AUTHOR>
 */
@Component
public class ProductComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        if (CollectionUtils.isEmpty(context.getProductComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        for (ProductComponentBO productComponent : context.getProductComponents()) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(productComponent.getAnimationType()), "商品组件 动效 不能为空");
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(productComponent.getProductRuleConfigDetails()), "商品组件 商品配置 不能为空");
            productComponent.getProductRuleConfigDetails().stream().forEach(p ->{
                Preconditions.checkArgument(StringUtils.isNotEmpty(p.getRuleType()), "商品组件 选品规则 不能为空");
                if (DataRuleTypeEnum.SELF.getCode().equals(p.getRuleType())) {
                    Preconditions.checkArgument(StringUtils.isNotEmpty(p.getBusinessId()), "商品组件 自主选择的业务ID 不能为空");
                } else if (DataRuleTypeEnum.RULE.getCode().equals(p.getRuleType())) {
                    Preconditions.checkArgument(StringUtils.isNotEmpty(p.getRuleCode()), "商品组件 选品规则code 不能为空");
                    TimeConfigBO timeConfigBO = p.getTimeConfig();
                    Preconditions.checkArgument(Objects.nonNull(timeConfigBO), "商品组件 规则生效方式 不能为空");
                    if (timeConfigBO.getEffectiveType().equals(TimeEffectiveType.REGULAR_TIME.getCode()) && timeConfigBO.getTimeType().equals(TimeType.MULTIPLE.getCode())) {
                        Preconditions.checkArgument(timeConfigBO.getTimeList().stream().allMatch(t -> StringUtils.isNotEmpty(t.getStartTime()) && StringUtils.isNotEmpty(t.getEndTime())), "商品组件 规则生效方式多时段 不能为空");
                    }
                }
            });

            List<TimeSlotBO> timeList = productComponent.getProductRuleConfigDetails().stream()
                    .map(config -> {
                        TimeConfigBO timeConfigBO = config.getTimeConfig();
                        if (config.getRuleType().equals(DataRuleTypeEnum.RULE.getCode()) && TimeConfigUtils.isMultiple.test(timeConfigBO)) {
                            return timeConfigBO.getTimeList();
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeList)) {
                Preconditions.checkArgument(!TimeConfigUtils.isTimeSlotsOverlap(timeList), "商品组件 多个规则时间段不能重叠");
            }
        }

        return new InnerResult<BusinessModel>(true, null);
    }

}
