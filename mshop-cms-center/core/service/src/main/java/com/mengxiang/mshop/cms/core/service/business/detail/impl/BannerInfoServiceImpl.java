package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * banner 组件 BannerComponentBO.class
 * <AUTHOR>
 */
@Service
public class BannerInfoServiceImpl extends AbstractComponentInfoService<BannerComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    public BannerComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        BannerComponentBO banner = JSON.parseObject(metaConfig, BannerComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),banner);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, BannerComponentConfigDetailBO.class))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .ifPresent(banner::setCarouselConfigDetails);
        if (CollectionUtil.isNotEmpty(banner.getCarouselConfigDetails())) {
            banner.getCarouselConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return banner;
    }

    @Override
    public BannerComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        BannerComponentBO banner = JSON.parseObject(metaConfig, BannerComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),banner);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(banner.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(banner.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, BannerComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(banner::setCarouselConfigDetails);
        if (CollectionUtil.isNotEmpty(banner.getCarouselConfigDetails())) {
            banner.getCarouselConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return banner;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public BannerComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        BannerComponentBO banner = JSON.parseObject(componentStr, BannerComponentBO.class);
        //组件是否生效
        if (Objects.nonNull(banner.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(banner.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(banner.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(banner.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        if (CollectionUtil.isNotEmpty(banner.getCarouselConfigDetails())) {
            //过滤无效配置详情
            List<BannerComponentConfigDetailBO> list = this.queryEffectiveDetails(banner.getCarouselConfigDetails());
            if (CollectionUtil.isEmpty(list)) {
                return null;
            }
            banner.setCarouselConfigDetails(list);
        }
        return banner;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.BANNER;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
