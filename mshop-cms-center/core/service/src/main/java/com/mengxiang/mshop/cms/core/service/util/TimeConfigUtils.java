package com.mengxiang.mshop.cms.core.service.util;

import cn.hutool.core.collection.CollectionUtil;
import com.mengxiang.mshop.cms.core.model.enums.TimeEffectiveType;
import com.mengxiang.mshop.cms.core.model.enums.TimeType;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeSlotBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * 生效时间校验工具类
 * <AUTHOR>
 */
public class TimeConfigUtils {

    /**
     * 检查规则是否生效
     *
     * @param timeConfig
     * @return true=生效 false=不生效
     */
    public static Boolean checkRuleTime(TimeConfigBO timeConfig) {
        if(Objects.isNull(timeConfig)){
            return Boolean.TRUE;
        }
        if(timeConfig.getEffectiveType() == 1){
            //立即生效
            return Boolean.TRUE;
        }
        Date nowTime = new Date();
        if (Objects.isNull(timeConfig.getTimeType())) {
            if (CollectionUtil.isNotEmpty(timeConfig.getTimeList())) {
                timeConfig.setTimeType(1);
            }
            if (Objects.nonNull(timeConfig.getTimeCycle())) {
                timeConfig.setTimeType(2);
            }
        }
        if (timeConfig.getTimeType() == 1) {
            //多时段
            for (TimeSlotBO timeConfigDTO : timeConfig.getTimeList()) {
                Date startTime = DateUtil.parseDate(timeConfigDTO.getStartTime());
                Date endTime = DateUtil.parseDate(timeConfigDTO.getEndTime());
                if (DateUtil.isEnable(nowTime, startTime, endTime)) {
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        } else if (timeConfig.getTimeType() == 2) {
            // 循环时段
            if (timeConfig.getTimeCycle().getTimeType() == 1) {
                return DateUtil.checkDateByWeek(timeConfig.getTimeCycle().getTimeWeekValue(), timeConfig.getTimeCycle().getStartTime(), timeConfig.getTimeCycle().getEndTime());
            } else if (timeConfig.getTimeCycle().getTimeType() == 2) {
                return DateUtil.checkDateByMonth(timeConfig.getTimeCycle().getTimeMonthValue(), timeConfig.getTimeCycle().getStartTime(), timeConfig.getTimeCycle().getEndTime());
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 周期类型 判断是否重叠
     * @param weeklyDay
     * @param monthlyDay
     * @return
     */
    public static boolean isRecurringTimeSlotsOverlap(DayOfWeek weeklyDay, int monthlyDay) {
        LocalDate currentDate = LocalDate.now();
        LocalDate nextWeeklyDate = getNextDayOfWeek(currentDate, weeklyDay);
        YearMonth currentYearMonth = YearMonth.from(currentDate);
        YearMonth nextMonthlyYearMonth = getNextMonthlyYearMonth(currentYearMonth, monthlyDay);

        return nextWeeklyDate.isEqual(nextMonthlyYearMonth.atDay(monthlyDay));
    }

    private static LocalDate getNextDayOfWeek(LocalDate date, DayOfWeek dayOfWeek) {
        return date.with(TemporalAdjusters.next(dayOfWeek));
    }

    private static YearMonth getNextMonthlyYearMonth(YearMonth currentYearMonth, int dayOfMonth) {
        YearMonth nextYearMonth = currentYearMonth.plusMonths(1);
        int lastDayOfMonth = nextYearMonth.lengthOfMonth();
        if (dayOfMonth <= lastDayOfMonth) {
            return nextYearMonth;
        }
        return nextYearMonth.plusMonths(1);
    }


    /**
     * 查询当前规则状态
     *
     * @param timeConfig
     * @return 规则状态 1=未生效 2=已生效 3=已失效.
     */
    public static Integer findRuleStatus(TimeConfigBO timeConfig) {
        if(timeConfig.getEffectiveType() == 1){
            //立即生效
            return 2;
        }
        Integer status = 1;
        if (timeConfig.getEffectiveType() == 2 && TimeConfigUtils.checkRuleTime(timeConfig)) {
            status = 2;
        } else if (timeConfig.getEffectiveType() == 2
                && timeConfig.getTimeType() == 1
                && !checkRuleTimeByEndTime(timeConfig)) {
            status = 3;
        }
        return status;
    }

//    public static Boolean checkRuleTimeNonNull(TimeConfigBO timeConfig) {
//        if (Objects.isNull(timeConfig)) {
//            return Boolean.FALSE;
//        }
//        if (timeConfig.getTimeType()==1){
//            //多时段
//            for (TimeSlotBO timeConfigDTO : timeConfig.getTimeList()) {
//                if (StringUtils.isNotBlank(timeConfigDTO.getStartTime()) || StringUtils.isNotBlank(timeConfigDTO.getEndTime())) {
//                    return Boolean.FALSE;
//                }
//            }
//        }
//        if (timeConfig.getTimeType()==2){
//            //循环时段
//            if (StringUtils.isBlank(timeConfig.getTimeCycle().getStartTime()) || StringUtils.isBlank(timeConfig.getTimeCycle().getEndTime())) {
//                return Boolean.FALSE;
//            }
//        }
//        //todo 看下前端页面传的时间是什么样的，然后根据传的参数来调整这个校验逻辑,比如要确定是多时段还是循环时段，然后再去对应的对象里校验开始结束时间是否为空
//        return Boolean.TRUE;
//    }
    /**
     * 检查结束时间是否都过期，返回false=全过期，true=有没过期的
     *
     * @param timeConfig
     * @return
     */
    public static Boolean checkRuleTimeByEndTime(TimeConfigBO timeConfig) {
        if(Objects.isNull(timeConfig)){
            return Boolean.TRUE;
        }
        if (timeConfig.getEffectiveType() == 1) {
            return Boolean.TRUE;
        }
        if (timeConfig.getTimeType() == 1) {
            //多时段
            Date nowTime = new Date();
            for (TimeSlotBO timeConfigDTO : timeConfig.getTimeList()) {
                Date endTime = DateUtil.parseDate(timeConfigDTO.getEndTime());
                if (nowTime.before(endTime)) {
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    public static Predicate<TimeConfigBO> isMultiple = config ->
            Objects.nonNull(config) &&
                    config.getEffectiveType().equals(TimeEffectiveType.REGULAR_TIME.getCode()) &&
                    config.getTimeType().equals(TimeType.MULTIPLE.getCode()) &&
                    CollectionUtils.isNotEmpty(config.getTimeList());

    public static boolean isTimeSlotsOverlap (List<TimeSlotBO> timeSlots) {
        return timeSlots.stream()
                .flatMap(slotA -> timeSlots.stream()
                        .filter(slotB -> !slotB.equals(slotA))
                        .filter(slotB -> isOverlap(slotA, slotB)))
                .findFirst()
                .isPresent();
    }

    private static boolean isOverlap(TimeSlotBO slotA, TimeSlotBO slotB) {
        Date slotAStartTime = DateUtil.parseDate(slotA.getStartTime());
        Date slotAEndTime = DateUtil.parseDate(slotA.getEndTime());
        Date slotBStartTime = DateUtil.parseDate(slotB.getStartTime());
        Date slotBEndTime = DateUtil.parseDate(slotB.getEndTime());
        if (Objects.isNull(slotAStartTime) || Objects.isNull(slotAEndTime) || Objects.isNull(slotBStartTime) || Objects.isNull(slotBEndTime)) {
            return true;
        }
        return slotAEndTime.after(slotBStartTime) && slotBEndTime.after(slotAStartTime);
    }

    /**
     * 检查开始时间是否未来
     * 是 = true
     * @param timeConfig
     * @return
     */
    public static Boolean checkRuleTimeByStartTime(TimeConfigBO timeConfig) {
        if(Objects.isNull(timeConfig)){
            return Boolean.FALSE;
        }
        if (timeConfig.getEffectiveType() == 1) {
            return Boolean.FALSE;
        }
        if (timeConfig.getTimeType() == 1) {
            //多时段
            Date nowTime = new Date();
            for (TimeSlotBO timeConfigDTO : timeConfig.getTimeList()) {
                Date startTime = DateUtil.parseDate(timeConfigDTO.getStartTime());
                if (nowTime.before(startTime)) {
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }
}
