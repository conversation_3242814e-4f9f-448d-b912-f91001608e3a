package com.mengxiang.mshop.cms.core.service.securty.impl;


import com.aikucun.common2.base.Result;
import com.aikucun.security.ugc.api.dto.request.ScanResult2Query;
import com.aikucun.security.ugc.api.dto.request.VideoScanReq2DTO;
import com.aikucun.security.ugc.api.dto.response.VideoScanApiAsyncResultResp;
import com.aikucun.security.ugc.api.dto.response.VideoScanApiResult2Resp;
import com.aikucun.security.ugc.api.feign.SecurityVideoScanApiClient;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.core.model.enums.ContentSuggestTypeEnum;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.model.result.content.ContentQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SecurityVideoClient {
    
    @Value("${risk.check.application.name:mshop-cms-center}")
    private String appId;
    
    @Value("${risk.check.app-secret:65527753358b41e28243a05d5a6f75b7}")
    private String appSecret;
    
    @Value("${risk.check.application.name:mshop-cms-center}")
    private String bizType;
    
    @Resource
    private SecurityVideoScanApiClient securityVideoScanApiClient;
    
    public List<ContentCheckResponse> riskVideoCheck(List<String> contents, String userId) {
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }
        List<List<String>> partitions = Lists.partition(contents, 20);
        List<ContentCheckResponse> resp = new ArrayList<>();
        for (List<String> partition : partitions) {
            List<ContentCheckResponse> contentCheckResponses = doRiskVideoCheck(partition, userId);
            if (!CollectionUtils.isEmpty(contentCheckResponses)) {
                resp.addAll(contentCheckResponses);
            }
        }
        return resp;
    }
    
    private List<ContentCheckResponse> doRiskVideoCheck(List<String> contents, String userId) {
        VideoScanReq2DTO videoScanReq2DTO = this.buildVideoScanReq(userId, contents);
        
        List<ContentCheckResponse> resp = contents.stream().map(e -> {
            ContentCheckResponse responseParam = new ContentCheckResponse();
            responseParam.setContent(e);
            responseParam.setQualifiedFlag(false);
            return responseParam;
        }).collect(Collectors.toList());
        
        try {
            Result<List<VideoScanApiAsyncResultResp>> result = securityVideoScanApiClient.videoScanAsync2(videoScanReq2DTO);
            log.info("[[doRiskVideoCheck]] 视屏check userId:{},contents:{},resp:{}",userId,contents,JSON.toJSONString(result));
            if (null == result || !result.getSuccess()) {
                log.warn("[[doRiskVideoCheck]] 调用图片安全校验出现异常 contents:{}", JSON.toJSONString(result));
                throw new BusinessException("视频校验失败,请稍后重试");
            }
            List<VideoScanApiAsyncResultResp> data = result.getData();
            if (CollectionUtils.isEmpty(result.getData())) {
                resp.stream().forEach(e->e.setCheckDesc(Lists.newArrayList("调用视屏审核 返回为空")));
                return resp;
            }
            //查询被风控
            Map<String, VideoScanApiAsyncResultResp> resps = data.stream()
                    .collect(Collectors.toMap(VideoScanApiAsyncResultResp::getUrl, v -> v, (v1, v2) -> v1));
            
            for (ContentCheckResponse contentCheckResponse : resp) {
                VideoScanApiAsyncResultResp imageScanApiResult2Resp = resps.get(contentCheckResponse.getContent());
                //没有返回失败
                if (null == imageScanApiResult2Resp) {
                    contentCheckResponse.setCheckDesc(Lists.newArrayList("调用视屏审核 没有返回"));
                    continue;
                }
                //成功
                if(String.valueOf(HttpStatus.OK.value()).equals(imageScanApiResult2Resp.getCode())){
                    contentCheckResponse.setQualifiedFlag(true);
                    //设置数据检查id
                    contentCheckResponse.setDateId(imageScanApiResult2Resp.getDataId());
                    continue;
                }
                //其他失败原因
                String message = imageScanApiResult2Resp.getMessage();
                contentCheckResponse.setCheckDesc(Lists.newArrayList(message));
            }
            return resp;
        } catch (Exception e) {
            log.error("[[doRiskVideoCheck]] 调用视频内容安全校验出现异常 userId:{},req:{}",userId,contents, e);
        }
        //异常结果
        resp.stream().forEach(e->e.setCheckDesc(Lists.newArrayList("调用视屏审核 异常")));
        return resp;
    }
    
    
    public List<ContentQueryResponse> riskVideoQuery(List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return null;
        }
        List<List<String>> partitions = Lists.partition(dataIds, 20);
        List<ContentQueryResponse> resp = new ArrayList<>();
        for (List<String> partition : partitions) {
            List<ContentQueryResponse> contentCheckResponses = doRiskVideoQuery(partition);
            if (!CollectionUtils.isEmpty(contentCheckResponses)) {
                resp.addAll(contentCheckResponses);
            }
        }
        return resp;
    }
    
    private List<ContentQueryResponse> doRiskVideoQuery(List<String> dataIds) {
        ScanResult2Query scanResult2Query = this.buildScanResultQuery(dataIds);
        
        List<ContentQueryResponse> resp = dataIds.stream().map(e -> {
            ContentQueryResponse responseParam = new ContentQueryResponse();
            responseParam.setDataId(e);
            responseParam.setSuggestion(ContentSuggestTypeEnum.UN_KNOWN);
            return responseParam;
        }).collect(Collectors.toList());
        
        try {
            Result<List<VideoScanApiResult2Resp>> result = securityVideoScanApiClient
                    .queryScanResults2(scanResult2Query);
            if (null == result || !result.getSuccess()) {
                log.warn("[[doRiskVideoQuery]] 调用图片安全校验出现异常 contents:{}", JSON.toJSONString(result));
                throw new BusinessException("调用风控返回失败");
            }
            List<VideoScanApiResult2Resp> data = result.getData();
            if (CollectionUtils.isEmpty(result.getData())) {
                return resp;
            }
            //查询被风控
            Map<String, VideoScanApiResult2Resp> resps = data.stream()
                    .collect(Collectors.toMap(VideoScanApiResult2Resp::getDataId, v -> v, (v1, v2) -> v1));
            
            for (ContentQueryResponse contentCheckResponse : resp) {
                VideoScanApiResult2Resp videoQueryResult = resps.get(contentCheckResponse.getDataId());
                if (null == videoQueryResult) {
                    contentCheckResponse.setCheckDesc(Lists.newArrayList("未查询到 视频结果"));
                    log.warn("[[doRiskVideoQuery]] 未找到 视屏风控 结果 dataId:{}", contentCheckResponse.getDataId());
                    continue;
                }
                //设置内容
                contentCheckResponse.setUrl(videoQueryResult.getUrl());
                contentCheckResponse.setTaskId(videoQueryResult.getTaskId());
                //判断是否是处理中
                if("600100".equals(videoQueryResult.getCode())){
                    log.info("[[doRiskVideoQuery]] 视频检测 处理中 dataId:{},reps:{}", contentCheckResponse.getDataId(),resp);
                    contentCheckResponse.setSuggestion(ContentSuggestTypeEnum.PROCESSING);
                    contentCheckResponse.setCheckDesc(Lists.newArrayList(videoQueryResult.getMessage()));
                    continue;
                }
                if(!String.valueOf(HttpStatus.OK.value()).equals(videoQueryResult.getCode())){
                    log.warn("[[doRiskVideoQuery]] 视频检测异常 结果 dataId:{},reps:{}", contentCheckResponse.getDataId(),resp);
                    contentCheckResponse.setCheckDesc(Lists.newArrayList(videoQueryResult.getMessage()));
                    continue;
                }
                //转换suggestion
                String suggestion = videoQueryResult.getSuggestion();
                ContentSuggestTypeEnum suggesst = ContentSuggestTypeEnum.getEnumBySuggesst(suggestion);
                if (suggesst == null) {
                    contentCheckResponse.setCheckDesc(Lists.newArrayList("未匹配到检测建议"));
                    log.error("[[doRiskVideoQuery]] 未匹配到视屏风控 建议 dataId:{},suggest:{}", contentCheckResponse.getDataId(),
                            JSON.toJSONString(videoQueryResult));
                    continue;
                }
                //设置参数
                contentCheckResponse.setSuggestion(suggesst);
            }
            return resp;
        } catch (Exception e) {
            log.error("[[doRiskVideoQuery]] 调用视频内容安全 查询 出现异常 req:{}", dataIds, e);
        }
        return null;
    }
    
    
    private VideoScanReq2DTO buildVideoScanReq(String userId, List<String> urls) {
        VideoScanReq2DTO videoScanReqDTO = new VideoScanReq2DTO();
        videoScanReqDTO.setUrls(urls);
        videoScanReqDTO.setUserId(userId);
        videoScanReqDTO.setAppId(appId);
        videoScanReqDTO.setAppSecret(appSecret);
        videoScanReqDTO.setBizType(bizType);
        return videoScanReqDTO;
    }
    
    
    private ScanResult2Query buildScanResultQuery(List<String> dataIds) {
        ScanResult2Query scanResultQuery = new ScanResult2Query();
        scanResultQuery.setAppId(appId);
        scanResultQuery.setAppSecret(appSecret);
        scanResultQuery.setDataIds(dataIds);
        return scanResultQuery;
    }
}
