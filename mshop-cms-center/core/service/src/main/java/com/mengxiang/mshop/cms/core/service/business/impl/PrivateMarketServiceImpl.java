package com.mengxiang.mshop.cms.core.service.business.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.dc.aiward.facade.stub.rule.enumfile.SourceCode;
import com.aikucun.dc.aiward.facade.stub.rule.sell.RuleProductsDto;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleFacade;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketRuleBO;
import com.mengxiang.mshop.cms.core.model.domain.base.UserGroupBO;
import com.mengxiang.mshop.cms.core.model.enums.ChannelTypeEnum;
import com.mengxiang.mshop.cms.core.service.business.PrivateMarketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PrivateMarketServiceImpl implements PrivateMarketService {

    private final StringRedisTemplate redisTemplate;
    @Resource
    private SellRuleFacade sellRuleFacade;


    private static final String PRIVATE_MARKET_RULE_KEY = PageConstant.PRIVATE_MARKET_RULE_CACHE_KEY;

    public PrivateMarketServiceImpl(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<PrivateMarketRuleBO> queryPrivateRuleByActivityId(String activityId) {
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(PRIVATE_MARKET_RULE_KEY);
            return this.parsePrivateMarketRule(entries, activityId);
        } catch (Exception e) {
            log.error("查询指定活动id私密会场规则发生异常:{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<PrivateMarketRuleBO> queryAllPrivateRule() {
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(PRIVATE_MARKET_RULE_KEY);
            return this.parsePrivateMarketRule(entries, null);
        } catch (Exception e) {
            log.error("查询全部私密会场规则发生异常:{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    private List<PrivateMarketRuleBO> parsePrivateMarketRule(Map<Object, Object> entries, String activityId) {
        List<PrivateMarketRuleBO> privateMarketRuleBOS = new ArrayList<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            PrivateMarketRuleBO privateMarketRule = JSONObject.parseObject(entry.getValue().toString(),PrivateMarketRuleBO.class);
            List<String> activityIds = privateMarketRule.getActivityIds();
            if (StringUtils.isNotBlank(activityId)) {
                //只保留指定活动id关联的规则
                if (activityIds.contains(activityId)) {
                    privateMarketRuleBOS.add(privateMarketRule);
                }
            } else {
                privateMarketRuleBOS.add(privateMarketRule);
            }
        }
        return privateMarketRuleBOS;
    }

    @Override
    public void savePrivateRule(PrivateMarketRuleBO privateMarketRule) {
        String privateMarketRuleStr = JSONObject.toJSONString(privateMarketRule);
        redisTemplate.opsForHash().put(PRIVATE_MARKET_RULE_KEY, privateMarketRule.getMarketId(), privateMarketRuleStr);

    }

    @Override
    public void savePrivateRule(String marketId, List<Long> activityRuleIds, PrivateMarketConfigBO privateMarketConfig) {
        try {
            if (Objects.isNull(privateMarketConfig)){
                return;
            }
            List<String> activityIdsByRuleId = this.getActivityIdsByRuleId(activityRuleIds);
            if (CollectionUtils.isEmpty(activityIdsByRuleId)){
                log.warn("规则id:{}未查询到活动id",activityRuleIds);
                return;
            }
            PrivateMarketRuleBO privateMarketRule = buildPrivateMarketRuleBO(marketId, privateMarketConfig, activityIdsByRuleId);
            String privateMarketRuleStr = JSONObject.toJSONString(privateMarketRule);
            redisTemplate.opsForHash().put(PRIVATE_MARKET_RULE_KEY, privateMarketRule.getMarketId(), privateMarketRuleStr);
        } catch (Exception e) {
            log.error("保存私密会场规则发生异常:{}", e.getMessage(), e);
        }
    }

    @Override
    public void expirePrivateRule(String marketId) {
        try {
            Long delete = redisTemplate.opsForHash().delete(PRIVATE_MARKET_RULE_KEY, marketId);
            if (delete == 0) {
                log.warn("删除私密策略，未查询到会场id:{}的策略", marketId);
            }else {
                log.info("删除会场id:{}的私密会场策略成功", marketId);
            }
        } catch (Exception e) {
            log.error("删除私密会场策略发生异常:{}", e.getMessage(), e);
        }
    }


    private static PrivateMarketRuleBO buildPrivateMarketRuleBO(String marketId, PrivateMarketConfigBO privateMarketConfig, List<String> activityIdsByRuleId) {
        ChannelTypeEnum channelType = privateMarketConfig.getChannelType();
//        List<String> userGroupIds = privateMarketConfig.getUserGroupIds();
        List<UserGroupBO> userGroups = privateMarketConfig.getUserGroups();
        PrivateMarketRuleBO privateMarketRule = new PrivateMarketRuleBO();
        privateMarketRule.setMarketId(marketId);
        privateMarketRule.setChannelType(channelType);
//        privateMarketRule.setUserGroupIds(userGroupIds);
        if (CollectionUtils.isNotEmpty(userGroups)) {
            //传了用户群组列表，优先使用群组列表的信息
            privateMarketRule.setUserGroupIds(userGroups.stream().map(UserGroupBO::getUserGroupId).collect(Collectors.toList()));
            privateMarketRule.setUserGroups(userGroups);
        }
        privateMarketRule.setActivityIds(activityIdsByRuleId);
        return privateMarketRule;
    }

    private List<String> getActivityIdsByRuleId(List<Long> activityRuleIds) {
        List<String> activityIds = new ArrayList<>();
        for (Long activityRuleId : activityRuleIds) {
            Result<RuleProductsDto> ruleProducts2 = sellRuleFacade.getRuleProducts2(SourceCode.XD, null, null, null, null, activityRuleId);
            if (ruleProducts2 != null && ruleProducts2.getSuccess()) {
                RuleProductsDto data = ruleProducts2.getData();
                activityIds.addAll(data.getProducts());
            }
        }
        return activityIds;
    }

}
