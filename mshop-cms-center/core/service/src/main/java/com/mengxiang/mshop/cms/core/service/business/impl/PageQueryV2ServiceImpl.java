package com.mengxiang.mshop.cms.core.service.business.impl;

import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleDto;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleGetBatchRequestDto;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.common.service.integration.feign.DcAiwardClient;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.request.PageInfoRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.mengxiang.mshop.cms.core.service.business.PageQueryService;
import com.mengxiang.mshop.cms.core.service.business.PageQueryV2Service;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/5/10
 * @Description: 支持 用户分群
 */
@Service
@Slf4j
public class PageQueryV2ServiceImpl implements PageQueryV2Service {

    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private PageCacheService pageCacheService;

    @Autowired
    private DcAiwardClient dcAiwardClient;

    @Override
    public PageBO detailByCache (PageInfoRequest request) {
        return Optional.ofNullable(request.getVersion())
                .map(v -> pageCacheService.getPageJsonByVersion(request.getPageCode(), v))
                .orElse(pageCacheService.getPageJsonByCode(request.getPageCode()))
                .map(json -> JSONObject.parseObject(json, PageBO.class))
                .filter(Objects::nonNull)
                .filter(page -> TimeConfigUtils.checkRuleTime(page.getTimeConfig()) && PageInstanceStatusEnum.isShowStatus(page.getStatus()))
                .map(page -> {
                    String componentStr = pageQueryService.convertEffectiveComponentJson(page.getComponents(),request.getAggrBaseReqModule());
                    page.setComponents(componentStr);
                    return page;
                })
                .orElseGet(() -> {
                    log.warn("detailByCache error pageCode:{}", request.getPageCode());
                    return null;
                });
    }

    @Override
    public PageBO detailByPageType(PageInfoRequest request) {
        Preconditions.checkArgument(!StringUtils.isEmpty(request.getOwnerId()), "所属者id 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(request.getPageType()), "页面类型 不能为空");

        PageType pageTypeEnum = PageType.translate(request.getPageType());
        if (Objects.isNull(pageTypeEnum)) {
            log.warn("detailByPageType pageTypeEnum is null ownerId:{} pageType:{}", request.getOwnerId(), request.getPageType());
            return null;
        }
        return Optional.ofNullable(pageCacheService.getPageJsonByTypeAndOwnerId(request.getPageType(), request.getOwnerId())
                .map(pageInfo -> JSONObject.parseObject(pageInfo, PageBO.class))
                .filter(pageResult -> TimeConfigUtils.checkRuleTime(pageResult.getTimeConfig()) && PageInstanceStatusEnum.isShowStatus(pageResult.getStatus()))
                .map(pageResult -> {
                    String componentStr = pageQueryService.convertEffectiveComponentJson(pageResult.getComponents(),request.getAggrBaseReqModule());
                    pageResult.setComponents(componentStr);
                    return pageResult;
                })
                .orElse(null))
                .orElseGet(() -> {
                    log.warn("detailByPageType pageInfo is null ownerId:{} pageType:{}", request.getOwnerId(), request.getPageType());
                    return null;
                });
    }

    @Override
    public List<PageRuleInfoResult> getRuleBatch(List<Long> ruleIds) {
        List<List<Long>> rules = Lists.partition(ruleIds, 10);
        List<PageRuleInfoResult> pageRuleInfoResults = Lists.newArrayList();
        for (List<Long> rs : rules) {
            SellRuleGetBatchRequestDto request = new SellRuleGetBatchRequestDto();
            request.setRuleIds(rs);
            List<SellRuleDto> result = dcAiwardClient.getRuleBatch(request);
            if (CollectionUtils.isNotEmpty(result)) {
                List<PageRuleInfoResult> list = result.stream().map(rule -> {
                    PageRuleInfoResult ruleInfo = new PageRuleInfoResult();
                    ruleInfo.setRuleName(rule.getName());
                    ruleInfo.setCreatedBy(rule.getCreatedBy());
                    ruleInfo.setEnable(rule.getEnable());
                    ruleInfo.setRuleId(String.valueOf(rule.getId()));
                    ruleInfo.setCreatedTime(Objects.isNull(rule.getCreatedTime()) ? null : DateUtil.dateToStrLong(rule.getCreatedTime()));
                    return ruleInfo;
                }).collect(Collectors.toList());
                pageRuleInfoResults.addAll(list);
            }
        }
        return pageRuleInfoResults;
    }
}
