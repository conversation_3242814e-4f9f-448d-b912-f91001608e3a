package com.mengxiang.mshop.cms.core.service.processor.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImageComponentBO;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/5/11
 * @Description: 图片组件
 */
@Service
@Slf4j
public class ImageComponentConverter extends AbstractBusinessConverter<PageContext, BusinessModel> {
    @Override
    protected InnerResult<BusinessModel> convert(PageContext context) {
        List<ImageComponentBO> imageComponents = context.getImageComponents();
        List<MshopComponentInstanceDo> components = context.getComponentInstances();
        List<MshopComponentInstanceDetailDo> componentDetails = context.getComponentInstanceDetails();
        if (CollectionUtils.isEmpty(imageComponents)) {
            return new InnerResult<>(Boolean.TRUE, null);
        }

        imageComponents.forEach(image -> {
            MshopComponentInstanceDo componentInstanceDo = components.stream()
                    .filter(x -> x.getComponentCode().equals(image.getComponentCode()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(componentInstanceDo) || (CollectionUtil.isEmpty(image.getHotConfigDetails()) && CollectionUtil.isEmpty(image.getHoleConfigDetails()))) {
                return;
            }
            image.getHotConfigDetails().stream().forEach(detail -> {
                MshopComponentInstanceDetailDo mshopComponentInstanceDetailDo = componentDetails.stream()
                        .filter(x -> x.getConfigDetailCode().equals(detail.getComponentDetailCode()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(mshopComponentInstanceDetailDo)) {
                    TimeConfigBO timeConfigBO = JSONObject.parseObject(componentInstanceDo.getTimeConfig(), TimeConfigBO.class);
                    detail.setTimeConfig(timeConfigBO);
                    mshopComponentInstanceDetailDo.setTimeConfig(componentInstanceDo.getTimeConfig());
                }
            });

            if (CollectionUtils.isNotEmpty(image.getHoleConfigDetails())) {
                image.getHoleConfigDetails().stream().forEach(detail -> {
                    MshopComponentInstanceDetailDo mshopComponentInstanceDetailDo = componentDetails.stream()
                            .filter(x -> x.getConfigDetailCode().equals(detail.getComponentDetailCode()))
                            .findFirst()
                            .orElse(null);
                    if (Objects.nonNull(mshopComponentInstanceDetailDo)) {
                        TimeConfigBO timeConfigBO = JSONObject.parseObject(componentInstanceDo.getTimeConfig(), TimeConfigBO.class);
                        detail.setTimeConfig(timeConfigBO);
                        mshopComponentInstanceDetailDo.setTimeConfig(componentInstanceDo.getTimeConfig());
                    }
                });
            }

        });
        return new InnerResult<>(Boolean.TRUE, null);
    }

}
