package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageContentCheckDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ContentCheckResultBO;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.ContentSuggestTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * video 组件 VideoComponentBO.class
 * <AUTHOR>
 */
@Service
public class VideoInfoServiceImpl extends AbstractComponentInfoService<VideoComponentBO> {

    @Autowired
    private MshopPageContentCheckDao pageContentCheckDao;

    @Autowired
    private DirectUserGroupService directUserGroupService;
    
    @Override
    public VideoComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        VideoComponentBO video = JSON.parseObject(metaConfig, VideoComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),video);
        if(CollectionUtils.isEmpty(componentDetail)){
            return video;
        }
        List<VideoComponentConfigDetailBO> details = this.doGetComponentDetail(component.getPageCode(),component.getVersion(),componentDetail);
        Optional.ofNullable(details)
                .filter(CollectionUtil::isNotEmpty).
                ifPresent(video::setVideoConfigDetails);
        if(CollectionUtil.isNotEmpty(details)){
            video.setVideoConfigDetails(details);
            video.getVideoConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return video;
    }

    @Override
    public VideoComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        VideoComponentBO video = JSON.parseObject(metaConfig, VideoComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),video);
        if(CollectionUtils.isEmpty(componentDetail)){
            return video;
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(video.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(video.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        List<VideoComponentConfigDetailBO> details = this.doGetComponentDetail(component.getPageCode(),component.getVersion(),componentDetail);
        Optional.ofNullable(details)
                .filter(CollectionUtil::isNotEmpty).
                ifPresent(video::setVideoConfigDetails);
        if(CollectionUtil.isNotEmpty(details)){
            video.setVideoConfigDetails(details);
            video.getVideoConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return video;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    public List<VideoComponentConfigDetailBO> doGetComponentDetail(String pageCode, String version, List<MshopComponentInstanceDetailDo> req) {
        List<String> componentDetailCodes = req.stream().map(x -> x.getConfigDetailCode()).collect(Collectors.toList());
        List<MshopPageContentCheckDo> mshopPageContentCheckDos = pageContentCheckDao.queryByComponentCodes(pageCode,version,componentDetailCodes);
        return req.stream().map(detailDo -> {
                    VideoComponentConfigDetailBO detail = this.detailBase(detailDo, VideoComponentConfigDetailBO.class);
                    if (CollectionUtil.isNotEmpty(mshopPageContentCheckDos)) {
                        MshopPageContentCheckDo mshopPageContentCheckDo = mshopPageContentCheckDos
                                .stream()
                                .filter(e -> ContentSuggestTypeEnum.BLOCK.getSuggesst().equals(e.getCheckStatus()) || ContentSuggestTypeEnum.UN_KNOWN.getSuggesst().equals(e.getCheckStatus()))
                                .findFirst()
                                .orElse(null);
                        if (Objects.nonNull(mshopPageContentCheckDo)) {
                            ContentCheckResultBO contentCheck = new ContentCheckResultBO();
                            contentCheck.setIsFail(true);
                            contentCheck.setCheckResult(StringUtils.isEmpty(mshopPageContentCheckDo.getCheckResult()) ? PageConstant.PAGE_FAIL_CONTENT_MESSAGE : mshopPageContentCheckDo.getCheckResult());
                            detail.setCheckResult(contentCheck);
                        }
                    }
                    return detail;
                }).collect(Collectors.toList());
    }
    
    @Override
    public VideoComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        VideoComponentBO video = JSON.parseObject(componentStr, VideoComponentBO.class);
        if (Objects.nonNull(video.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(video.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }

        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(video.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(video.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        if(CollectionUtil.isNotEmpty(video.getVideoConfigDetails())){
            video.getVideoConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return video;
    }
    
    
    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.VIDEO;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
