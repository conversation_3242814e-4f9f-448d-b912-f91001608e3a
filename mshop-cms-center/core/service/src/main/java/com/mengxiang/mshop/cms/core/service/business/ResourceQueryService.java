package com.mengxiang.mshop.cms.core.service.business;


import com.mengxiang.mshop.cms.core.model.domain.resource.BannerResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.DiamondResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.StartupAdvertisementBO;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;

import java.util.List;

/**
 * @Auther: zhangmoxun
 * @Date: 2023/3/13
 * @Description:
 */
public interface ResourceQueryService {

    /**
     * 查询开机广告
     */
    StartupAdvertisementBO findStartupAdvertisement(ResourceRequest req);

    /**
     * 查询banner
     */
    List<BannerResourceBO> findBannerList(ResourceRequest req);


    /**
     * 查询金刚位
     */
    List<DiamondResourceBO.DiamondResourceConfig> findDiamond(ResourceRequest req);

    /**
     * 查询导航
     */
   List<NavigationResourceBO.ResourceConfig> findNavigation(ResourceRequest req);
}
