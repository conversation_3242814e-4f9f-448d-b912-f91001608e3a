package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.text.TextComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 素材组件
 * <AUTHOR>
 */
@Service
public class TextInfoServiceImpl extends AbstractComponentInfoService<TextComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    public TextComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        TextComponentBO textComponentBO = JSON.parseObject(metaConfig, TextComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),textComponentBO);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
        return textComponentBO;
    }

    @Override
    public TextComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        TextComponentBO image = JSON.parseObject(metaConfig, TextComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),image);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(image.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(image.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        return image;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public TextComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        TextComponentBO image = JSON.parseObject(componentStr, TextComponentBO.class);
        if (Objects.nonNull(image.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(image.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(image.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(image.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        return image;
    }
    
    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.TEXT;
    }


    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
