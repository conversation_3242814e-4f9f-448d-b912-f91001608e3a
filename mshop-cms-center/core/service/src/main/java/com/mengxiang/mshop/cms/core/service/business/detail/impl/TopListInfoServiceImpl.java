package com.mengxiang.mshop.cms.core.service.business.detail.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.toplist.TopListComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.toplist.TopListComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/4
 * @Description:
 */
@Service
public class TopListInfoServiceImpl extends AbstractComponentInfoService<TopListComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    protected TopListComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        TopListComponentBO toplist = JSON.parseObject(metaConfig, TopListComponentBO.class);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, TopListComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(toplist::setTopListConfigDetails);
        if (CollectionUtil.isNotEmpty(toplist.getTopListConfigDetails())) {
            toplist.getTopListConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return toplist;
    }

    @Override
    protected TopListComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        TopListComponentBO toplist = JSON.parseObject(metaConfig, TopListComponentBO.class);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, TopListComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(toplist::setTopListConfigDetails);
        if (CollectionUtil.isNotEmpty(toplist.getTopListConfigDetails())) {
            toplist.getTopListConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return toplist;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public TopListComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        TopListComponentBO toplist = JSON.parseObject(componentStr, TopListComponentBO.class);
        if (Objects.nonNull(toplist.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(toplist.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }

        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(toplist.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(toplist.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        if (CollectionUtil.isNotEmpty(toplist.getTopListConfigDetails())) {
            toplist.getTopListConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return toplist;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.TOPLIST;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
