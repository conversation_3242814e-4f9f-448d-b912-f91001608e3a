package com.mengxiang.mshop.cms.core.service.business.detail.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.seckill.SeckillComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.seckill.SeckillComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/4
 * @Description:
 */
@Service
public class SeckillInfoServiceImpl extends AbstractComponentInfoService<SeckillComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    protected SeckillComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        SeckillComponentBO seckill = JSON.parseObject(metaConfig, SeckillComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),seckill);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, SeckillComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(seckill::setSeckillConfigDetails);
        if (CollectionUtil.isNotEmpty(seckill.getSeckillConfigDetails())) {
            seckill.getSeckillConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return seckill;
    }

    @Override
    protected SeckillComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        SeckillComponentBO seckill = JSON.parseObject(metaConfig, SeckillComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),seckill);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(seckill.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(seckill.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, SeckillComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(seckill::setSeckillConfigDetails);
        if (CollectionUtil.isNotEmpty(seckill.getSeckillConfigDetails())) {
            seckill.getSeckillConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return seckill;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public SeckillComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        SeckillComponentBO seckill = JSON.parseObject(componentStr, SeckillComponentBO.class);
        if (Objects.nonNull(seckill.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(seckill.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }

        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(seckill.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(seckill.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        if (CollectionUtil.isNotEmpty(seckill.getSeckillConfigDetails())) {
            seckill.getSeckillConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return seckill;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.SECKILL;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
