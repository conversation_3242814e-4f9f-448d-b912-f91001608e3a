package com.mengxiang.mshop.cms.core.service.business.impl;

import com.aikucun.dc.aiward.facade.stub.common.OrderDto;
import com.aikucun.dc.aiward.facade.stub.rule.enumfile.SourceCode;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleCreateDto;
import com.aikucun.dc.aiward.facade.stub.selection.LabelsResponseDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.common.service.integration.feign.DcAiwardClient;
import com.mengxiang.mshop.cms.core.model.enums.PromotionTypeEnum;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.core.service.business.BIDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BIDataServiceImpl implements BIDataService {

    @Value("${page.merchant.default.create.orderBy:688}")
    private String defaultOrderBy;

    @Autowired
    private DcAiwardClient dcAiwardClient;

    @Value("${page.merchant.label.orderBy:688}")
    private String label;

    @Value("${page.merchant.label.group:SPU_MERCHANT_SHOP_MARKET}")
    private String labelGroup;

    @Value("${page.merchant.label.productTitleImgUrl:}")
    private String productTitleImgUrl;

   @Value("${page.merchant.label.default.productTitleImgUrl:https://akim-oss.aikucun.com/f49eb79a7baf4eef234fd5fd446833b4a787bcb4_1704339110428_35.png}")
   private String defaultProductTitleImgUrl;


    /**
     * 创建商家规则
     *
     * @param merchantId
     * @param merchantShopId
     * @param operatorUserName
     * @return
     */
    public Long createRule(String merchantId, String merchantShopId, String operatorUserName) {
        try {
            SellRuleCreateDto req = new SellRuleCreateDto();
            req.setName("商家规则");
            req.setSourceCode(SourceCode.MERCHANT);
            req.setRuleType(1);
            req.setRuleBindType(5);
            req.setEnable(Boolean.FALSE);
            req.setMerchantId(merchantId);
            req.setMerchantShopId(merchantShopId);
            req.setOperatorUserName(operatorUserName);
            req.setFilter(new String[0]);
            OrderDto orderDto = new OrderDto();
            orderDto.setOrderBy(defaultOrderBy);
            orderDto.setOrderType("DESC");
            req.setOrderList(Lists.newArrayList(orderDto));
            return dcAiwardClient.createRuleByMerchantInfo(req);
        } catch (Exception ex) {
            log.error("createRule error req:{},{},{}", merchantId, merchantShopId, operatorUserName, ex);
        }
        return null;
    }


    /**
     * 查询选品中心营销标签
     */
    @Override
    public List<SafeModelRuleLabelResult> findSafeModelRuleLabel(){
        JSONObject config = JSON.parseObject(productTitleImgUrl);
        List<SafeModelRuleLabelResult> result = Lists.newArrayList();
        for (PromotionTypeEnum typeEnum : PromotionTypeEnum.values()) {
            //排除全部档期
            if(typeEnum.getCode().equals(PromotionTypeEnum.ALL_ACTIVITY.getCode())){
                continue;
            }

            SafeModelRuleLabelResult labelResult =  new SafeModelRuleLabelResult();
            labelResult.setPromotionType(typeEnum.getCode());
            labelResult.setName(typeEnum.getDesc());
            if(Objects.nonNull(config)
                    &&  StringUtils.isNotBlank(config.getString(typeEnum.getCode()))){
                labelResult.setTitleImgUrl(config.getString(typeEnum.getCode()));
            }else{
                labelResult.setTitleImgUrl(defaultProductTitleImgUrl);
            }
            result.add(labelResult);
        }
        return result;
    }
}
