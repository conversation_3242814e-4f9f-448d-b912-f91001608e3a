package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.base.PrivateMarketRuleBO;

import java.util.List;

public interface PrivateMarketService {

    /**
     * 根据活动id查询私密会场策略
     * @param activityId 活动id
     * @return List
     */
    List<PrivateMarketRuleBO> queryPrivateRuleByActivityId(String activityId);

    /**
     * 查询所有的私密会场策略
     * @return  List
     */
    List<PrivateMarketRuleBO> queryAllPrivateRule();

    /**
     * 保存私密会场策略信息
     * @param privateMarketRule 策略对象
     */
    void savePrivateRule(PrivateMarketRuleBO privateMarketRule);

    /**
     * 保存私密会场信息
     * @param marketId 会场id
     * @param activityRuleIds 规则id
     * @param privateMarketConfigBO 私密会场配置
     */
    void savePrivateRule(String marketId,List<Long> activityRuleIds,PrivateMarketConfigBO privateMarketConfigBO);

    /**
     * 失效私密会场策略
     * @param marketId
     */
    void expirePrivateRule(String marketId);

}
