package com.mengxiang.mshop.cms.core.service.context;

import com.mengxiang.base.common.process.model.ServiceResult;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description: 业务服务返回对象
 * @param <T>
 */
@Data
@Accessors(chain = true)
public class BusinessServiceResult<T> extends ServiceResult {

    private T data;

    public BusinessServiceResult() {
    }

    public BusinessServiceResult(T t, Boolean success) {
        super.setSuccess(success);
        this.data = t;
    }


}
