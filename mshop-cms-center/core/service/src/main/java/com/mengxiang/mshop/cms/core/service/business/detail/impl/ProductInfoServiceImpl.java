package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.product.ProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.ProductComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.DataRuleTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Product 组件 ProductComponentBO.class
 * <AUTHOR>
 */
@Service
public class ProductInfoServiceImpl extends AbstractComponentInfoService<ProductComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    public ProductComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        ProductComponentBO product = JSON.parseObject(metaConfig, ProductComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),product);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> {
                            ProductComponentConfigDetailBO detailBO = detailBase(detailDo, ProductComponentConfigDetailBO.class);
                            if (Objects.nonNull(detailBO.getTimeConfig())) {
                                Integer ruleStatus = TimeConfigUtils.findRuleStatus(detailBO.getTimeConfig());
                                detailBO.setRuleStatus(ruleStatus);
                                String ruleCreateTime = DateUtil.dateToStrLong(detailDo.getCreateTime());
                                detailBO.setRuleCreateTime(ruleCreateTime);
                            }
                            return detailBO;
                        })
                        .collect(Collectors.toList()))
                .ifPresent(product::setProductRuleConfigDetails);
        if (CollectionUtil.isNotEmpty(product.getProductRuleConfigDetails())) {
            product.getProductRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return product;
    }

    @Override
    public ProductComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        ProductComponentBO product = JSON.parseObject(metaConfig, ProductComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),product);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(product.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(product.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo ->  detailBase(detailDo, ProductComponentConfigDetailBO.class))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .ifPresent(product::setProductRuleConfigDetails);
        if (CollectionUtil.isNotEmpty(product.getProductRuleConfigDetails())) {
            //查DB 人群不为空, 端上预览
            if (Objects.nonNull(aggrBaseReqModule)) {
                boolean isRule = product.getProductRuleConfigDetails().stream().allMatch(x -> StringUtils.isNotEmpty(x.getRuleCode()) && x.getRuleType().equals(DataRuleTypeEnum.RULE.getCode()));
                //多个规则,选择生效时间最近的规则
                if (isRule) {
                    //有效的
                    List<ProductComponentConfigDetailBO> productRuleConfigDetails = queryEffectiveDetails(product.getProductRuleConfigDetails());
                    //未生效或已过期
                    List<ProductComponentConfigDetailBO> list2 = product.getProductRuleConfigDetails().stream().filter(detail -> !TimeConfigUtils.checkRuleTime(detail.getTimeConfig())).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(productRuleConfigDetails)) {
                        productRuleConfigDetails = list2;
                        ProductComponentConfigDetailBO detail = this.queryClosestDetailByPreview(productRuleConfigDetails);
                        if (Objects.isNull(detail)) {
                            return null;
                        }
                        product.setProductRuleConfigDetails(Arrays.asList(detail));
                    } else {
                        product.setProductRuleConfigDetails(productRuleConfigDetails);
                    }
                }
            }
            if (StringUtils.isEmpty(product.getRuleType())){
                String ruleType = product.getProductRuleConfigDetails().get(0).getRuleType();
                product.setRuleType(ruleType);
            }
            product.getProductRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return product;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Optional.ofNullable(componentStr)
                .filter(StringUtils::isNotEmpty)
                .map(str -> JSON.parseObject(str, ProductComponentBO.class))
                .map(ProductComponentBO::getProductRuleConfigDetails)
                .orElse(Collections.emptyList())
                .stream()
                .map(detail -> StringUtils.isNotEmpty(detail.getRuleCode()) ? Long.parseLong(detail.getRuleCode()) : null)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public ProductComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        ProductComponentBO product = JSON.parseObject(componentStr, ProductComponentBO.class);
        if (Objects.nonNull(product.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(product.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(product.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(product.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        //过滤无效配置详情
        List<ProductComponentConfigDetailBO> list = this.queryEffectiveDetails(product.getProductRuleConfigDetails());
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        product.setProductRuleConfigDetails(list);
        //规则生效配置大于1 兜底
        if (DataRuleTypeEnum.RULE.getCode().equals(list.get(0).getRuleType()) && list.size() > 1) {
            ProductComponentConfigDetailBO detail = this.queryClosestDetailByPreview(list);
            if (Objects.isNull(detail)) {
                return null;
            }
            product.setProductRuleConfigDetails(Arrays.asList(detail));
        }
        if (StringUtils.isEmpty(product.getRuleType())){
            String ruleType = product.getProductRuleConfigDetails().get(0).getRuleType();
            product.setRuleType(ruleType);
        }
        return product;
    }
    
    
    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.PRODUCT;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
