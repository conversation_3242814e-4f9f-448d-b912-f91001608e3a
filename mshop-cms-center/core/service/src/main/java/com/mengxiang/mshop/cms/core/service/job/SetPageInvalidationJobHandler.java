package com.mengxiang.mshop.cms.core.service.job;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/4/14
 * @Description: 定时失效页面
 */
@Component
@Slf4j
public class SetPageInvalidationJobHandler {

    @Autowired
    private PageOperateService pageOperateService;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @XxlJob("setPageInvalidationJobHandler")
    public ReturnT<String> setPageInvalidationJobHandler(String params) {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(PageInstanceStatusEnum.PUBLISH.getCode());
        statusList.add(PageInstanceStatusEnum.EXECUTORY.getCode());
        List<MshopPageInstanceDo> instanceDos = mshopPageInstanceDao.queryByStatusList(statusList);
        if (CollectionUtil.isEmpty(instanceDos)) {
            return ReturnT.SUCCESS;
        }
        for (MshopPageInstanceDo page : instanceDos) {
            if (StringUtils.isEmpty(page.getVersion())) {
                continue;
            }
            MshopPageDraftDo mshopPageDraftDo = mshopPageDraftDao.getByPageCode(page.getPageCode(),page.getVersion());
            if (Objects.isNull(mshopPageDraftDo) || StringUtils.isEmpty(mshopPageDraftDo.getTimeConfig())) {
                continue;
            }
            TimeConfigBO timeConfigBO = JSONObject.parseObject(mshopPageDraftDo.getTimeConfig(), TimeConfigBO.class);
            if(Objects.isNull(timeConfigBO)) {
                continue;
            }
            boolean checkRuleTimeByEndTime = TimeConfigUtils.checkRuleTimeByEndTime(timeConfigBO);
            if (!checkRuleTimeByEndTime) {
                pageOperateService.setPageInvalidation(page.getPageCode(),"system","system");
            }
        }
        return ReturnT.SUCCESS;
    }
}
