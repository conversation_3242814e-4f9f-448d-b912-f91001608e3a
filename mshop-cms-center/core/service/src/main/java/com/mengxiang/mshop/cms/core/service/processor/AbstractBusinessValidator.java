package com.mengxiang.mshop.cms.core.service.processor;

import com.mengxiang.base.common.process.model.BusinessContext;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.base.common.process.processor.AbstractBusinessProcessor;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/3/8
 * @Description: 业务校验抽象层
 * @param <C>
 * @param <M>
 */
public abstract class AbstractBusinessValidator<C extends BusinessContext, M extends BusinessModel> extends
        AbstractBusinessProcessor<C, M> {

    /**
     * 业务执行前处理 校验器无逻辑
     *
     * @param context 业务上下文
     * @return 业务执行结果
     */
    @Override
    protected InnerResult<M> beforeProcess(C context) {
        return new InnerResult<M>(Boolean.TRUE, null);
    }

    /**
     * 获取执行器名称
     *
     * @return
     */
    @Override
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 业务执行后处理
     *
     * @param context 业务上下文
     * @return 业务执行结果
     */
    @Override
    protected InnerResult<M> doProcess(C context) {
        InnerResult<BusinessModel> innerResult = this.validator(context);
        if (innerResult.getSuccess() && !context.getNeedInterrupt()) {
            return new InnerResult<>(Boolean.TRUE, null);
        } else {
            return new InnerResult(innerResult.getErrorCode(), CmsProdConstant.APP_ID);
        }
    }

    /**
     * 校验器逻辑 遇到失败则抛出异常
     *
     * @param context 上下文
     * @return
     */
    protected abstract InnerResult<BusinessModel> validator(C context);
}
