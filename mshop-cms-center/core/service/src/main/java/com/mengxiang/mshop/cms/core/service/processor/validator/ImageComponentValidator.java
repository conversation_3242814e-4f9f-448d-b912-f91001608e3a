package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageDraftDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.image.ImageComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImgComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImgComponentHoleConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.TargetType;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 图片组件校验
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImageComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private MshopPageDraftDao mshopPageDraftDao;

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context) {
        //解析组件
        if (CollectionUtils.isEmpty(context.getImageComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        List<ImageComponentBO> images = context.getImageComponents();
        for (ImageComponentBO image : images) {
            //参数校验
            paramVerify(image);
        }
        return new InnerResult<BusinessModel>(true, null);
    }
    
    /**
     * 参数校验
     */
    private void paramVerify(ImageComponentBO image) {
        if (CollectionUtils.isNotEmpty(image.getHotConfigDetails())) {
            Preconditions.checkArgument(image.getHotConfigDetails().size() <= 6, "图片组件 热区配置 不能超过6个");
            for (ImgComponentConfigDetailBO hotConfigDetail : image.getHotConfigDetails()) {
                Preconditions.checkArgument(Objects.nonNull(TargetType.getByType(hotConfigDetail.getTargetType())), "图片组件热区 跳转类型 不支持或不能为空");
                Preconditions.checkArgument(!StringUtils.isEmpty(hotConfigDetail.getTargetId()), "图片组件热区 目标参数 不能为空");
            }
            //校验目标页面
            List<String> pageCodes = image.getHotConfigDetails().stream().filter(c -> TargetType.SHOPMICRO.getType().equals(c.getTargetType()) || TargetType.PAGE.getType().equals(c.getTargetType())).map(x -> x.getTargetId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pageCodes)) {
                pageCodes.stream().forEach(pageCode ->{
                    MshopPageInstanceDo page = mshopPageInstanceDao.getByPageCode(pageCode, null);
                    if (Objects.isNull(page)) {
                        throw new BusinessException("图片组件 目标页面不存在");
                    }
                    MshopPageDraftDo mshopPageDraftDo;
                    if (StringUtils.isEmpty(page.getVersion())) {
                        mshopPageDraftDo = mshopPageDraftDao.getLastByPageCode(page.getPageCode(),null);
                    } else {
                        mshopPageDraftDo = mshopPageDraftDao.getByPageCode(page.getPageCode(),page.getVersion());
                    }
                    if (Objects.nonNull(mshopPageDraftDo) && (StringUtils.isEmpty(page.getVersion()) || !page.getStatus().equals(PageInstanceStatusEnum.PUBLISH.getCode()))) {
                        throw new BusinessException("图片组件 目标页面【"+mshopPageDraftDo.getName()+"】 必须是已发布已生效的页面");
                    }
                });
            }
        }
        if (CollectionUtils.isNotEmpty(image.getHoleConfigDetails())) {
            Preconditions.checkArgument(image.getHoleConfigDetails().size() <= 1, "图片组件 区域挖孔配置 不能超过1个");
            for (ImgComponentHoleConfigDetailBO holeConfigDetail : image.getHoleConfigDetails()) {
                Preconditions.checkArgument(Objects.nonNull(TargetType.getByType(holeConfigDetail.getTargetType())), "图片组件 区域挖孔配置类型 不支持或不能为空");
            }
        }
    }
}
