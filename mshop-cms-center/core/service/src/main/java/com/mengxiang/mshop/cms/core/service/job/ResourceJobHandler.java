package com.mengxiang.mshop.cms.core.service.job;

import com.mengxiang.mshop.cms.core.service.business.ResourceService;
import com.mengxiang.mshop.cms.core.service.business.WorkbenchService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 工作流检查
 * <AUTHOR>
 */
@Component
@Slf4j
public class ResourceJobHandler {


    @Autowired
    private ResourceService resourceService;

    @XxlJob("checkResourceStatusJobHandler")
    public ReturnT<String> checkResourceStatusJobHandler(String params) {
        try {
            log.info("checkResourceStatusJobHandler action");
//            resourceService.checkStatus();
            resourceService.updateNavigationCache(null);
        } catch (Exception e) {
            log.error("checkResourceStatusJobHandler 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }


}
