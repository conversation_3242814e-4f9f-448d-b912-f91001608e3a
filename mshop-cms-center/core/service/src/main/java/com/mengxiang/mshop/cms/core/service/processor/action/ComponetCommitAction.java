package com.mengxiang.mshop.cms.core.service.processor.action;

import cn.hutool.core.collection.CollectionUtil;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 组件提交
 * <AUTHOR>
 */
@Service
public class ComponetCommitAction extends AbstractBusinessAction<PageContext, BusinessModel> {

    @Autowired
    private MshopComponentInstanceDao componentDao;


    @Override
    protected void beforeAction(PageContext context) {

    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        List<MshopComponentInstanceDo> componentDos = context.getComponentInstances();
        if(CollectionUtil.isNotEmpty(componentDos)){
            int res = componentDao.insertBatch(componentDos);
            if (res <= 0) {
                context.setNeedInterrupt(true);
                return new InnerResult<BusinessModel>(CmsErrorCodeEnum.SAVE_PAGE_ERROR.getErrorCode(), "mshop-cms-center");
            }
        }
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
