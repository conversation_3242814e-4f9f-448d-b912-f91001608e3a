package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopResourceComponentDo;
import com.mengxiang.mshop.cms.core.model.domain.resource.*;
import com.mengxiang.mshop.cms.core.model.request.ResourceRequest;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Auther: zhangmoxun
 * @Date: 2023/3/13
 * @Description:
 */
public interface ResourceService {
    /**
     * 分页查询资源位
     * @return
     */
    Pagination<BaseResourceBO> selectPage(ResourceRequest req);
    /**
     * 保存金刚位替换数据
     */
    void saveDiamondResource(DiamondResourceBO bo);
    /**
     * 查询金刚位替换数据
     */
    List<DiamondResourceBO.DiamondResourceConfig> findDiamondResourceList(DiamondResourceBO bo);

    /**
     * 保存banner组件
     */
    void saveBannerResource(BannerResourceBO bo);

    /**
     * 查询banner组件
     */
    BannerResourceBO getBannerResourceById(BannerResourceBO bo);


    /**
     * 保存导航组件
     */
    void saveNavigationResource(NavigationResourceBO bo);

    /**
     * 查询导航组件
     */
    NavigationResourceBO getNavigationResourceById(NavigationResourceBO bo);


    /**
     * 查询平台页面排序值
     */
    NavigationResourceBO getPlatPageDataByTenantInfo(NavigationResourceBO req);

    /**
     * 保存平台页面排序值
     *
     * @return
     */
    void savePlatPageData(NavigationResourceBO req);
    /**
     * 保存开机广告
     */
    void saveStartupAdvertisement(StartupAdvertisementBO bo);

    /**
     * 查询开机广告
     */
    StartupAdvertisementBO getStartupAdvertisementById(StartupAdvertisementBO bo);

    /**
     * 资源位置状态处理
     */
    void updateStatus(BaseResourceBO bo);

    /**
     * 资源位置状态处理
     */
    void deleteById(BaseResourceBO bo);

    /**
     * 处理定时生效
     */
    void checkStatus();


    /**
     * 更新导航缓存
     */
    void updateNavigationCache(String tenantId);
}
