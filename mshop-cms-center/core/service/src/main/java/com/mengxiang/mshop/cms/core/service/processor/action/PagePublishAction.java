package com.mengxiang.mshop.cms.core.service.processor.action;

import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOperateType;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 2023/4/6
 * @Description: 发布页面 且 状态是 立即生效的 去发布页面
 */
@Service
@Slf4j
public class PagePublishAction extends AbstractBusinessAction<PageContext, BusinessModel>  {

    @Autowired
    private PageOperateService pageOperateService;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;


    @Override
    protected void beforeAction(PageContext context) {

    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        SavePageRequest request = context.getRequest();
        MshopPageInstanceDo featurePageInstance = context.getFeaturePageInstance();
        Boolean result = true;
        if(request.getOperateType().equals(PageOperateType.PUBLISH.getCode())) {
            if(featurePageInstance.getStatus().equals(PageInstanceStatusEnum.APPROVAL.getCode())) {
                int res = mshopPageInstanceDao.updateStatusAndVersionByPageCode(featurePageInstance.getPageCode(), featurePageInstance.getStatus(), featurePageInstance.getVersion());
                result = res > 0;
            } else {
                result = pageOperateService.pageCheckPublish(featurePageInstance.getPageCode(),featurePageInstance.getVersion());
            }
        }
        if (!result) {
            context.setNeedInterrupt(true);
            return new InnerResult<BusinessModel>(CmsErrorCodeEnum.SAVE_PAGE_ERROR.getErrorCode(), "mshop-cms-center");
        }
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
