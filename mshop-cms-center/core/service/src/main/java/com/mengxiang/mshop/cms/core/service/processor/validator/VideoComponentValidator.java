package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentBO;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;


/**
 * 视频组件校验
 * <AUTHOR>
 */
@Component
@Slf4j
public class VideoComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {
    
    @Override
    protected InnerResult<BusinessModel> validator(PageContext context) {
        if (CollectionUtils.isEmpty(context.getVideoComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        List<VideoComponentBO> videos = context.getVideoComponents();
        for (VideoComponentBO video : videos) {
            paramVerify(video);
        }
        return new InnerResult<BusinessModel>(true, null);
    }

    private void paramVerify(VideoComponentBO video) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(video.getVideoConfigDetails()), "视频组件 视频配置 不能为空");
        Preconditions.checkArgument(video.getVideoConfigDetails().stream().allMatch(v -> StringUtils.isNotEmpty(v.getUrl())),"视频组件 视频URL不能为空");
        //Preconditions.checkArgument(video.getVideoConfigDetails().stream().allMatch(v -> StringUtils.isNotEmpty(v.getName())),"视频组件 视频名称 不能为空");
        //Preconditions.checkArgument(video.getVideoConfigDetails().stream().allMatch(v -> StringUtils.isNotEmpty(v.getCoverImg())),"视频组件 视频封面 不能为空");
    }
    
}
