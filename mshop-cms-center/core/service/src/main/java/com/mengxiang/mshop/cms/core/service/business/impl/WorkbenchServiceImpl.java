package com.mengxiang.mshop.cms.core.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageWorkbenchDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo;
import com.mengxiang.mshop.cms.common.service.integration.feign.WorkbenchClient;
import com.mengxiang.mshop.cms.core.model.constant.PageConstant;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogActionEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.enums.WorkbenchType;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.*;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.business.OperationLogService;
import com.mengxiang.mshop.cms.core.service.business.WorkbenchService;
import com.mengxiang.mshop.cms.core.service.enums.ProcessStatusEnum;
import com.mengxiang.mshop.cms.core.service.util.SerialNoUtil;
import com.mengxiang.workbench.service.facade.common.enums.ButtonTypeV2Enum;
import com.mengxiang.workbench.service.facade.common.request.EventForm;
import com.mengxiang.workbench.service.facade.common.request.msg.WxMsgSendReq;
import com.mengxiang.workbench.service.facade.common.request.proc.ProcNoFlowReq;
import com.mengxiang.workbench.service.facade.common.request.proc.ProcVariable;
import com.mengxiang.workbench.service.facade.common.request.proc.TaskOperationBaseReq;
import com.mengxiang.workbench.service.facade.common.response.proc.TaskCompleteResp;
import com.netflix.servo.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Service
@Slf4j
public class WorkbenchServiceImpl implements WorkbenchService {
    @Autowired
    private MshopPageWorkbenchDao mshopPageWorkbenchDao;
    @Autowired
    private WorkbenchClient workbenchClient;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private StringRedisTemplate redisTemplate;


    //流程key
    @Value("${mshop-cms-center.workbench.procKey:proc_member_meeting_v3}")
    private String procKey;

    //商家会场审核
    @Value("${mshop-cms-center.workbench.merchantProcKey:proc_member_merchant_meeting}")
    private String merchantProcKey;

    @Value("${mshop-cms-center.workbench.notice:-48}")
    private Integer notice;

    @Value("${mshop-cms-center.workbench.cancel:-120}")
    private Integer cancel;

    /**
     * 流程回调通知
     * {"businessKey":"1656119685687984129","buttonControl":"AGREE","createBy":"AK005300","createName":"段艳飞","endStatus":true,"nextKeyList":["100"],"procInstCode":"20230510000001","reqBody":{"remark":"同意"},"taskId":"1113180","taskKey":"111"}
     * {"businessKey":"1656119685687984129","buttonControl":"REJECT","createBy":"AK005300","createName":"段艳飞","endStatus":true,"nextKeyList":["100"],"procInstCode":"20230510000001","reqBody":{"remark":"同意"},"taskId":"1113180","taskKey":"111"}
     *
     * @param req
     */
    @Override
    public void callBack(WorkflowCallBackRequest req) {

        log.info("callBack,businessKey={},req={}", req.getBusinessKey(), JSON.toJSONString(req));

        String businessKey = req.getBusinessKey();
        HashMap<String, Object> reqBody = req.getReqBody();

        String pageCode = (String) reqBody.get("pageCode");
        String version = (String) reqBody.get("version");

        MshopPageWorkbenchDo mpwb = mshopPageWorkbenchDao.selectOneByBusinessKey(businessKey, pageCode, version);
        if (Objects.isNull(mpwb)) {
            //幂等处理
            log.warn("selectOneByBusinessKey callBack,businessKey={},updateByBusinessKey,req={}", req.getBusinessKey(), JSON.toJSONString(req));
            return;
        }
        OperationLogSaveRequest logSaveRequest = new OperationLogSaveRequest();
        //结束标记
        boolean endStatus = req.isEndStatus();
        // AGREE  REJECT
        String buttonControl = req.getButtonControl();
        String taskKey = req.getTaskKey();
        if (endStatus && ButtonTypeV2Enum.AGREE.getKey().equals(buttonControl)) {
            mpwb.setButtonControl(buttonControl);
            mpwb.setCurrentTaskKey(taskKey);
            mpwb.setProcessStatus(ProcessStatusEnum.AUDIT_SUCCESS.getCode());
            //更新 工作流表 操作信息
            int row = mshopPageWorkbenchDao.updateByPrimaryKeySelective(mpwb);

            if (row == 0) {
                log.error("agree callBack,businessKey={},updateByBusinessKey fail,req={}", req.getBusinessKey(), JSON.toJSONString(req));
            }
            logSaveRequest.setAction(OperationLogActionEnum.AGREE.getCode());
            logSaveRequest.setRemark(OperationLogActionEnum.EDIT.getDesc());
        }
        if (ButtonTypeV2Enum.REJECT.getKey().equals(buttonControl)) {
            mpwb.setButtonControl(buttonControl);
            mpwb.setCurrentTaskKey(taskKey);
            mpwb.setProcessStatus(ProcessStatusEnum.AUDIT_FAIL.getCode());
            //更新 工作流表 操作信息
            int row = mshopPageWorkbenchDao.updateByPrimaryKeySelective(mpwb);

            if (row == 0) {
                log.error("reject callBack,businessKey={},updateByBusinessKey fail,req={}", req.getBusinessKey(), JSON.toJSONString(req));
            }
            logSaveRequest.setAction(OperationLogActionEnum.REJECT.getCode());
            logSaveRequest.setRemark(OperationLogActionEnum.EDIT.getDesc());
        }
        //记录日志
        logSaveRequest.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
        logSaveRequest.setBizType(1);
        logSaveRequest.setBizCode(pageCode);
        logSaveRequest.setCreateBy(req.getCreateName());
        logSaveRequest.setCreateUserId(req.getCreateBy());
        operationLogService.saveOperationLog(logSaveRequest);

    }

    @Transactional
    @Override
    public Result<Void> createProcNo(SaveProcRequest req) {
        log.info("createProcNo,req={}", JSON.toJSONString(req));
        String pageCode = req.getPageCode();
        String version = req.getVersion();
        List<MshopPageWorkbenchDo> wblist = mshopPageWorkbenchDao.selectListByProcessStatus(pageCode, version, ProcessStatusEnum.AUDITING.getCode());
        if (!CollectionUtils.isEmpty(wblist)) {
            //已经存在工作流
            return Result.error("工作流已经存在，不能重复发起");
        }
        //业务key
        String businessKey = "MP" + SerialNoUtil.getBusinessKey();

        ProcNoFlowReq flowReq = new ProcNoFlowReq();
        Map<String, Object> optionalParamMap = new HashMap<>();
        WorkflowInfoVO workflowInfoVO = new WorkflowInfoVO();
        String workbenchType = WorkbenchType.checkWorkBenchByPage(req.getOwnerType(), req.getType());
        //判断工作流key
        if (org.apache.commons.lang3.StringUtils.isNotBlank(workbenchType)
                && workbenchType.equals(WorkbenchType.MENGXIANG_MARKET.getCode())) {
            flowReq.setProcKey(procKey);
            //其他选项参数 创建人员上级信息
            // 发起的时候需要传两个值  firstAudit  待审核人  submitter  提交人（用户驳回之后的重新提交）
            // 传入的人都要工号   AK开头的
            optionalParamMap.put("firstAudit", req.getManagerNoDirect());
            optionalParamMap.put("submitter", req.getUserCode());
            flowReq.setUserId(req.getUserId());
            flowReq.setUserName(req.getUserName());
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(workbenchType)
                && workbenchType.equals(WorkbenchType.SUPPLIER_MARKET.getCode())) {
            flowReq.setProcKey(merchantProcKey);
            //设置默认
            req.getAuditList().add("system");
            //参数 bdAudit，值是工号，多个人用,拼接
            optionalParamMap.put("bdAudit", String.join(",", req.getAuditList()));
            optionalParamMap.put("merchant", req.getOwnerId());
            flowReq.setUserId(req.getOwnerId());
            flowReq.setUserName(org.apache.commons.lang3.StringUtils.isNotEmpty(req.getShopName()) ? req.getShopName() : req.getOwnerId());
        }
        workflowInfoVO.setWorkbenchType(workbenchType);
        workflowInfoVO.setOptionalParamMap(optionalParamMap);
        workflowInfoVO.setUserId(flowReq.getUserId());
        workflowInfoVO.setUserName(flowReq.getUserName());
        workflowInfoVO.setCategoryLeaderList(req.getCategoryLeaderList());

        //流程节点变更的表单内容;业务需要展示的核心4个字段
        EventForm eventForm = new EventForm();
        eventForm.setKey1("页面编号");
        eventForm.setValue1(req.getPageCode());
        eventForm.setKey2("版本号");
        eventForm.setValue2(req.getVersion());
        eventForm.setKey3("会场名称");
        eventForm.setValue3(req.getName());
        eventForm.setKey4("创建人");
        eventForm.setValue4(req.getUserName());
        flowReq.setEventForm(eventForm);
        flowReq.setBusinessKey(businessKey);


        flowReq.setOptionalParamMap(optionalParamMap);

        TaskCompleteResp resp = workbenchClient.createProcNo(flowReq);
        MshopPageWorkbenchDo wbdo = new MshopPageWorkbenchDo();
        wbdo.setBusinessKey(businessKey);
        wbdo.setPageCode(req.getPageCode());
        wbdo.setVersion(req.getVersion());
        wbdo.setProcessStatus(ProcessStatusEnum.AUDITING.getCode());
        wbdo.setProcInstCode(resp.getProcInstCode());

        wbdo.setCreateBy(req.getUserName());
        wbdo.setCreateUserId(req.getUserId());
        //保存工作流业务信息
        wbdo.setFlowInfo(JSON.toJSONString(workflowInfoVO));
        int res = mshopPageWorkbenchDao.insert(wbdo);
        return res == 1 ? Result.success() : Result.error();
    }

    /**
     * 驳回重新开始
     */
    @Override
    @Transactional
    public Result<Void> rejectReStart(ReStartProcRequest req) {
        log.info("rejectReStart,req={}", JSON.toJSONString(req));
        String businessKey = req.getBusinessKey();
        String pageCode = req.getPageCode();

        List<MshopPageWorkbenchDo> wblist = mshopPageWorkbenchDao.selectListByBusinessKeyAndPageCode(businessKey, pageCode);
        if (CollectionUtils.isEmpty(wblist)) {
            return Result.error("工作流不存在");
        }
        //审核不通过的流程
        List<MshopPageWorkbenchDo> failList = wblist.stream().filter(i -> i.getProcessStatus().equals(ProcessStatusEnum.AUDIT_FAIL.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(failList)) {
            return Result.error("工作流没有驳回，不能重新开始流程");
        }
        MshopPageWorkbenchDo oldwb = wblist.get(0);
        //组装重新发起工作流对象
        TaskOperationBaseReq taskOperationBaseReq = new TaskOperationBaseReq();
        taskOperationBaseReq.setProcInstCode(oldwb.getProcInstCode());
        //待审核状态 为 10   整改  状态为 55
        taskOperationBaseReq.setTaskKey("55");

        //组装流程变量
        ProcVariable procVariable = new ProcVariable();
        //按钮类型
        procVariable.setButtonType(ButtonTypeV2Enum.AGREE.getKey());
        procVariable.setCreateBy(req.getUserCode());

        Map<String, Object> optionalParamMap = buildOptionalParamMap(req);

        procVariable.setOptionalParamMap(optionalParamMap);

        taskOperationBaseReq.setProcVariable(procVariable);
        taskOperationBaseReq.setUserId(req.getUserId());
        taskOperationBaseReq.setUserName(req.getUserName());

        WorkflowInfoVO vo = JSON.parseObject(oldwb.getFlowInfo(), WorkflowInfoVO.class);
        //更新审批人信息
        if (Objects.nonNull(vo) && Objects.equals(vo.getWorkbenchType(), WorkbenchType.SUPPLIER_MARKET.getCode())) {
            if(Objects.nonNull(vo.getOptionalParamMap().get("merchant"))){
                String mCode = vo.getOptionalParamMap().get("merchant").toString();
                taskOperationBaseReq.getProcVariable().setCreateBy(mCode);
                taskOperationBaseReq.setUserId(vo.getUserId());
                taskOperationBaseReq.setUserName(vo.getUserName());
                optionalParamMap.put("merchant", mCode);
                //更新类目长
                vo.setCategoryLeaderList(req.getCategoryLeaderList());
            }
        }

        TaskCompleteResp resp = workbenchClient.completeTask(taskOperationBaseReq);

        MshopPageWorkbenchDo wbdo = new MshopPageWorkbenchDo();
        wbdo.setBusinessKey(businessKey);
        wbdo.setPageCode(req.getPageCode());
        wbdo.setVersion(req.getVersion());
        wbdo.setProcessStatus(ProcessStatusEnum.AUDITING.getCode());
        wbdo.setProcInstCode(resp.getProcInstCode());
        wbdo.setCreateBy(req.getUserName());
        wbdo.setCreateUserId(req.getUserId());
        vo.setOptionalParamMap(optionalParamMap);
        wbdo.setFlowInfo(JSON.toJSONString(vo));
        //保存新版本的流程记录
        int res = mshopPageWorkbenchDao.insert(wbdo);

        return res == 1 ? Result.success() : Result.error();
    }

    @Override
    public WorkflowInfoResp findWorkflowInfo(WorkflowInfoRequest req) {

        String businessKey = req.getBusinessKey();
        if (StringUtils.isBlank(businessKey)) {
            throw new BusinessException("业务key不能为空");
        }
        List<MshopPageWorkbenchDo> wblist = mshopPageWorkbenchDao.selectListByBusinessKey(businessKey);

        MshopPageWorkbenchDo wbdo = wblist.get(0);

        WorkflowInfoResp resp = new WorkflowInfoResp();
        resp.setPageCode(wbdo.getPageCode());
        resp.setVersion(wbdo.getVersion());
        resp.setBusinessKey(wbdo.getBusinessKey());
        resp.setProcInstCode(wbdo.getProcInstCode());

        return resp;
    }

    @Override
    public ProcessStatusResp findProcessStatus(ProcessStatusRequest req) {
        String pageCode = req.getPageCode();
        String version = req.getVersion();

        if (StringUtils.isBlank(pageCode)) {
            throw new BusinessException("pageCode不能为空");
        }
        if (StringUtils.isBlank(version)) {
            throw new BusinessException("version不能为空");
        }
        List<MshopPageWorkbenchDo> wblist = mshopPageWorkbenchDao.selectListByPageCodeAndVersion(pageCode, version);
        if (CollectionUtils.isEmpty(wblist)) {
            return null;
        }
        MshopPageWorkbenchDo wbdo = wblist.get(0);

        ProcessStatusResp resp = new ProcessStatusResp();
        resp.setPageCode(wbdo.getPageCode());
        resp.setVersion(wbdo.getVersion());
        resp.setBusinessKey(wbdo.getBusinessKey());
        resp.setProcInstCode(wbdo.getProcInstCode());
        resp.setProcessStatus(wbdo.getProcessStatus());
        resp.setProcessStatusName(ProcessStatusEnum.getDescBycode(wbdo.getProcessStatus()));
        return resp;

    }

    private Map<String, Object> buildOptionalParamMap(ReStartProcRequest req) {
        Map<String, Object> optionalParamMap = Maps.newHashMap();
        String workbenchType = WorkbenchType.checkWorkBenchByPage(req.getOwnerType(), req.getType());
        //判断工作流key
        if (org.apache.commons.lang3.StringUtils.isNotBlank(workbenchType)
                && workbenchType.equals(WorkbenchType.MENGXIANG_MARKET.getCode())) {
            optionalParamMap.put("firstAudit", req.getManagerNoDirect());
            optionalParamMap.put("submitter", req.getUserCode());
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(workbenchType)
                && workbenchType.equals(WorkbenchType.SUPPLIER_MARKET.getCode())) {
            //设置默认
            req.getAuditList().add("system");
            //参数 bdAudit，值是工号，多个人用,拼接
            optionalParamMap.put("bdAudit", String.join(",", req.getAuditList()));
        }
        return optionalParamMap;
    }


    /**
     * 审批超时通知
     */
    @Override
    public void notice() {
        //查询哪些页面超过48小时没审批
        List<MshopPageWorkbenchDo> list = mshopPageWorkbenchDao.selectListByTime(DateUtil.getLastHour(notice));
        for (MshopPageWorkbenchDo workbenchDo : list) {
            WorkflowInfoVO vo = JSON.parseObject(workbenchDo.getFlowInfo(), WorkflowInfoVO.class);
            if (Objects.isNull(vo)) {
                continue;
            }

            String flag = redisTemplate.opsForValue().get(PageConstant.WORK_BENCH_NOTICE_FLAG + workbenchDo.getProcInstCode());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(flag)) {
                continue;
            }
            try {
                //发起通知
                WxMsgSendReq wxMsgSendReq = new WxMsgSendReq();
                wxMsgSendReq.setTmpCode("SJ2023102601");

                Set<String> notifier = Arrays.stream(vo.getOptionalParamMap().get("bdAudit").toString().split(",")).collect(Collectors.toSet());
                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getCategoryLeaderList())){
                    notifier.addAll(vo.getCategoryLeaderList());
                }
                wxMsgSendReq.setNotifier(notifier);
                LinkedHashMap<String, Object> params = new LinkedHashMap<>();
                params.put("notifyTime", DateUtil.dateToStrLong(new Date()));
                params.put("procInstCode", workbenchDo.getProcInstCode());
                params.put("taskName", "待审批");
                params.put("submitTime", DateUtil.dateToStrLong(workbenchDo.getUpdateTime()));
                params.put("remark", "请尽快审批商家会场");
                wxMsgSendReq.setParams(params);
                workbenchClient.wxMsgSend(wxMsgSendReq);
                redisTemplate.opsForValue().set(PageConstant.WORK_BENCH_NOTICE_FLAG + workbenchDo.getProcInstCode(), workbenchDo.getProcInstCode(), 120, TimeUnit.HOURS);
            } catch (Exception ex) {
                log.warn("wxMsgSend warn req:{}", JSON.toJSONString(workbenchDo), ex);
            }
        }

    }

    /**
     * 审批超时通知
     */
    @Override
    public void cancel() {
        //查询哪些页面超过48+72小时没审批
        List<MshopPageWorkbenchDo> list = mshopPageWorkbenchDao.selectListByTime(DateUtil.getLastHour(cancel));
        for (MshopPageWorkbenchDo workbenchDo : list) {
            WorkflowInfoVO vo = JSON.parseObject(workbenchDo.getFlowInfo(), WorkflowInfoVO.class);
            if (Objects.isNull(vo)) {
                continue;
            }
            try {
                //驳回流程
                TaskOperationBaseReq taskOperationBaseReq = new TaskOperationBaseReq();
                taskOperationBaseReq.setProcInstCode(workbenchDo.getProcInstCode());
                ProcVariable procVariable = new ProcVariable();
                procVariable.setButtonType("REJECT");
                procVariable.setCreateBy("system");
                taskOperationBaseReq.setProcVariable(procVariable);
                taskOperationBaseReq.setRemark("系统自动驳回");
                taskOperationBaseReq.setTaskKey("10");
                taskOperationBaseReq.setUserId("system");
                taskOperationBaseReq.setUserName("系统审核");
                workbenchClient.completeTask(taskOperationBaseReq);

                workbenchDo.setButtonControl("REJECT");
                workbenchDo.setCurrentTaskKey("10");
                workbenchDo.setProcessStatus(ProcessStatusEnum.AUDIT_FAIL.getCode());
                //更新 工作流表 操作信息
                int row = mshopPageWorkbenchDao.updateByPrimaryKeySelective(workbenchDo);

                if (row == 0) {
                    log.error("reject callBack,updateByBusinessKey fail,req={}", JSON.toJSONString(workbenchDo));
                }
                OperationLogSaveRequest logSaveRequest = new OperationLogSaveRequest();
                logSaveRequest.setAction(OperationLogActionEnum.REJECT.getCode());
                logSaveRequest.setRemark(OperationLogActionEnum.EDIT.getDesc());

                //记录日志
                logSaveRequest.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
                logSaveRequest.setBizType(1);
                logSaveRequest.setBizCode(workbenchDo.getPageCode());
                logSaveRequest.setCreateBy("system");
                logSaveRequest.setCreateUserId("system");
                operationLogService.saveOperationLog(logSaveRequest);

                //发起通知
                WxMsgSendReq wxMsgSendReq = new WxMsgSendReq();
                wxMsgSendReq.setTmpCode("SJ2023102602");
                wxMsgSendReq.setNotifier(Arrays.stream(vo.getOptionalParamMap().get("bdAudit").toString().split(",")).collect(Collectors.toSet()));
                LinkedHashMap<String, Object> params = new LinkedHashMap<>();
                params.put("notifyTime", DateUtil.dateToStrLong(new Date()));
                params.put("procInstCode", workbenchDo.getProcInstCode());
                params.put("taskName", "驳回");
                params.put("submitTime", DateUtil.dateToStrLong(workbenchDo.getUpdateTime()));
                params.put("remark", "系统自动驳回");
                wxMsgSendReq.setParams(params);
                workbenchClient.wxMsgSend(wxMsgSendReq);
            } catch (Exception ex) {
                log.warn("wxMsgSend warn req:{}", JSON.toJSONString(workbenchDo), ex);
            }
        }

    }
}
