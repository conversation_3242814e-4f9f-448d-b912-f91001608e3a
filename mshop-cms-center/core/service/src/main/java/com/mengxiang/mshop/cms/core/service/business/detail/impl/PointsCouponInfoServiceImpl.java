package com.mengxiang.mshop.cms.core.service.business.detail.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.CouponComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.PointsCouponComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/4
 * @Description:
 */
@Service
public class PointsCouponInfoServiceImpl extends AbstractComponentInfoService<PointsCouponComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;

    @Override
    protected PointsCouponComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        PointsCouponComponentBO coupon = JSON.parseObject(metaConfig, PointsCouponComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),coupon);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, CouponComponentConfigDetailBO.class))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .ifPresent(coupon::setCouponConfigDetails);
        if (CollectionUtil.isNotEmpty(coupon.getCouponConfigDetails())) {
            coupon.getCouponConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return coupon;
    }

    @Override
    protected PointsCouponComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        PointsCouponComponentBO coupon = JSON.parseObject(metaConfig, PointsCouponComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),coupon);
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(coupon.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(coupon.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, CouponComponentConfigDetailBO.class))
                        .collect(Collectors.toList()))
                .ifPresent(coupon::setCouponConfigDetails);
        if (CollectionUtil.isNotEmpty(coupon.getCouponConfigDetails())) {
            coupon.getCouponConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return coupon;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Collections.emptyList();
    }

    @Override
    public PointsCouponComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        PointsCouponComponentBO coupon = JSON.parseObject(componentStr, PointsCouponComponentBO.class);
        if (Objects.nonNull(coupon.getTimeConfig())) {
            boolean isValid = TimeConfigUtils.checkRuleTime(coupon.getTimeConfig());
            if (!isValid) {
                return null;
            }
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(coupon.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(coupon.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        if (CollectionUtil.isNotEmpty(coupon.getCouponConfigDetails())) {
            coupon.getCouponConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return coupon;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.POINTSCOUPON;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Collections.emptyList();
    }
}
