package com.mengxiang.mshop.cms.core.service.business.detail;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.base.*;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbstractComponentInfoService<T extends ComponentBO> {

    /**
     * 获取原样组件信息
     */
    public String getComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        return Optional.ofNullable(component)
                .map(c -> {
                    T componentBo = doGetComponent(c, componentDetail);
                    if (Objects.isNull(componentBo)) {
                        return null;
                    }
                    if (StringUtils.isNotEmpty(c.getUseRule())) {
                        componentBo.setAuthorizationList(Arrays.asList(c.getUseRule().split(",")));
                    }
                    if (StringUtils.isNotEmpty(c.getTimeConfig())) {
                        TimeConfigBO timeConfigBO = JSON.parseObject(c.getTimeConfig(), TimeConfigBO.class);
                        componentBo.setTimeConfig(timeConfigBO);
                    }
                    return componentBo;
                }).filter(Objects::nonNull).map(t -> {
                    t.setComponentCode(component.getComponentCode());
                    t.setOrder(component.getOrderValue());
                    t.setType(ComponentTypeEnum.getEnumByCode(component.getType()));
                    return JSON.toJSONString(t);
                }).orElse("");
    }

    /**
     * 预览组件 不包含过滤无效组件, 包含用户分群过滤
     */
    public String getComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        return Optional.ofNullable(component)
                .map(c -> {
                    T componentBo = doGetComponentByPreview(c, componentDetail,aggrBaseReqModule);
                    if (Objects.isNull(componentBo)) {
                        return null;
                    }
                    if (StringUtils.isNotEmpty(c.getUseRule())) {
                        componentBo.setAuthorizationList(Arrays.asList(c.getUseRule().split(",")));
                    }
                    if (StringUtils.isNotEmpty(c.getTimeConfig())) {
                        TimeConfigBO timeConfigBO = JSON.parseObject(c.getTimeConfig(), TimeConfigBO.class);
                        componentBo.setTimeConfig(timeConfigBO);
                    }
                    return componentBo;
                }).filter(Objects::nonNull).map(t -> {
                    t.setComponentCode(component.getComponentCode());
                    t.setOrder(component.getOrderValue());
                    t.setType(ComponentTypeEnum.getEnumByCode(component.getType()));
                    return JSON.toJSONString(t);
                }).orElse("");
    }


    /**
     * 预览组件信息
     * @param component
     * @param componentDetail
     * @param aggrBaseReqModule
     * @return
     */
    protected abstract T doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule);

    /**
     * 组件原信息赋值
     * @param component
     * @param componentDetail
     * @return
     */
    protected abstract T doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail);

    /**
     * 组件解析 包含过滤无效组件 支持用户分群
     */
    public <T> String componentParse(String componentStr,AggrBaseReqModule aggrBaseReqModule) {
        if(StringUtils.isEmpty(componentStr)) {
            return "";
        }
        T t = (T) doComponentParse(componentStr,aggrBaseReqModule);
        if(t==null){
             return "";
        }
        return JSON.toJSONString(t);
    }

    /**
     * 获取选品中心规则ID
     * @param componentStr
     * @return
     */
    public abstract List<Long> doGetRuleIds(String componentStr);

    /**
     * 转换组件
     * @param componentStr
     * @return
     */
    public abstract T doComponentParse(String componentStr,AggrBaseReqModule aggrBaseReqModule);
    
    /**
     * 获取类型
     */
    public abstract ComponentTypeEnum getComponentType();


    public <D extends ComponentDetailBO> D detailBase(MshopComponentInstanceDetailDo req, Class<D> transfer) {
        return Optional.ofNullable(req)
                .map(r -> JSON.parseObject(r.getConfigDetail(), transfer))
                .map(t -> {
                    t.setComponentDetailCode(req.getConfigDetailCode());
                    t.setOrder(req.getOrderValue());
                    if (StringUtils.isNotEmpty(req.getTimeConfig())) {
                        TimeConfigBO timeConfigBO = JSON.parseObject(req.getTimeConfig(), TimeConfigBO.class);
                        t.setTimeConfig(timeConfigBO);
                    }
                    return t;
                }).orElse(null);
    }

    /**
     * 获取有效的配置详情
     * @param details
     * @param <D>
     * @return
     */
    public <D extends ComponentDetailBO> List<D> queryEffectiveDetails(List<D> details) {
        if (CollectionUtil.isEmpty(details)) {
            return new ArrayList<>();
        }
        return details.stream()
                .filter(detail -> TimeConfigUtils.checkRuleTime(detail.getTimeConfig()))
                .sorted(Comparator.comparing(D::getOrder))
                .collect(Collectors.toList());
    }

    /**
     * 预览模式 获取最近配置详情
     * @param details
     * @param <D>
     * @return
     */
    public <D extends ComponentDetailBO> D queryClosestDetailByPreview(List<D> details) {
        if (CollectionUtil.isEmpty(details)) {
            return null;
        }
        Date currentTime = new Date();
        D closestComponentDetail = null;
        Date closestStartTime = null;
        for (D componentDetail : details) {
            TimeConfigBO timeConfig = componentDetail.getTimeConfig();
            List<TimeSlotBO> timeList = timeConfig.getTimeList();
            for (TimeSlotBO timeSlot : timeList) {
                Date startTime = DateUtil.parseDate(timeSlot.getStartTime());
                if (closestStartTime == null || Math.abs(startTime.getTime() - currentTime.getTime()) < Math.abs(closestStartTime.getTime() - currentTime.getTime())) {
                    closestComponentDetail = componentDetail;
                    closestStartTime = startTime;
                }
            }
        }
        return closestComponentDetail;
    }

    public <T extends ComponentBO> void setDirectUserGroup (String directUserGroup,T component) {
        if (StringUtils.isNotEmpty(directUserGroup)) {
            DirectUserGroupBO directUserGroupBO = JSON.parseObject(directUserGroup, DirectUserGroupBO.class);
            component.setDirectUserGroup(directUserGroupBO);
        }
    }

    /**
     * 获取未过期的配置详情
     * @param details
     * @param <D>
     * @return
     */
    public <D extends ComponentDetailBO> List<D> queryNotExpiredDetails(List<D> details) {
        if (CollectionUtil.isEmpty(details)) {
            return new ArrayList<>();
        }
        return details.stream()
                .filter(detail -> TimeConfigUtils.checkRuleTimeByEndTime(detail.getTimeConfig()))
                .sorted(Comparator.comparing(D::getOrder))
                .collect(Collectors.toList());
    }

    /**
     * 获取组件详情 （todo 目前只写了type为活动的，其他的type如果需要这个详情，需要去实现类里写，现在其他的type默认返回的是空list）
     * @param componentStr
     * @return
     */
    public abstract List<ComponentDetailBO> doGetComponentDetail(String componentStr);
}
