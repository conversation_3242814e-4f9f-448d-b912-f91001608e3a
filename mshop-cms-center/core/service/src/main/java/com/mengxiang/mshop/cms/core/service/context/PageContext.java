package com.mengxiang.mshop.cms.core.service.context;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageDraftDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.banner.BannerComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.CouponComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.coupon.PointsCouponComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.cube.CubeComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.image.ImageComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.material.MaterialComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.navigation.NavigationComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.AllProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.product.ProductComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.profit.ProfitAdditionalComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.promotion.UserIncentiveComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.seckill.SeckillComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.text.SeparatorComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.text.TextComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.toplist.TopListComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.video.VideoComponentBO;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description: 微页面上下文
 */
@Data
@Slf4j
public class PageContext extends CmsContext<SavePageRequest> {
    private Map<Class<? extends ComponentBO>, Function<PageContext, List<? extends ComponentBO>>> componentMap = new HashMap<>();

    /**
     * 页面版本号
     */
    private String featureVersion;

    /**
     * 当前DB页面
     */
    private MshopPageInstanceDo currentPage;

    /**
     * 页面信息
     */
    private MshopPageInstanceDo featurePageInstance;

    /**
     * 原页面草稿信息
     */
    private MshopPageDraftDo currentPageDraft;

    /**
     * 页面草稿信息
     */
    private MshopPageDraftDo featurePageDraft;
    
    @ApiModelProperty(value = "banner组件")
    private List<BannerComponentBO> bannerComponents;

    @ApiModelProperty(value = "图片组件")
    private List<ImageComponentBO> imageComponents;

    @ApiModelProperty(value = "视频组件")
    private List<VideoComponentBO> videoComponents;

    @ApiModelProperty(value = "导航组件")
    private List<NavigationComponentBO> navigationComponents;

    @ApiModelProperty(value = "商品组件")
    private List<ProductComponentBO> productComponents;

    @ApiModelProperty(value = "档期活动组件")
    private List<ActivityComponentBO> activityComponents;

    @ApiModelProperty(value = "优惠券组件")
    private List<CouponComponentBO> couponComponents;

    @ApiModelProperty(value = "积分优惠券组件")
    private List<PointsCouponComponentBO> pointsCouponComponents;

    @ApiModelProperty(value = "高佣组件")
    private List<ProfitAdditionalComponentBO> profitComponents;

    @ApiModelProperty(value = "分隔符组件")
    private List<SeparatorComponentBO> separatorComponents;

    @ApiModelProperty(value = "榜单组件")
    private List<TopListComponentBO> topListComponents;

    @ApiModelProperty(value = "秒杀组件")
    private List<SeckillComponentBO> seckillComponents;

    @ApiModelProperty(value = "图片魔方组件")
    private List<CubeComponentBO> cubeComponents;

    @ApiModelProperty(value = "文本组件")
    private List<TextComponentBO> textComponents;

    @ApiModelProperty(value = "素材组件")
    private List<MaterialComponentBO> materialComponents;

    @ApiModelProperty(value = "全部商品组件")
    private List<AllProductComponentBO> allProductComponents;

    @ApiModelProperty(value = "爱豆激励组件")
    private List<UserIncentiveComponentBO> userIncentiveComponents;

    @ApiModelProperty(value = "组件")
    private List<MshopComponentInstanceDo> componentInstances = new ArrayList<>();

    @ApiModelProperty(value = "组件详情")
    private List<MshopComponentInstanceDetailDo> componentInstanceDetails = new ArrayList<>();

    @ApiModelProperty(value = "安全校验内容")
    private List<ContentCheckResponse> contentCheck = new ArrayList<>();

    @ApiModelProperty(value = "定位导航 定位组件MAP , key为前端ID ,新增的")
    private Map<String,MshopComponentInstanceDo> archorIdMap = new HashMap<>();

    @ApiModelProperty(value = "定位导航 定位组件MAP, key为组件code,修改的")
    private Map<String,MshopComponentInstanceDo> archorCodeMap = new HashMap<>();

    @ApiModelProperty(value = "审核流操作类型 0:不操作 1:创建审核流 2:重启原审核流")
    private Integer workBenchType;

    @ApiModelProperty(value = "审核流业务key")
    private String businessKey;
}
