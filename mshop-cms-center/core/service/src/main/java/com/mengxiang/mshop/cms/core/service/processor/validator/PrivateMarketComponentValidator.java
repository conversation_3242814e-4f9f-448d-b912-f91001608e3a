package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentBO;
import com.mengxiang.mshop.cms.core.model.enums.MarketTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * banner 组件校验
 * <AUTHOR>
 */
@Component
public class PrivateMarketComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {

    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        SavePageRequest request = context.getRequest();

        //如果是私密会场，必须添加档期活动
        MarketTypeEnum marketType = request.getMarketType();
        if (Objects.nonNull(marketType) && MarketTypeEnum.PRIVATE.equals(marketType)) {
            List<ActivityComponentBO> activityComponents = context.getActivityComponents();
            if (CollectionUtils.isEmpty(activityComponents)) {
                throw new BusinessException(CmsErrorCodeEnum.PAGE_PRIVATE_COMPONENT_SCHEDULE_EMPTY_ERROR.getErrorMsg());
            }
        }
        return new InnerResult<BusinessModel>(true, null);
    }
}
