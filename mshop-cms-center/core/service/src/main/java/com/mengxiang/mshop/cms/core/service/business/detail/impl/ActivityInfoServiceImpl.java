package com.mengxiang.mshop.cms.core.service.business.detail.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentBO;
import com.mengxiang.mshop.cms.core.model.domain.activity.ActivityComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.model.domain.base.ComponentDetailBO;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.DataRuleTypeEnum;
import com.mengxiang.mshop.cms.core.model.request.AggrBaseReqModule;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.business.DirectUserGroupService;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.service.util.TimeConfigUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Activity 组件 ActivityComponentBO.class
 * <AUTHOR>
 */
@Service
public class ActivityInfoServiceImpl extends AbstractComponentInfoService<ActivityComponentBO> {

    @Autowired
    private DirectUserGroupService directUserGroupService;
    
    @Override
    public ActivityComponentBO doGetComponent(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail) {
        String metaConfig = component.getMetaConfig();
        ActivityComponentBO activity = JSON.parseObject(metaConfig, ActivityComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),activity);
        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> {
                            ActivityComponentConfigDetailBO detailBO = detailBase(detailDo, ActivityComponentConfigDetailBO.class);
                            if (Objects.nonNull(detailBO.getTimeConfig())) {
                                Integer ruleStatus = TimeConfigUtils.findRuleStatus(detailBO.getTimeConfig());
                                detailBO.setRuleStatus(ruleStatus);
                                String ruleCreateTime = DateUtil.dateToStrLong(detailDo.getCreateTime());
                                detailBO.setRuleCreateTime(ruleCreateTime);
                            }
                            return detailBO;
                        })
                        .collect(Collectors.toList()))
                .ifPresent(activity::setActivityRuleConfigDetails);
        if (CollectionUtil.isNotEmpty(activity.getActivityRuleConfigDetails())) {
            activity.getActivityRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return activity;
    }

    @Override
    public ActivityComponentBO doGetComponentByPreview(MshopComponentInstanceDo component, List<MshopComponentInstanceDetailDo> componentDetail, AggrBaseReqModule aggrBaseReqModule) {
        String metaConfig = component.getMetaConfig();
        ActivityComponentBO activity = JSON.parseObject(metaConfig, ActivityComponentBO.class);
        setDirectUserGroup(component.getDirectUserGroup(),activity);

        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(activity.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(activity.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }

        Optional.ofNullable(componentDetail)
                .filter(CollectionUtil::isNotEmpty)
                .map(details -> details.stream()
                        .map(detailDo -> detailBase(detailDo, ActivityComponentConfigDetailBO.class))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .ifPresent(activity::setActivityRuleConfigDetails);
        if (CollectionUtil.isNotEmpty(activity.getActivityRuleConfigDetails())) {
            //查DB 人群不为空, 端上预览
            if (Objects.nonNull(aggrBaseReqModule)) {
                activity = doPreview(activity);
            }
            if (StringUtils.isEmpty(activity.getRuleType())){
                String ruleType = activity.getActivityRuleConfigDetails().get(0).getRuleType();
                activity.setRuleType(ruleType);
            }
            activity.getActivityRuleConfigDetails().sort(Comparator.comparing(obj -> obj.getOrder()));
        }
        return activity;
    }

    private ActivityComponentBO doPreview (ActivityComponentBO activity) {
        boolean isRule = activity.getActivityRuleConfigDetails().stream().allMatch(x -> StringUtils.isNotEmpty(x.getRuleCode()) && x.getRuleType().equals(DataRuleTypeEnum.RULE.getCode()));
        //多个规则,选择生效时间最近的规则
        if (isRule) {
            //有效的
            List<ActivityComponentConfigDetailBO> activityRuleConfigDetails = queryEffectiveDetails(activity.getActivityRuleConfigDetails());
            //未生效或已过期
            List<ActivityComponentConfigDetailBO> list2 = activity.getActivityRuleConfigDetails().stream().filter(detail -> !TimeConfigUtils.checkRuleTime(detail.getTimeConfig())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(activityRuleConfigDetails)) {
                activityRuleConfigDetails = list2;
                ActivityComponentConfigDetailBO detail = this.queryClosestDetailByPreview(activityRuleConfigDetails);
                if (Objects.isNull(detail)) {
                    return null;
                }
                activity.setActivityRuleConfigDetails(Arrays.asList(detail));
            } else {
                activity.setActivityRuleConfigDetails(activityRuleConfigDetails);
            }
        }
        return activity;
    }

    @Override
    public List<Long> doGetRuleIds(String componentStr) {
        return Optional.ofNullable(componentStr)
                .filter(StringUtils::isNotEmpty)
                .map(str -> JSON.parseObject(str, ActivityComponentBO.class))
                .map(ActivityComponentBO::getActivityRuleConfigDetails)
                .orElse(Collections.emptyList())
                .stream()
                .map(detail -> StringUtils.isNotEmpty(detail.getRuleCode()) ? Long.parseLong(detail.getRuleCode()) : null)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public ActivityComponentBO doComponentParse(String componentStr, AggrBaseReqModule aggrBaseReqModule) {
        ActivityComponentBO activity = JSON.parseObject(componentStr, ActivityComponentBO.class);
        boolean isValid = TimeConfigUtils.checkRuleTime(activity.getTimeConfig());
        if (!isValid) {
            return null;
        }
        //用户分群
        if (Objects.nonNull(aggrBaseReqModule) && Objects.nonNull(activity.getDirectUserGroup())) {
            boolean isShow = directUserGroupService.userFilter(activity.getDirectUserGroup(),aggrBaseReqModule);
            if (!isShow) {
                return null;
            }
        }
        //过滤无效配置详情
        List<ActivityComponentConfigDetailBO> list = this.queryEffectiveDetails(activity.getActivityRuleConfigDetails());
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        activity.setActivityRuleConfigDetails(list);
        //规则生效配置大于1 兜底
        if (DataRuleTypeEnum.RULE.getCode().equals(list.get(0).getRuleType()) && list.size() > 1) {
            ActivityComponentConfigDetailBO detail = this.queryClosestDetailByPreview(list);
            if (Objects.isNull(detail)) {
                return null;
            }
            activity.setActivityRuleConfigDetails(Arrays.asList(detail));
        }
        if (StringUtils.isEmpty(activity.getRuleType())){
            String ruleType = list.get(0).getRuleType();
            activity.setRuleType(ruleType);
        }
        return activity;
    }

    @Override
    public ComponentTypeEnum getComponentType() {
        return ComponentTypeEnum.ACTIVITY;
    }

    @Override
    public List<ComponentDetailBO> doGetComponentDetail(String componentStr) {
        return Optional.ofNullable(componentStr)
                .filter(StringUtils::isNotEmpty)
                .map(str -> JSON.parseObject(str, ActivityComponentBO.class))
                .map(ActivityComponentBO::getActivityRuleConfigDetails)
                .orElse(Collections.emptyList())
                .stream()
                .map(detail -> (ComponentDetailBO) detail)
                .collect(Collectors.toList());
    }

}
