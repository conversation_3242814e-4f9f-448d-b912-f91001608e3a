package com.mengxiang.mshop.cms.core.service.processor.validator;

import com.google.common.base.Preconditions;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.core.model.domain.coupon.CouponComponentConfigDetailBO;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessValidator;
import com.mengxiang.mshop.cms.core.service.util.StreamUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * coupon 组件校验
 * <AUTHOR>
 */
@Component
public class CouponComponentValidator extends AbstractBusinessValidator<PageContext, BusinessModel> {


    @Override
    protected InnerResult<BusinessModel> validator(PageContext context){
        if (CollectionUtils.isEmpty(context.getCouponComponents())) {
            return new InnerResult<BusinessModel>(true, null);
        }
        context.getCouponComponents().stream().forEach(coupon -> {
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(coupon.getCouponConfigDetails()), "优惠券组件 优惠券配置 不能为空");
            Preconditions.checkArgument(coupon.getCouponConfigDetails().size() <= 12, "优惠券组件 优惠券配置 不能超过12个");
            List<CouponComponentConfigDetailBO> couponConfigDetails = coupon.getCouponConfigDetails().stream()
                    .filter(StreamUtils.distinctByKey(k -> k.getAwdId()))
                    .collect(Collectors.toList());
            coupon.setCouponConfigDetails(couponConfigDetails);
        });

        return new InnerResult<BusinessModel>(true, null);
    }


}
