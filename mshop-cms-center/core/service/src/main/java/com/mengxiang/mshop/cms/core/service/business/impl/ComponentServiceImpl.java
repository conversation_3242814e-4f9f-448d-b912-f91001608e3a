package com.mengxiang.mshop.cms.core.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopComponentInstanceDetailDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDetailDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.service.enums.CmsErrorCodeEnum;
import com.mengxiang.mshop.cms.core.model.enums.ComponentTypeEnum;
import com.mengxiang.mshop.cms.core.service.business.ComponentService;
import com.mengxiang.mshop.cms.core.service.business.SequenceGeneratorService;
import com.mengxiang.mshop.cms.core.service.business.detail.ComponentDetailInfoContainer;
import com.mengxiang.mshop.cms.core.service.business.detail.AbstractComponentInfoService;
import com.mengxiang.mshop.cms.core.model.request.ComponetAggregationRequest;
import com.mengxiang.mshop.cms.core.model.request.ComponetCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ComponentServiceImpl implements ComponentService {
    
    @Autowired
    private MshopComponentInstanceDao componentInstance;

    @Autowired
    private MshopComponentInstanceDetailDao mshopComponentInstanceDetailDao;

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;

    @Autowired
    private MshopComponentInstanceDetailDao componentInstanceDetailDao;

    @Autowired
    private ComponentDetailInfoContainer componentDetailInfoContainer;
    
    @Override
    public String create(ComponetCreateRequest req) {
        log.info("[[create]] 创建组件 req:{}", JSON.toJSONString(req));
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getPageCode()), "页面编号 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getComponentType()), "组件类型 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getCreateBy()), "创建人 不能为空");
        ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(req.getComponentType());
        if(componentType == null){
            log.error("[[create]] 创建组件 不支持的组件类型 req:{}",JSON.toJSONString(req));
            throw new BusinessException("不支持的组件类型");
        }
        //校验页面
        MshopPageInstanceDo pageInstance = mshopPageInstanceDao.getByPageCode(req.getPageCode(),null);
        if (Objects.isNull(pageInstance)) {
            log.info("[[create]] 创建组件 页面code不存在 pageCode",req.getPageCode());
            throw new BusinessException(CmsErrorCodeEnum.PAGE_ISNOTEXISTS_ERROR.getErrorMsg());
        }
        String componentCode = sequenceGeneratorService.getSequence(pageInstance.getOwnerId());
        MshopComponentInstanceDo instanceDo = new MshopComponentInstanceDo();
        instanceDo.setPageCode(req.getPageCode());
        instanceDo.setComponentCode(componentCode);
        instanceDo.setType(componentType.getCode());
        componentInstance.insert(instanceDo);
        return componentCode;
    }

    @Override
    public String detail(ComponetAggregationRequest req) {
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getPageCode()), "页面编号 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getComponentCode()), "组件编号 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getVersion()), "版本号 不能为空");

        //组件类型
        ComponentTypeEnum componentType = ComponentTypeEnum.getEnumByCode(req.getComponentType());
        if(Objects.isNull(componentType)){
            log.error("detail componentType is null, req:{}",JSON.toJSONString(req));
            throw new BusinessException("不支持的组件类型");
        }
        MshopComponentInstanceDo mshopComponentInstanceDo = componentInstance.queryByComponentCode(req.getPageCode(),req.getComponentCode(),req.getVersion());
        if (Objects.isNull(mshopComponentInstanceDo)) {
            log.error("detail mshopComponentInstanceDo is null, req:{}",JSON.toJSONString(req));
            throw new BusinessException("没有此组件");
        }
        //查询组件详情
        List<MshopComponentInstanceDetailDo> details = componentInstanceDetailDao.queryByComponent(req.getVersion(), req.getPageCode(), req.getComponentCode());
        if(CollectionUtils.isEmpty(details)){
            log.error("detail details is null, req:{}",JSON.toJSONString(req));
            throw new BusinessException("没有此组件详情");
        }
        //聚合组件类型
        AbstractComponentInfoService componetConvert = componentDetailInfoContainer.getComponentConvert(componentType);
        String componentDetail = componetConvert.getComponent(mshopComponentInstanceDo,details);
        return componentDetail;
    }
    
    @Override
    public List<MshopComponentInstanceDo> queryByPageCode(String pageCode, String version) {
        return componentInstance.queryByPageCode(pageCode,version);
    }

    @Override
    public boolean copyComponents (String pageCode,String version,String featureVersion) {
        List<MshopComponentInstanceDo> componentInstanceDos = componentInstance.queryByPageCode(version,pageCode);
        List<MshopComponentInstanceDetailDo> componentInstanceDetailDos = mshopComponentInstanceDetailDao.queryByPageCode(version,pageCode);
        if (!CollectionUtils.isEmpty(componentInstanceDos)) {
            componentInstanceDos.forEach(c -> c.setVersion(featureVersion));
            int res = componentInstance.insertBatch(componentInstanceDos);
            if (res <= 0) {
                throw new BusinessException("页面保存失败");
            }
        }
        if (!CollectionUtils.isEmpty(componentInstanceDetailDos)) {
            componentInstanceDetailDos.forEach(c -> c.setVersion(featureVersion));
            int res = mshopComponentInstanceDetailDao.insertBatch(componentInstanceDetailDos);
            if (res <= 0) {
                throw new BusinessException("页面保存失败");
            }
        }
        return true;
    }
}
