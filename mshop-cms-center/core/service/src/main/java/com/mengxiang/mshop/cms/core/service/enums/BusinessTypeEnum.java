package com.mengxiang.mshop.cms.core.service.enums;

import com.mengxiang.base.common.process.model.BusinessType;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 */
public enum BusinessTypeEnum implements BusinessType {
    /**
     * 饷店页面搭建
     */
    SAVE_PAGE_MENGXIANG("SAVE_PAGE_MENGXIANG", "饷店页面搭建"),

    /**
     * 商家页面搭建
     */
    SAVE_PAGE_SUPPLIER("SAVE_PAGE_SUPPLIER", "商家页面搭建"),

    /**
     * 商家页面搭建
     */
    SAVE_PAGE_SAAS("SAVE_PAGE_SAAS", "SAAS页面搭建"),

    ;


    /**
     * 枚举值
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    BusinessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
