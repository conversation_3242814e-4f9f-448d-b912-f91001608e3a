package com.mengxiang.mshop.cms.core.service.business;


import com.mengxiang.mshop.cms.common.dal.dataobject.MshopComponentInstanceDo;
import com.mengxiang.mshop.cms.core.model.request.ComponetAggregationRequest;
import com.mengxiang.mshop.cms.core.model.request.ComponetCreateRequest;

import java.util.List;

/**
 * 组件服务
 */
public interface ComponentService {
    
    String create(ComponetCreateRequest req);
    
    List<MshopComponentInstanceDo> queryByPageCode(String pageCode, String version);

    boolean copyComponents (String pageCode,String version,String featureVersion);

    String detail(ComponetAggregationRequest req);
    
}
