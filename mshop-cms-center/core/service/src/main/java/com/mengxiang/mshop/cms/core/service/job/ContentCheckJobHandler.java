package com.mengxiang.mshop.cms.core.service.job;

import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageContentCheckDao;
import com.mengxiang.mshop.cms.common.dal.dao.MshopPageInstanceDao;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageContentCheckDo;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.core.model.enums.ContentSuggestTypeEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.mengxiang.mshop.cms.core.service.securty.ContentCheckService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;


/**
 * 内容检测job
 * <AUTHOR>
 */
@Component
@Slf4j
public class ContentCheckJobHandler {
    
    @Autowired
    private MshopPageInstanceDao mshopPageInstanceDao;
    
    @Autowired
    private MshopPageContentCheckDao mshopPageContentCheckDao;
    
    
    @Autowired
    private PageOperateService pageOperateService;

    @Autowired
    private ContentCheckService contentCheckService;
    
    @XxlJob("pagePublishJobHandler")
    public ReturnT<String> pagePublishJobHandler(String params) {
        try {
            long execStart = System.currentTimeMillis();
            List<MshopPageInstanceDo> mshopPageInstanceDos = queryApproval();
            if (CollectionUtils.isEmpty(mshopPageInstanceDos)) {
                return ReturnT.SUCCESS;
            }
            log.info("[[pagePublishJobHandler]] 页面发布 开始执行定时任务 start...params:{}", params);
            for (MshopPageInstanceDo mshopPageInstanceDo : mshopPageInstanceDos) {
                String pageCode = mshopPageInstanceDo.getPageCode();
                String version = mshopPageInstanceDo.getVersion();
                try {
                    pageOperateService.pageCheckPublish(pageCode, version);
                } catch (Exception e) {
                    log.error("[[pagePublishJobHandler]] 页面发布 异常 pageCode:{},version:{}", pageCode, version, e);
                }
            }
            log.info("[[pagePublishJobHandler]]  页面内容检测 执行结束 end..,cost:{}", System.currentTimeMillis() - execStart);
        } catch (Exception e) {
            log.error("pagePublishJobHandler 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }
    
    
    /**
     * 检测内容。
     */
    @XxlJob("contentCheckResultJobHandler")
    public ReturnT<String> contentCheckResultJobHandler(String params) {
        try {
            long execStart = System.currentTimeMillis();
            log.info("[[contentCheckResultJobHandler]] 内容安全结果查询 开始执行定时任务 start...params:{}", params);
            Long startNum = 0L;
            int pickCount = 500;
            while (true) {
                List<MshopPageContentCheckDo> checkDos = mshopPageContentCheckDao
                        .queryAll(ContentSuggestTypeEnum.PROCESSING.getSuggesst(), startNum, pickCount);
                if (CollectionUtils.isEmpty(checkDos)) {
                    break;
                }
                List<String> dataIdLists = checkDos.stream().map(MshopPageContentCheckDo::getDataId)
                        .collect(Collectors.toList());
                List<List<String>> dataIds = Lists.partition(dataIdLists, 20);
                //每次20个查询
                for (List<String> dataId : dataIds) {
                    try {
                        contentCheckService.contentCheckResult(dataId);
                    } catch (Exception e) {
                        log.error("[[contentCheckResultJobHandler]] 内容检测查询异常 dataIds:{}", dataId, e);
                    }
                }
                startNum = checkDos.get(checkDos.size() - 1).getId();
                if (checkDos.size() < pickCount) {
                    break;
                }
            }
            log.info("[[contentCheckResultJobHandler]]  内容安全结果查询 执行结束 end..,cost:{}", System.currentTimeMillis() - execStart);
        } catch (Exception e) {
            log.error("credit 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }
    
    
    private List<MshopPageInstanceDo> queryApproval() {
        List<MshopPageInstanceDo> mshopPageInstanceDos = mshopPageInstanceDao.queryByStatus(PageInstanceStatusEnum.APPROVAL.getCode());
        return mshopPageInstanceDos;
    }
}
