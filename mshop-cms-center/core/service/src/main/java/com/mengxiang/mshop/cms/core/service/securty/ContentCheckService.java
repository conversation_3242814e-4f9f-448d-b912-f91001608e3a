package com.mengxiang.mshop.cms.core.service.securty;


import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.request.content.PageContentRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.model.result.content.ContentQueryResponse;

import java.util.List;


/**
 * 内容检测
 * <AUTHOR>
 */
public interface ContentCheckService {

    /**
     * 内容检测
     */
    List<ContentCheckResponse> contextCheck(ContentCheckRequest contentCheckRequest);
    
    /**
     * 视频内容检测结果查询222
     */
    List<ContentQueryResponse> videoContextQuery(List<String> dataId);
    
    
    /**页面视频检测
     * @param pageCode
     * @param version
     * @return
     */
    List<ContentCheckResponse>  pageContentCheck(String pageCode,String version, List<PageContentRequest> componentUrls);

    /**
     * 页面视频检测结果查询
     */
    Boolean  contentCheckResult(List<String> dataIds);
    
    /**
     * 视频
     */
    Boolean callBack(String msg);

    String queryContentCheckStatus (String pageCode, String version);
    
    
}