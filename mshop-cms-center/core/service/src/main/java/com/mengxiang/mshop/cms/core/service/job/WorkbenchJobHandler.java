package com.mengxiang.mshop.cms.core.service.job;

import com.mengxiang.mshop.cms.core.service.business.WorkbenchService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 工作流检查
 * <AUTHOR>
 */
@Component
@Slf4j
public class WorkbenchJobHandler {


    @Autowired
    private WorkbenchService workbenchService;

    @XxlJob("noticeWorkbenchJobHandler")
    public ReturnT<String> noticeWorkbenchJobHandler(String params) {
        try {
            workbenchService.notice();
        } catch (Exception e) {
            log.error("noticeWorkbenchJobHandler 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("cancelWorkbenchJobHandler")
    public ReturnT<String> cancelWorkbenchJobHandler(String params) {
        try {
            workbenchService.cancel();
        } catch (Exception e) {
            log.error("cancelWorkbenchJobHandler 定时任务执行出错", e);
        }
        return ReturnT.SUCCESS;
    }

}
