package com.mengxiang.mshop.cms.core.service.processor.action;

import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleEnableRequestDto;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.process.model.BusinessModel;
import com.mengxiang.base.common.process.model.InnerResult;
import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageInstanceDo;
import com.mengxiang.mshop.cms.common.service.integration.feign.DcAiwardClient;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOperateType;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.service.business.PageCacheService;
import com.mengxiang.mshop.cms.core.service.business.PageOperateService;
import com.mengxiang.mshop.cms.core.service.context.PageContext;
import com.mengxiang.mshop.cms.core.service.processor.AbstractBusinessAction;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/4/23
 * @Description: 页面生效时间 为待生效，需要把已生效的 缓存删除
 */
@Service
public class PageExecutoryAction extends AbstractBusinessAction<PageContext, BusinessModel> {

    @Autowired
    private PageOperateService pageOperateService;

    @Override
    protected void beforeAction(PageContext context) {

    }

    @Override
    protected InnerResult<BusinessModel> doAction(PageContext context) {
        SavePageRequest request = context.getRequest();
        MshopPageInstanceDo currentPage = context.getCurrentPage();
        MshopPageInstanceDo featurePageInstance = context.getFeaturePageInstance();
        //删除目前线上在线页面缓存
        if(request.getOperateType().equals(PageOperateType.PUBLISH.getCode()) && featurePageInstance.getStatus().equals(PageInstanceStatusEnum.EXECUTORY.getCode()) && Objects.nonNull(currentPage) && StringUtils.isNotEmpty(currentPage.getVersion())) {
            pageOperateService.clearPageOnLine(featurePageInstance.getPageCode(),featurePageInstance.getType(),featurePageInstance.getOwnerId(),currentPage.getVersion());
        }
        return new InnerResult<>(Boolean.TRUE, null);
    }
}
