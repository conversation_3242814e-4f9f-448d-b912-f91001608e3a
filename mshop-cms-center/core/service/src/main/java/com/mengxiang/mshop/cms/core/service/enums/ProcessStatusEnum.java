package com.mengxiang.mshop.cms.core.service.enums;

/**
 * <AUTHOR>
 * @Date: 2023/4/18
 * @Description:
 */
public enum ProcessStatusEnum {


    AUDITING(0, "审批中"),

    AUDIT_SUCCESS(1, "审批通过"),

    AUDIT_FAIL(2, "审批不通过");

    private Integer code;
    private String desc;


    ProcessStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescBycode(Integer code){
        ProcessStatusEnum[] items = ProcessStatusEnum.values();
        for(ProcessStatusEnum item : items){
            if(code.equals(item.getCode())){
                return item.getDesc();
            }
        }
        return null;
    }
}
