package com.mengxiang.mshop.cms.core.service.business;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.request.workflow.*;
import com.mengxiang.mshop.cms.core.model.result.SaveProcResult;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
public interface WorkbenchService {

    /**
     * 流程回调通知
     * @param req
     */
    void callBack(WorkflowCallBackRequest req);

    /**
     * 创建工作流工单
     * @param req
     * @return
     */
    Result<Void> createProcNo(SaveProcRequest req);

    /**
     * 驳回重新开始
     */
    Result<Void> rejectReStart(ReStartProcRequest req);

    /**
     * 查询工作流信息
     * @param req
     * @return
     */
    WorkflowInfoResp findWorkflowInfo(WorkflowInfoRequest req);


    /**
     * 查询工作流状态
     * @param req
     * @return
     */
    ProcessStatusResp findProcessStatus(ProcessStatusRequest req);

    /**
     * 审批超时通知
     */
    void notice();


    /**
     * 审批超时通知
     */
    void cancel();
}
