package com.mengxiang.mshop.cms;

import com.mengxiang.mshop.cms.common.dal.dataobject.MshopPageWorkbenchDo;
import com.mengxiang.mshop.cms.core.service.enums.ProcessStatusEnum;
import com.mengxiang.workbench.service.facade.common.enums.ButtonTypeV2Enum;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020-09-22 16:16
 **/
public class DemoTest {

    @Test
    public void test(){
        Integer processStatus = ProcessStatusEnum.AUDITING.getCode();
        String processStatusName = ProcessStatusEnum.AUDITING.getDesc();


        // AGREE  REJECT
        String buttonControl = null;

        if(ButtonTypeV2Enum.AGREE.getKey().equals(buttonControl)){

            processStatus = ProcessStatusEnum.AUDIT_SUCCESS.getCode();
            processStatusName = ProcessStatusEnum.AUDIT_SUCCESS.getDesc();

        }
        if(StringUtils.isBlank(buttonControl)){
            processStatus = ProcessStatusEnum.AUDITING.getCode();
            processStatusName = ProcessStatusEnum.AUDITING.getDesc();

        }else if(ButtonTypeV2Enum.AGREE.getKey().equals(buttonControl)){

            processStatus = ProcessStatusEnum.AUDIT_SUCCESS.getCode();
            processStatusName = ProcessStatusEnum.AUDIT_SUCCESS.getDesc();

        }else if(ButtonTypeV2Enum.REJECT.getKey().equals(buttonControl)){

            processStatus = ProcessStatusEnum.AUDIT_FAIL.getCode();
            processStatusName = ProcessStatusEnum.AUDIT_FAIL.getDesc();
        }
        System.out.println("processStatus = " + processStatus);
        System.out.println("processStatusName = " + processStatusName);
    }
}
