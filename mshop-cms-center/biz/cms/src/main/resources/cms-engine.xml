<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <!-- 创建微页面流程 -->
    <import resource="save-page.xml"/>


    <bean id="transactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="transactionManager"/>
    </bean>

    <bean id="businessExecuteEngine"
          class="com.mengxiang.base.common.process.impl.BusinessExecuteEngineImpl">
        <property name="businessExecutorMap">
            <map>
                <entry key="SAVE_PAGE_SUPPLIER" value-ref="pageSaveForSupplier"/>
                <entry key="SAVE_PAGE_MENGXIANG" value-ref="pageSaveForMengXiang"/>
                <entry key="SAVE_PAGE_SAAS" value-ref="pageSaveForSaas"/>
            </map>
        </property>
    </bean>

    <bean id="abstractServiceExecuteTemplate"
          class="com.mengxiang.base.common.process.impl.AbstractServiceExecuteTemplate"
          abstract="true">
        <property name="businessExecuteEngine" ref="businessExecuteEngine"/>
    </bean>

    <bean id="abstractBusinessService"
          class="com.mengxiang.mshop.cms.core.service.business.AbstractBusinessService"
          parent="abstractServiceExecuteTemplate" abstract="true"/>

    <bean id="pageOperateServiceImpl"
          class="com.mengxiang.mshop.cms.core.service.business.impl.PageOperateServiceImpl"
          parent="abstractBusinessService"/>

</beans>