<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- 商家搭建页面-->
    <bean id="pageSaveForSupplier"
          class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl">
        <property name="transactionTemplate" ref="transactionTemplate"/>
        <property name="subTranHolders">
            <list>
                <bean class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl.SubTranHolder">
                    <property name="businessProcessors">
                        <list>
                            <ref bean="pageConfigBaseArgumentValidator"/>
                            <ref bean="pageValidator"/>
                            <ref bean="pageTypeValidator"/>
                            <ref bean="componentJsonParser"/>
                            <ref bean="pageParser"/>
                            <ref bean="activityComponentValidator"/>
                            <ref bean="bannerComponentValidator"/>
                            <ref bean="imageComponentValidator"/>
                            <ref bean="navigationComponentValidator"/>
                            <ref bean="productComponentBySupplierValidator"/>
                            <ref bean="videoComponentValidator"/>
                            <ref bean="baseComponentConverter"/>
                            <ref bean="imageComponentConverter"/>
                        </list>
                    </property>
                </bean>
                <bean class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl.SubTranHolder">
                    <property name="businessProcessors">
                        <list>
                            <ref bean="pageDraftCommitAction"/>
                            <ref bean="pageInstanceCommitAction"/>
                            <ref bean="componetCommitAction"/>
                            <ref bean="componetDetailCommitAction"/>
                            <ref bean="securityVideoScanAsyncAction"/>
                            <ref bean="pageExecutoryAction"/>
                            <ref bean="pagePublishAction"/>
                            <ref bean="operationLogSaveAction"/>
                        </list>
                    </property>
                    <property name="trans" value="true"/>
                </bean>
            </list>
        </property>
    </bean>

    <!-- 梦饷运营搭建页面-->
    <bean id="pageSaveForMengXiang"
          class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl">
        <property name="transactionTemplate" ref="transactionTemplate"/>
        <property name="subTranHolders">
            <list>
                <bean class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl.SubTranHolder">
                    <property name="businessProcessors">
                        <list>
                            <ref bean="pageConfigBaseArgumentValidator"/>
                            <ref bean="pageConfigByMshopArgumentValidator"/>
                            <ref bean="pageValidator"/>
                            <ref bean="pageTypeValidator"/>
                            <ref bean="componentJsonParser"/>
                            <ref bean="pageParser"/>
                            <ref bean="privateMarketComponentValidator"/>
                            <ref bean="activityComponentValidator"/>
                            <ref bean="bannerComponentValidator"/>
                            <ref bean="bannerComponentAkcCmsValidator"/>
                            <ref bean="imageComponentValidator"/>
                            <ref bean="imageComponentAkcCmsValidator"/>
                            <ref bean="navigationComponentByMshopValidator"/>
                            <ref bean="productComponentValidator"/>
                            <ref bean="videoComponentValidator"/>
                            <ref bean="couponComponentValidator"/>
                            <ref bean="cubeComponentValidator"/>
                            <ref bean="baseComponentConverter"/>
                            <ref bean="navigationComponentConverter"/>
                            <ref bean="imageComponentConverter"/>
                            <ref bean="cubeComponentConverter"/>
                            <ref bean="navigationComponentConvertAfterValidator"/>
                        </list>
                    </property>
                </bean>
                <bean class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl.SubTranHolder">
                    <property name="businessProcessors">
                        <list>
                            <ref bean="pageDraftCommitAction"/>
                            <ref bean="pageInstanceCommitAction"/>
                            <ref bean="componetCommitAction"/>
                            <ref bean="componetDetailCommitAction"/>
                            <ref bean="securityVideoScanAsyncAction"/>
                            <ref bean="pagePublishAction"/>
                            <ref bean="operationLogSaveAction"/>
                            <ref bean="createWorkBenchAction"/>
                        </list>
                    </property>
                    <property name="trans" value="true"/>
                </bean>
            </list>
        </property>
    </bean>


    <!-- 梦饷云搭建页面-->
    <bean id="pageSaveForSaas"
          class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl">
        <property name="transactionTemplate" ref="transactionTemplate"/>
        <property name="subTranHolders">
            <list>
                <bean class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl.SubTranHolder">
                    <property name="businessProcessors">
                        <list>
                            <ref bean="pageConfigBaseArgumentValidator"/>
                            <ref bean="pageValidator"/>
                            <ref bean="pageTypeValidator"/>
                            <ref bean="componentJsonParser"/>
                            <ref bean="pageParser"/>
                            <ref bean="activityComponentValidator"/>
                            <ref bean="bannerComponentValidator"/>
                            <ref bean="bannerComponentAkcCmsValidator"/>
                            <ref bean="imageComponentValidator"/>
                            <ref bean="imageComponentAkcCmsValidator"/>
                            <ref bean="navigationComponentByMshopValidator"/>
                            <ref bean="productComponentValidator"/>
                            <ref bean="videoComponentValidator"/>
                            <ref bean="couponComponentValidator"/>
                            <ref bean="pointsCouponComponentValidator"/>
                            <ref bean="cubeComponentValidator"/>
                            <ref bean="baseComponentConverter"/>
                            <ref bean="navigationComponentConverter"/>
                            <ref bean="imageComponentConverter"/>
                            <ref bean="cubeComponentConverter"/>
                            <ref bean="navigationComponentConvertAfterValidator"/>
                        </list>
                    </property>
                </bean>
                <bean class="com.mengxiang.base.common.process.executor.BusinessSubTransExecutorImpl.SubTranHolder">
                    <property name="businessProcessors">
                        <list>
                            <ref bean="pageDraftCommitAction"/>
                            <ref bean="pageInstanceCommitAction"/>
                            <ref bean="componetCommitAction"/>
                            <ref bean="componetDetailCommitAction"/>
                            <ref bean="securityVideoScanAsyncAction"/>
                            <ref bean="pagePublishAction"/>
                            <ref bean="operationLogSaveAction"/>
                        </list>
                    </property>
                    <property name="trans" value="true"/>
                </bean>
            </list>
        </property>
    </bean>
</beans>