# 组件领域模型设计

## 1. 概述

基于ComponentStyleBO的分析，本文档设计了CMS系统中几个核心组件的领域模型。这些组件继承了ComponentStyleBO的样式属性，并扩展了各自的业务特性。

## 2. 基础领域模型

### 2.1 组件样式基类 (ComponentStyleBO)

```java
/**
 * 组件样式基类 - 定义所有组件的通用样式属性
 */
@Data
public class ComponentStyleBO {
    @ApiModelProperty(value = "上边距")
    private Integer paddingTop;
    
    @ApiModelProperty(value = "下边距")
    private Integer paddingBottom;
    
    @ApiModelProperty(value = "左边距")
    private Integer paddingLeft;
    
    @ApiModelProperty(value = "右边距")
    private Integer paddingRight;
    
    @ApiModelProperty(value = "左上圆角")
    private Integer borderTopLeftRadius;
    
    @ApiModelProperty(value = "右上圆角")
    private Integer borderTopRightRadius;
    
    @ApiModelProperty(value = "左下圆角")
    private Integer borderBottomLeftRadius;
    
    @ApiModelProperty(value = "右下圆角")
    private Integer borderBottomRightRadius;
}
```

### 2.2 组件基类 (ComponentBO)

```java
/**
 * 组件基类 - 继承样式属性，添加组件通用业务属性
 */
@Data
public class ComponentBO extends ComponentStyleBO {
    @ApiModelProperty(value = "组件编号")
    private String componentCode;
    
    @ApiModelProperty(value = "组件名称")
    private String componentName;
    
    @ApiModelProperty(value = "前端组件id")
    private String componentId;
    
    @ApiModelProperty(value = "版本号")
    private String version;
    
    @ApiModelProperty(value = "组件类型")
    private ComponentTypeEnum type;
    
    @ApiModelProperty(value = "组件排序")
    private Integer order;
    
    @ApiModelProperty(value = "生效方式")
    private TimeConfigBO timeConfig;
    
    @ApiModelProperty(value = "权限列表")
    private List<String> authorizationList;
    
    @ApiModelProperty(value = "定向分群")
    private DirectUserGroupBO directUserGroup;
}
```

### 2.3 组件详情基类 (ComponentDetailBO)

```java
/**
 * 组件详情基类 - 继承样式属性，用于组件内部配置项
 */
@Data
public class ComponentDetailBO extends ComponentStyleBO {
    @ApiModelProperty(value = "组件配置编号")
    private String componentDetailCode;
    
    @ApiModelProperty(value = "组件详情排序")
    private Integer order;
    
    @ApiModelProperty(value = "生效方式")
    private TimeConfigBO timeConfig;
    
    @ApiModelProperty(value = "定向分群")
    private DirectUserGroupBO directUserGroup;
}
```

## 3. 核心组件领域模型

### 3.1 图片组件 (ImageComponent)

```mermaid
classDiagram
    ComponentStyleBO <|-- ComponentBO
    ComponentBO <|-- ImageComponentBO
    ComponentDetailBO <|-- ImgComponentConfigDetailBO
    ComponentDetailBO <|-- ImgComponentHoleConfigDetailBO
    ImageComponentBO --> ImgComponentConfigDetailBO : hotConfigDetails
    ImageComponentBO --> ImgComponentHoleConfigDetailBO : holeConfigDetails
    
    class ComponentStyleBO {
        +Integer paddingTop
        +Integer paddingBottom
        +Integer paddingLeft
        +Integer paddingRight
        +Integer borderTopLeftRadius
        +Integer borderTopRightRadius
        +Integer borderBottomLeftRadius
        +Integer borderBottomRightRadius
    }
    
    class ImageComponentBO {
        +String url
        +Double width
        +Double height
        +Integer bulletScreenDisplay
        +String bulletScreenSite
        +List~ImgComponentConfigDetailBO~ hotConfigDetails
        +List~ImgComponentHoleConfigDetailBO~ holeConfigDetails
    }
    
    class ImgComponentConfigDetailBO {
        +String targetType
        +String targetId
        +String activityId
        +Double x
        +Double y
        +Double width
        +Double height
        +String miniAppId
    }
    
    class ImgComponentHoleConfigDetailBO {
        +String targetType
        +Double x
        +Double y
        +Double width
        +Double height
    }
```

**领域特性：**
- **展示能力**: 支持图片展示、尺寸控制
- **交互能力**: 热区点击、挖孔区域配置
- **弹幕功能**: 支持弹幕展示和位置控制
- **跳转能力**: 支持页面、链接、商品、活动等多种跳转

### 3.2 轮播组件 (BannerComponent)

```mermaid
classDiagram
    ComponentBO <|-- BannerComponentBO
    ComponentDetailBO <|-- BannerComponentConfigDetailBO
    BannerComponentBO --> BannerComponentConfigDetailBO : carouselConfigDetails
    
    class BannerComponentBO {
        +Double width
        +Double height
        +String backgroundImgUrl
        +Integer hasBackgroundImg
        +String carouselType
        +List~BannerComponentConfigDetailBO~ carouselConfigDetails
    }
    
    class BannerComponentConfigDetailBO {
        +String img
        +Double width
        +Double height
        +String targetType
        +String targetId
        +String activityId
        +String miniAppId
    }
```

**领域特性：**
- **轮播能力**: 支持容器内轮播、图片轮播两种模式
- **背景控制**: 支持背景图片和背景色设置
- **多图管理**: 支持多张图片的轮播配置
- **跳转能力**: 每张轮播图支持独立的跳转配置

### 3.3 商品组件 (ProductComponent)

```mermaid
classDiagram
    ComponentBO <|-- ProductComponentBO
    ComponentDetailBO <|-- ProductComponentConfigDetailBO
    ProductComponentBO --> ProductComponentConfigDetailBO : productRuleConfigDetails
    
    class ProductComponentBO {
        +String backgroundImgUrl
        +String imgStyle
        +String backgroundColor
        +Integer hasBackgroundImg
        +String animationType
        +String layoutType
        +Boolean showTitleImg
        +String titleImgUrl
        +List~ProductComponentConfigDetailBO~ productRuleConfigDetails
    }
    
    class ProductComponentConfigDetailBO {
        +String ruleType
        +String ruleCode
        +Integer ruleStatus
        +String ruleCreateTime
        +String activityId
        +String businessId
        +String promotionType
    }
```

**领域特性：**
- **布局能力**: 支持一行一、一行二、一行三、一行多等多种布局
- **样式控制**: 支持背景图、背景色、图片样式配置
- **动效支持**: 支持滑动缩放等动效
- **规则驱动**: 支持自主选择、规则中心、条件筛选等商品规则
- **营销集成**: 支持档期活动、营销类型配置

### 3.4 活动组件 (ActivityComponent)

```mermaid
classDiagram
    ComponentBO <|-- ActivityComponentBO
    ComponentDetailBO <|-- ActivityComponentConfigDetailBO
    ActivityComponentBO --> ActivityComponentConfigDetailBO : activityRuleConfigDetails
    
    class ActivityComponentBO {
        +Integer pageSize
        +Integer morePageSize
        +String ruleType
        +Boolean showTitleImg
        +String titleImgUrl
        +List~ActivityComponentConfigDetailBO~ activityRuleConfigDetails
    }
    
    class ActivityComponentConfigDetailBO {
        +String ruleType
        +String ruleCode
        +Integer ruleStatus
        +String ruleCreateTime
        +String businessId
        +String promotionType
    }
```

**领域特性：**
- **分页控制**: 支持默认展示数量和更多商品数量配置
- **规则驱动**: 支持自主、规则中心、条件等多种规则类型
- **标题配置**: 支持配套标题图片
- **活动管理**: 支持档期活动和营销类型配置

### 3.5 优惠券组件 (CouponComponent)

```mermaid
classDiagram
    ComponentBO <|-- CouponComponentBO
    ComponentDetailBO <|-- CouponComponentConfigDetailBO
    CouponComponentBO --> CouponComponentConfigDetailBO : couponConfigDetails
    
    class CouponComponentBO {
        +String layoutType
        +String imgStyle
        +String backgroundColor
        +String backgroundImgUrl
        +Integer hasBackgroundImg
        +TimeConfigBO timeConfig
        +List~CouponComponentConfigDetailBO~ couponConfigDetails
    }
    
    class CouponComponentConfigDetailBO {
        +String promoActivityId
        +String awdId
        +String activityId
    }
```

**领域特性：**
- **布局控制**: 支持一行一、一行多、二行多等布局方式
- **样式配置**: 支持带图样式（top、left）和背景配置
- **营销集成**: 关联营销活动ID、优惠券ID、档期ID
- **时间控制**: 支持优惠券的时间配置

### 3.6 视频组件 (VideoComponent)

```mermaid
classDiagram
    ComponentBO <|-- VideoComponentBO
    ComponentDetailBO <|-- VideoComponentConfigDetailBO
    VideoComponentBO --> VideoComponentConfigDetailBO : videoConfigDetails
    
    class VideoComponentBO {
        +String layoutType
        +List~VideoComponentConfigDetailBO~ videoConfigDetails
    }
    
    class VideoComponentConfigDetailBO {
        +Double width
        +Double height
        +String name
        +String url
        +String imageUrl
        +String coverImg
        +ContentCheckResultBO checkResult
    }
```

**领域特性：**
- **布局支持**: 支持一行一、一行二等布局方式
- **视频管理**: 支持多个视频的配置和管理
- **尺寸控制**: 支持视频宽高配置
- **封面管理**: 支持占位图和封面图配置
- **内容安全**: 集成视频内容安全检查
