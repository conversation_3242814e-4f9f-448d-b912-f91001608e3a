<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.mshop.cms</groupId>
        <artifactId>mshop-cms-center</artifactId>
        <version>1.0.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mshop-cms-center-deploy</artifactId>
    <packaging>war</packaging>

    <name>mshop-cms-center-deploy</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-center-web-cms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-center-biz-service-impl</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>mshop-cms-center</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.mengxiang.mshop.cms.Application</mainClass>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
