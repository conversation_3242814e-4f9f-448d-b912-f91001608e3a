CREATE TABLE `mshop_page_content_check`
(
    id                    bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    page_code             varchar(64)  not null default '' comment '页面编号',
    version               varchar(64)  not null default '' comment '版本号',
    component_code        varchar(64)  not null default '' comment '组件编号',
    component_config_code varchar(64)  not null default '' comment '组件配置code',
    content               varchar(512) not null default '' comment '检测内容',
    data_id               varchar(64)  not null default '' comment '检测id',
    content_type          tinyint(4) not null default 0 comment '检测类型 0:文本 1:图片 2:视频',
    check_status          varchar(64)  not null default '' comment 'UN_KNOWN:未知原因，PASS:通过,REVIEW:审核 BLOCK:阻塞 PROCESSING:处理中',
    check_result          varchar(64)  not null default '' comment '检测结果',

    delete_flag           tinyint(64) not null default 0 comment '状态 0:正常 1:删除',
    create_by             varchar(16)  not null default '' not null comment '创建人',
    update_by             varchar(16)  not null default '' not null comment '更新人',
    create_time           timestamp(3) not null default CURRENT_TIMESTAMP(3) not null comment '创建时间',
    update_time           timestamp(3) not null default CURRENT_TIMESTAMP(3) not null on update CURRENT_TIMESTAMP (3) comment '最后修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_page_version_component`(`page_code`, `version`,`component_code`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '页面视频检测结果';

