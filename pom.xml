<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.base</groupId>
        <artifactId>base-parent</artifactId>
        <version>1.1.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.mengxiang.mshop.cms</groupId>
    <artifactId>mshop-cms-center</artifactId>
    <packaging>pom</packaging>
    <version>1.2.2</version>

    <modules>
        <module>mshop-cms-center/common/dal</module>
        <module>mshop-cms-center/common/util</module>
        <module>mshop-cms-center/common/service/integration</module>
        <module>mshop-cms-center/core/model</module>
        <module>mshop-cms-center/core/service</module>
        <module>mshop-cms-center/service/facade/common</module>
        <module>mshop-cms-center/biz/shared</module>
        <module>mshop-cms-center/biz/cms</module>
        <module>mshop-cms-center/biz/service/impl</module>
        <module>mshop-cms-center/test</module>
        <module>mshop-cms-center/web/cms</module>
        <module>assembly/template</module>
        <module>deploy</module>
    </modules>

    <!--版本控制-->
    <properties>
        <security-ugc-api-stub.version>0.0.3.6</security-ugc-api-stub.version>
        <guava.version>30.0-jre</guava.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <!-- demo project depends -->
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-common-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-common-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-common-service-integration</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-core-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-core-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-service-facade-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-biz-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-biz-cms</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-biz-service-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-assembly-template</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-deploy</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-web-cms</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>transaction-framework</artifactId>
                <version>1.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.9</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>common-sequence</artifactId>
                <version>1.2.13</version>
            </dependency>

            <dependency>
                <groupId>com.aikucun.security.ugc</groupId>
                <artifactId>security-ugc-api-stub</artifactId>
                <version>${security-ugc-api-stub.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- guava工具包-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>30.0-jre</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.12</version>
            </dependency>

            <dependency>
                <groupId>com.akucun.mshop</groupId>
                <artifactId>mshop-common</artifactId>
                <version>0.2.3</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>base-metrics-client</artifactId>
                <version>2.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83_noneautotype</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.16</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <defaultGoal>install</defaultGoal>
        <plugins>
            <plugin>
                <groupId>com.mengxiang.dalgen</groupId>
                <artifactId>mybatis-maven-plugin</artifactId>
                <version>1.1.2</version>
                <configuration>
                    <templateDirectory>dalgen/templates</templateDirectory>
                    <outputDirectory>mshop-cms-center/common/dal/src</outputDirectory>
                    <config>dalgen/config/config.xml</config>
                    <copyTemplate>false</copyTemplate>
                </configuration>
                <inherited>false</inherited>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://maven.aikucun.com:8082/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>nexus-releases</id>
            <url>http://maven.aikucun.com:8082/nexus/content/repositories/releases/</url>
        </repository>
    </distributionManagement>
</project>
