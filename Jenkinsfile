node {
    stage('pull-code') {
        checkout scm
    }

    try {
            stage('deploy-mshop-cms-center') {
                sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/pom.xml clean deploy"
            }
        } catch (e){
            echo 'deploy-mshop-cms-center deploy failed'
        }

    try {
            stage('deploy-mshop-cms-center-core-model') {
                sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/mshop-cms-center-core-model/pom.xml clean deploy"
            }
        } catch (e){
            echo 'mshop-cms-center-core-model deploy failed'
       }

    try {
             stage('deploy-mshop-cms-center-service-facade-common') {
                  sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/mshop-cms-center-service-facade-common/pom.xml clean deploy"
             }
         } catch (e){
            echo 'mshop-cms-center-service-facade-common deploy failed'
         }
}