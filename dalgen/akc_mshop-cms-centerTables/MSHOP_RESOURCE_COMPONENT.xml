<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_RESOURCE_COMPONENT" physicalName="MSHOP_RESOURCE_COMPONENT" remark="资源位表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,NAME,CHANNEL,OWNER_ID,CREATE_BY 
        ,TENANT_ID,UPDATE_BY,OWNER_TYPE,CATEGORY_ID,TIME_CONFIG 
        ,CONFIG_DETAIL,RESOURCE_TYPE,RESOURCE_PAGE_TYPE,STATUS,DELETE_FLAG 
        ,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.NAME,sf.CHANNEL,sf.OWNER_ID,sf.CREATE_BY
        ,sf.TENANT_ID,sf.UPDATE_BY,sf.OWNER_TYPE,sf.CATEGORY_ID,sf.TIME_CONFIG
        ,sf.CONFIG_DETAIL,sf.RESOURCE_TYPE,sf.RESOURCE_PAGE_TYPE,sf.STATUS,sf.DELETE_FLAG
        ,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_RESOURCE_COMPONENT">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_RESOURCE_COMPONENT(
            ID
            ,NAME
            ,CHANNEL
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,CATEGORY_ID
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,RESOURCE_TYPE
            ,RESOURCE_PAGE_TYPE
            ,STATUS
            ,DELETE_FLAG
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{name,jdbcType=VARCHAR}
            , #{channel,jdbcType=VARCHAR}
            , #{ownerId,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{tenantId,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{ownerType,jdbcType=VARCHAR}
            , #{categoryId,jdbcType=VARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{configDetail,jdbcType=LONGVARCHAR}
            , #{resourceType,jdbcType=VARCHAR}
            , #{resourcePageType,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{deleteFlag,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_RESOURCE_COMPONENT">
        INSERT INTO MSHOP_RESOURCE_COMPONENT(
            ID
            ,NAME
            ,CHANNEL
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,CATEGORY_ID
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,RESOURCE_TYPE
            ,RESOURCE_PAGE_TYPE
            ,STATUS
            ,DELETE_FLAG
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.name,jdbcType=VARCHAR}
                , #{item.channel,jdbcType=VARCHAR}
                , #{item.ownerId,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.tenantId,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.ownerType,jdbcType=VARCHAR}
                , #{item.categoryId,jdbcType=VARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.configDetail,jdbcType=LONGVARCHAR}
                , #{item.resourceType,jdbcType=VARCHAR}
                , #{item.resourcePageType,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.deleteFlag,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_RESOURCE_COMPONENT">
        <![CDATA[
        UPDATE MSHOP_RESOURCE_COMPONENT
        SET
            NAME            = #{name,jdbcType=VARCHAR}
            ,CHANNEL         = #{channel,jdbcType=VARCHAR}
            ,OWNER_ID        = #{ownerId,jdbcType=VARCHAR}
            ,CREATE_BY       = #{createBy,jdbcType=VARCHAR}
            ,TENANT_ID       = #{tenantId,jdbcType=VARCHAR}
            ,UPDATE_BY       = #{updateBy,jdbcType=VARCHAR}
            ,OWNER_TYPE      = #{ownerType,jdbcType=VARCHAR}
            ,CATEGORY_ID     = #{categoryId,jdbcType=VARCHAR}
            ,TIME_CONFIG     = #{timeConfig,jdbcType=LONGVARCHAR}
            ,CONFIG_DETAIL   = #{configDetail,jdbcType=LONGVARCHAR}
            ,RESOURCE_TYPE   = #{resourceType,jdbcType=VARCHAR}
            ,RESOURCE_PAGE_TYPE = #{resourcePageType,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_RESOURCE_COMPONENT">
        <![CDATA[
        DELETE FROM MSHOP_RESOURCE_COMPONENT
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_RESOURCE_COMPONENT">
        SELECT *
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxResourceType" multiplicity="many" remark="根据普通索引IdxResourceType获取数据:MSHOP_RESOURCE_COMPONENT">
        SELECT *
        FROM MSHOP_RESOURCE_COMPONENT
        WHERE
        <![CDATA[
            RESOURCE_TYPE   = #{resourceType,jdbcType=VARCHAR}
            AND TENANT_ID       = #{tenantId,jdbcType=VARCHAR}
            AND OWNER_TYPE      = #{ownerType,jdbcType=VARCHAR}
            AND OWNER_ID        = #{ownerId,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
