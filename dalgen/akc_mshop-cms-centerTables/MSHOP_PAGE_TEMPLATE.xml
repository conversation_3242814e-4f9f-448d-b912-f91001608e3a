<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_PAGE_TEMPLATE" physicalName="MSHOP_PAGE_TEMPLATE" remark="页面模版表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,OWNER_ID,CREATE_BY,TENANT_ID,UPDATE_BY 
        ,OWNER_TYPE,PAGE_USE_RULE,PAGE_USE_TYPE,USE_CHANNELS,TEMPLATE_CODE 
        ,TEMPLATE_DESC,TEMPLATE_NAME,COMPONENT_USE_RULE,TEMPLATE_IMAGE_URL,STATUS 
        ,DELETE_FLAG,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.OWNER_ID,sf.CREATE_BY,sf.TENANT_ID,sf.UPDATE_BY
        ,sf.OWNER_TYPE,sf.PAGE_USE_RULE,sf.PAGE_USE_TYPE,sf.USE_CHANNELS,sf.TEMPLATE_CODE
        ,sf.TEMPLATE_DESC,sf.TEMPLATE_NAME,sf.COMPONENT_USE_RULE,sf.TEMPLATE_IMAGE_URL,sf.STATUS
        ,sf.DELETE_FLAG,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_PAGE_TEMPLATE">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_TEMPLATE(
            ID
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,PAGE_USE_RULE
            ,PAGE_USE_TYPE
            ,USE_CHANNELS
            ,TEMPLATE_CODE
            ,TEMPLATE_DESC
            ,TEMPLATE_NAME
            ,COMPONENT_USE_RULE
            ,TEMPLATE_IMAGE_URL
            ,STATUS
        )VALUES(
             null
            , #{ownerId,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{tenantId,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{ownerType,jdbcType=VARCHAR}
            , #{pageUseRule,jdbcType=LONGVARCHAR}
            , #{pageUseType,jdbcType=VARCHAR}
            , #{useChannels,jdbcType=VARCHAR}
            , #{templateCode,jdbcType=VARCHAR}
            , #{templateDesc,jdbcType=VARCHAR}
            , #{templateName,jdbcType=VARCHAR}
            , #{componentUseRule,jdbcType=LONGVARCHAR}
            , #{templateImageUrl,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_PAGE_TEMPLATE">
        INSERT INTO MSHOP_PAGE_TEMPLATE(
            ID
            ,OWNER_ID
            ,CREATE_BY
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,PAGE_USE_RULE
            ,PAGE_USE_TYPE
            ,USE_CHANNELS
            ,TEMPLATE_CODE
            ,TEMPLATE_DESC
            ,TEMPLATE_NAME
            ,COMPONENT_USE_RULE
            ,TEMPLATE_IMAGE_URL
            ,STATUS
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.ownerId,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.tenantId,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.ownerType,jdbcType=VARCHAR}
                , #{item.pageUseRule,jdbcType=LONGVARCHAR}
                , #{item.pageUseType,jdbcType=VARCHAR}
                , #{item.useChannels,jdbcType=VARCHAR}
                , #{item.templateCode,jdbcType=VARCHAR}
                , #{item.templateDesc,jdbcType=VARCHAR}
                , #{item.templateName,jdbcType=VARCHAR}
                , #{item.componentUseRule,jdbcType=LONGVARCHAR}
                , #{item.templateImageUrl,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_PAGE_TEMPLATE">
        <![CDATA[
        UPDATE MSHOP_PAGE_TEMPLATE
        SET
            OWNER_ID        = #{ownerId,jdbcType=VARCHAR}
            ,CREATE_BY       = #{createBy,jdbcType=VARCHAR}
            ,TENANT_ID       = #{tenantId,jdbcType=VARCHAR}
            ,UPDATE_BY       = #{updateBy,jdbcType=VARCHAR}
            ,OWNER_TYPE      = #{ownerType,jdbcType=VARCHAR}
            ,PAGE_USE_RULE   = #{pageUseRule,jdbcType=LONGVARCHAR}
            ,PAGE_USE_TYPE   = #{pageUseType,jdbcType=VARCHAR}
            ,USE_CHANNELS    = #{useChannels,jdbcType=VARCHAR}
            ,TEMPLATE_CODE   = #{templateCode,jdbcType=VARCHAR}
            ,TEMPLATE_DESC   = #{templateDesc,jdbcType=VARCHAR}
            ,TEMPLATE_NAME   = #{templateName,jdbcType=VARCHAR}
            ,COMPONENT_USE_RULE = #{componentUseRule,jdbcType=LONGVARCHAR}
            ,TEMPLATE_IMAGE_URL = #{templateImageUrl,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_PAGE_TEMPLATE">
        <![CDATA[
        DELETE FROM MSHOP_PAGE_TEMPLATE
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_PAGE_TEMPLATE">
        SELECT *
        FROM MSHOP_PAGE_TEMPLATE
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

</table>
