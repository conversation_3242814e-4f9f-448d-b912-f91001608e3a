<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_COMPONENT_INSTANCE_DETAIL" physicalName="MSHOP_COMPONENT_INSTANCE_DETAIL" remark="组件配置详情表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,VERSION,PAGE_CODE,TIME_CONFIG,CONFIG_DETAIL 
        ,COMPONENT_CODE,COMPONENT_TYPE,DIRECT_USER_GROUP,CONFIG_DETAIL_CODE,COMPONENT_DETAIL_TYPE 
        ,DELETE_FLAG,ORDER_VALUE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.VERSION,sf.PAGE_CODE,sf.TIME_CONFIG,sf.CONFIG_DETAIL
        ,sf.COMPONENT_CODE,sf.COMPONENT_TYPE,sf.DIRECT_USER_GROUP,sf.CONFIG_DETAIL_CODE,sf.COMPONENT_DETAIL_TYPE
        ,sf.DELETE_FLAG,sf.ORDER_VALUE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_COMPONENT_INSTANCE_DETAIL">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_COMPONENT_INSTANCE_DETAIL(
            ID
            ,VERSION
            ,PAGE_CODE
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,COMPONENT_CODE
            ,COMPONENT_TYPE
            ,DIRECT_USER_GROUP
            ,CONFIG_DETAIL_CODE
            ,COMPONENT_DETAIL_TYPE
            ,DELETE_FLAG
            ,ORDER_VALUE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{version,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{configDetail,jdbcType=LONGVARCHAR}
            , #{componentCode,jdbcType=VARCHAR}
            , #{componentType,jdbcType=VARCHAR}
            , #{directUserGroup,jdbcType=LONGVARCHAR}
            , #{configDetailCode,jdbcType=VARCHAR}
            , #{componentDetailType,jdbcType=VARCHAR}
            , #{deleteFlag,jdbcType=INTEGER}
            , #{orderValue,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_COMPONENT_INSTANCE_DETAIL">
        INSERT INTO MSHOP_COMPONENT_INSTANCE_DETAIL(
            ID
            ,VERSION
            ,PAGE_CODE
            ,TIME_CONFIG
            ,CONFIG_DETAIL
            ,COMPONENT_CODE
            ,COMPONENT_TYPE
            ,DIRECT_USER_GROUP
            ,CONFIG_DETAIL_CODE
            ,COMPONENT_DETAIL_TYPE
            ,DELETE_FLAG
            ,ORDER_VALUE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.version,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.configDetail,jdbcType=LONGVARCHAR}
                , #{item.componentCode,jdbcType=VARCHAR}
                , #{item.componentType,jdbcType=VARCHAR}
                , #{item.directUserGroup,jdbcType=LONGVARCHAR}
                , #{item.configDetailCode,jdbcType=VARCHAR}
                , #{item.componentDetailType,jdbcType=VARCHAR}
                , #{item.deleteFlag,jdbcType=INTEGER}
                , #{item.orderValue,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_COMPONENT_INSTANCE_DETAIL">
        <![CDATA[
        UPDATE MSHOP_COMPONENT_INSTANCE_DETAIL
        SET
            VERSION         = #{version,jdbcType=VARCHAR}
            ,PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            ,TIME_CONFIG     = #{timeConfig,jdbcType=LONGVARCHAR}
            ,CONFIG_DETAIL   = #{configDetail,jdbcType=LONGVARCHAR}
            ,COMPONENT_CODE  = #{componentCode,jdbcType=VARCHAR}
            ,COMPONENT_TYPE  = #{componentType,jdbcType=VARCHAR}
            ,DIRECT_USER_GROUP = #{directUserGroup,jdbcType=LONGVARCHAR}
            ,CONFIG_DETAIL_CODE = #{configDetailCode,jdbcType=VARCHAR}
            ,COMPONENT_DETAIL_TYPE = #{componentDetailType,jdbcType=VARCHAR}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=INTEGER}
            ,ORDER_VALUE     = #{orderValue,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_COMPONENT_INSTANCE_DETAIL">
        <![CDATA[
        DELETE FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_COMPONENT_INSTANCE_DETAIL">
        SELECT *
        FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据普通索引查询数据 -->
    <operation name="queryByMshopComponentInstanceDetailPageCodeIdx" multiplicity="many" remark="根据普通索引MshopComponentInstanceDetailPageCodeIdx获取数据:MSHOP_COMPONENT_INSTANCE_DETAIL">
        SELECT *
        FROM MSHOP_COMPONENT_INSTANCE_DETAIL
        WHERE
        <![CDATA[
            PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            AND VERSION         = #{version,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
