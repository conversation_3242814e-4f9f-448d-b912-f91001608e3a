<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_PAGE_CONTENT_CHECK" physicalName="MSHOP_PAGE_CONTENT_CHECK" remark="页面视频检测结果">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,DATA_ID,CONTENT,VERSION,PAGE_CODE 
        ,CHECK_RESULT,CHECK_STATUS,COMPONENT_CODE,COMPONENT_CONFIG_CODE,DELETE_FLAG 
        ,CONTENT_TYPE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.DATA_ID,sf.CONTENT,sf.VERSION,sf.PAGE_CODE
        ,sf.CHECK_RESULT,sf.CHECK_STATUS,sf.COMPONENT_CODE,sf.COMPONENT_CONFIG_CODE,sf.DELETE_FLAG
        ,sf.CONTENT_TYPE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_PAGE_CONTENT_CHECK">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_CONTENT_CHECK(
            ID
            ,DATA_ID
            ,CONTENT
            ,VERSION
            ,PAGE_CODE
            ,CHECK_RESULT
            ,CHECK_STATUS
            ,COMPONENT_CODE
            ,COMPONENT_CONFIG_CODE
            ,CONTENT_TYPE
        )VALUES(
             null
            , #{dataId,jdbcType=VARCHAR}
            , #{content,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{checkResult,jdbcType=VARCHAR}
            , #{checkStatus,jdbcType=VARCHAR}
            , #{componentCode,jdbcType=VARCHAR}
            , #{componentConfigCode,jdbcType=VARCHAR}
            , #{contentType,jdbcType=TINYINT}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_PAGE_CONTENT_CHECK">
        INSERT INTO MSHOP_PAGE_CONTENT_CHECK(
            ID
            ,DATA_ID
            ,CONTENT
            ,VERSION
            ,PAGE_CODE
            ,CHECK_RESULT
            ,CHECK_STATUS
            ,COMPONENT_CODE
            ,COMPONENT_CONFIG_CODE
            ,CONTENT_TYPE
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.dataId,jdbcType=VARCHAR}
                , #{item.content,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.checkResult,jdbcType=VARCHAR}
                , #{item.checkStatus,jdbcType=VARCHAR}
                , #{item.componentCode,jdbcType=VARCHAR}
                , #{item.componentConfigCode,jdbcType=VARCHAR}
                , #{item.contentType,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_PAGE_CONTENT_CHECK">
        <![CDATA[
        UPDATE MSHOP_PAGE_CONTENT_CHECK
        SET
            DATA_ID         = #{dataId,jdbcType=VARCHAR}
            ,CONTENT         = #{content,jdbcType=VARCHAR}
            ,VERSION         = #{version,jdbcType=VARCHAR}
            ,PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            ,CHECK_RESULT    = #{checkResult,jdbcType=VARCHAR}
            ,CHECK_STATUS    = #{checkStatus,jdbcType=VARCHAR}
            ,COMPONENT_CODE  = #{componentCode,jdbcType=VARCHAR}
            ,COMPONENT_CONFIG_CODE = #{componentConfigCode,jdbcType=VARCHAR}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=TINYINT}
            ,CONTENT_TYPE    = #{contentType,jdbcType=TINYINT}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_PAGE_CONTENT_CHECK">
        <![CDATA[
        DELETE FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_PAGE_CONTENT_CHECK">
        SELECT *
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxPageVersionComponent" multiplicity="many" remark="根据普通索引IdxPageVersionComponent获取数据:MSHOP_PAGE_CONTENT_CHECK">
        SELECT *
        FROM MSHOP_PAGE_CONTENT_CHECK
        WHERE
        <![CDATA[
            PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            AND VERSION         = #{version,jdbcType=VARCHAR}
            AND COMPONENT_CODE  = #{componentCode,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
