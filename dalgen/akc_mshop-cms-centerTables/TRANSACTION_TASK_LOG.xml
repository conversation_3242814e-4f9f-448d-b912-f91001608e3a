<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TRANSACTION_TASK_LOG" physicalName="TRANSACTION_TASK_LOG" remark="一致性事务补偿表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,STATUS,TASK_ID,TASK_TYPE,ERROR_CODE 
        ,RETRY_STATUS,ERROR_MESSAGE,TASK_CLASS_NAME,REVERSAL_STATUS,TRANSACTION_TYPE 
        ,RESULT_ADDITIONAL_INFO,REQUEST_ADDITIONAL_INFO,TIMES,CREATE_TIME,UPDATE_TIME 
        ,NEXT_EXECUTE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.STATUS,sf.TASK_ID,sf.TASK_TYPE,sf.ERROR_CODE
        ,sf.RETRY_STATUS,sf.ERROR_MESSAGE,sf.TASK_CLASS_NAME,sf.REVERSAL_STATUS,sf.TRANSACTION_TYPE
        ,sf.RESULT_ADDITIONAL_INFO,sf.REQUEST_ADDITIONAL_INFO,sf.TIMES,sf.CREATE_TIME,sf.UPDATE_TIME
        ,sf.NEXT_EXECUTE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:TRANSACTION_TASK_LOG">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO TRANSACTION_TASK_LOG(
            ID
            ,STATUS
            ,TASK_ID
            ,TASK_TYPE
            ,ERROR_CODE
            ,RETRY_STATUS
            ,ERROR_MESSAGE
            ,TASK_CLASS_NAME
            ,REVERSAL_STATUS
            ,TRANSACTION_TYPE
            ,RESULT_ADDITIONAL_INFO
            ,REQUEST_ADDITIONAL_INFO
            ,TIMES
            ,CREATE_TIME
            ,UPDATE_TIME
            ,NEXT_EXECUTE_TIME
        )VALUES(
             null
            , #{status,jdbcType=VARCHAR}
            , #{taskId,jdbcType=VARCHAR}
            , #{taskType,jdbcType=VARCHAR}
            , #{errorCode,jdbcType=VARCHAR}
            , #{retryStatus,jdbcType=VARCHAR}
            , #{errorMessage,jdbcType=VARCHAR}
            , #{taskClassName,jdbcType=VARCHAR}
            , #{reversalStatus,jdbcType=VARCHAR}
            , #{transactionType,jdbcType=VARCHAR}
            , #{resultAdditionalInfo,jdbcType=VARCHAR}
            , #{requestAdditionalInfo,jdbcType=VARCHAR}
            , #{times,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
            , #{nextExecuteTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:TRANSACTION_TASK_LOG">
        INSERT INTO TRANSACTION_TASK_LOG(
            ID
            ,STATUS
            ,TASK_ID
            ,TASK_TYPE
            ,ERROR_CODE
            ,RETRY_STATUS
            ,ERROR_MESSAGE
            ,TASK_CLASS_NAME
            ,REVERSAL_STATUS
            ,TRANSACTION_TYPE
            ,RESULT_ADDITIONAL_INFO
            ,REQUEST_ADDITIONAL_INFO
            ,TIMES
            ,CREATE_TIME
            ,UPDATE_TIME
            ,NEXT_EXECUTE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.status,jdbcType=VARCHAR}
                , #{item.taskId,jdbcType=VARCHAR}
                , #{item.taskType,jdbcType=VARCHAR}
                , #{item.errorCode,jdbcType=VARCHAR}
                , #{item.retryStatus,jdbcType=VARCHAR}
                , #{item.errorMessage,jdbcType=VARCHAR}
                , #{item.taskClassName,jdbcType=VARCHAR}
                , #{item.reversalStatus,jdbcType=VARCHAR}
                , #{item.transactionType,jdbcType=VARCHAR}
                , #{item.resultAdditionalInfo,jdbcType=VARCHAR}
                , #{item.requestAdditionalInfo,jdbcType=VARCHAR}
                , #{item.times,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
                , #{item.nextExecuteTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:TRANSACTION_TASK_LOG">
        <![CDATA[
        UPDATE TRANSACTION_TASK_LOG
        SET
            STATUS          = #{status,jdbcType=VARCHAR}
            ,TASK_ID         = #{taskId,jdbcType=VARCHAR}
            ,TASK_TYPE       = #{taskType,jdbcType=VARCHAR}
            ,ERROR_CODE      = #{errorCode,jdbcType=VARCHAR}
            ,RETRY_STATUS    = #{retryStatus,jdbcType=VARCHAR}
            ,ERROR_MESSAGE   = #{errorMessage,jdbcType=VARCHAR}
            ,TASK_CLASS_NAME = #{taskClassName,jdbcType=VARCHAR}
            ,REVERSAL_STATUS = #{reversalStatus,jdbcType=VARCHAR}
            ,TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
            ,RESULT_ADDITIONAL_INFO = #{resultAdditionalInfo,jdbcType=VARCHAR}
            ,REQUEST_ADDITIONAL_INFO = #{requestAdditionalInfo,jdbcType=VARCHAR}
            ,TIMES           = #{times,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
            ,NEXT_EXECUTE_TIME = #{nextExecuteTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:TRANSACTION_TASK_LOG">
        <![CDATA[
        DELETE FROM TRANSACTION_TASK_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:TRANSACTION_TASK_LOG">
        SELECT *
        FROM TRANSACTION_TASK_LOG
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByUkTaskIdType" paramtype="object" remark="根据唯一约束UkTaskIdType更新表:TRANSACTION_TASK_LOG">
        <![CDATA[
        UPDATE TRANSACTION_TASK_LOG
        SET
            STATUS          = #{status,jdbcType=VARCHAR}
            ,ERROR_CODE      = #{errorCode,jdbcType=VARCHAR}
            ,RETRY_STATUS    = #{retryStatus,jdbcType=VARCHAR}
            ,ERROR_MESSAGE   = #{errorMessage,jdbcType=VARCHAR}
            ,TASK_CLASS_NAME = #{taskClassName,jdbcType=VARCHAR}
            ,REVERSAL_STATUS = #{reversalStatus,jdbcType=VARCHAR}
            ,TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
            ,RESULT_ADDITIONAL_INFO = #{resultAdditionalInfo,jdbcType=VARCHAR}
            ,REQUEST_ADDITIONAL_INFO = #{requestAdditionalInfo,jdbcType=VARCHAR}
            ,TIMES           = #{times,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
            ,NEXT_EXECUTE_TIME = #{nextExecuteTime,jdbcType=TIMESTAMP}
        WHERE
            TASK_ID         = #{taskId,jdbcType=VARCHAR}
            AND TASK_TYPE       = #{taskType,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByUkTaskIdType" remark="根据唯一约束UkTaskIdType删除数据:TRANSACTION_TASK_LOG">
        <![CDATA[
        DELETE FROM TRANSACTION_TASK_LOG
        WHERE
            TASK_ID         = #{taskId,jdbcType=VARCHAR}
            AND TASK_TYPE       = #{taskType,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByUkTaskIdType" multiplicity="one" remark="根据唯一约束UkTaskIdType获取数据:TRANSACTION_TASK_LOG">
        SELECT *
        FROM TRANSACTION_TASK_LOG
        WHERE
        <![CDATA[
            TASK_ID         = #{taskId,jdbcType=VARCHAR}
            AND TASK_TYPE       = #{taskType,jdbcType=VARCHAR}
        ]]>
    </operation>

    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxNextExecuteTime" multiplicity="many" remark="根据普通索引IdxNextExecuteTime获取数据:TRANSACTION_TASK_LOG">
        SELECT *
        FROM TRANSACTION_TASK_LOG
        WHERE
        <![CDATA[
            NEXT_EXECUTE_TIME = #{nextExecuteTime,jdbcType=TIMESTAMP}
            AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxUpdateTime" multiplicity="many" remark="根据普通索引IdxUpdateTime获取数据:TRANSACTION_TASK_LOG">
        SELECT *
        FROM TRANSACTION_TASK_LOG
        WHERE
        <![CDATA[
            UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
            AND TRANSACTION_TYPE = #{transactionType,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
