<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_COMPONENT_INSTANCE" physicalName="MSHOP_COMPONENT_INSTANCE" remark="页面组件表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,TYPE,USE_RULE,VERSION,PAGE_CODE 
        ,META_CONFIG,TIME_CONFIG,COMPONENT_CODE,DIRECT_USER_GROUP,DELETE_FLAG 
        ,ORDER_VALUE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.TYPE,sf.USE_RULE,sf.VERSION,sf.PAGE_CODE
        ,sf.META_CONFIG,sf.TIME_CONFIG,sf.COMPONENT_CODE,sf.DIRECT_USER_GROUP,sf.DELETE_FLAG
        ,sf.ORDER_VALUE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_COMPONENT_INSTANCE">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_COMPONENT_INSTANCE(
            ID
            ,TYPE
            ,USE_RULE
            ,VERSION
            ,PAGE_CODE
            ,META_CONFIG
            ,TIME_CONFIG
            ,COMPONENT_CODE
            ,DIRECT_USER_GROUP
            ,DELETE_FLAG
            ,ORDER_VALUE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{type,jdbcType=VARCHAR}
            , #{useRule,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{metaConfig,jdbcType=LONGVARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{componentCode,jdbcType=VARCHAR}
            , #{directUserGroup,jdbcType=LONGVARCHAR}
            , #{deleteFlag,jdbcType=INTEGER}
            , #{orderValue,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_COMPONENT_INSTANCE">
        INSERT INTO MSHOP_COMPONENT_INSTANCE(
            ID
            ,TYPE
            ,USE_RULE
            ,VERSION
            ,PAGE_CODE
            ,META_CONFIG
            ,TIME_CONFIG
            ,COMPONENT_CODE
            ,DIRECT_USER_GROUP
            ,DELETE_FLAG
            ,ORDER_VALUE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.type,jdbcType=VARCHAR}
                , #{item.useRule,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.metaConfig,jdbcType=LONGVARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.componentCode,jdbcType=VARCHAR}
                , #{item.directUserGroup,jdbcType=LONGVARCHAR}
                , #{item.deleteFlag,jdbcType=INTEGER}
                , #{item.orderValue,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_COMPONENT_INSTANCE">
        <![CDATA[
        UPDATE MSHOP_COMPONENT_INSTANCE
        SET
            TYPE            = #{type,jdbcType=VARCHAR}
            ,USE_RULE        = #{useRule,jdbcType=VARCHAR}
            ,VERSION         = #{version,jdbcType=VARCHAR}
            ,PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            ,META_CONFIG     = #{metaConfig,jdbcType=LONGVARCHAR}
            ,TIME_CONFIG     = #{timeConfig,jdbcType=LONGVARCHAR}
            ,COMPONENT_CODE  = #{componentCode,jdbcType=VARCHAR}
            ,DIRECT_USER_GROUP = #{directUserGroup,jdbcType=LONGVARCHAR}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=INTEGER}
            ,ORDER_VALUE     = #{orderValue,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_COMPONENT_INSTANCE">
        <![CDATA[
        DELETE FROM MSHOP_COMPONENT_INSTANCE
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_COMPONENT_INSTANCE">
        SELECT *
        FROM MSHOP_COMPONENT_INSTANCE
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据普通索引查询数据 -->
    <operation name="queryByMshopComponentInstancePageCodeIdx" multiplicity="many" remark="根据普通索引MshopComponentInstancePageCodeIdx获取数据:MSHOP_COMPONENT_INSTANCE">
        SELECT *
        FROM MSHOP_COMPONENT_INSTANCE
        WHERE
        <![CDATA[
            PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            AND VERSION         = #{version,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
