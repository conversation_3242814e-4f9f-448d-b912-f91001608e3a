<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_PAGE_INSTANCE" physicalName="MSHOP_PAGE_INSTANCE" remark="微页面表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,TYPE,CHANNEL,OWNER_ID,VERSION 
        ,CREATE_BY,PAGE_CODE,TENANT_ID,UPDATE_BY,OWNER_TYPE 
        ,MARKET_TYPE,CREATE_USER_ID,TEMPLATE_CODE,STATUS,DELETE_FLAG 
        ,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.TYP<PERSON>,sf.CHANNEL,sf.OWNER_ID,sf.VERSION
        ,sf.CREATE_BY,sf.PAGE_CODE,sf.TENANT_ID,sf.UPDATE_BY,sf.OWNER_TYPE
        ,sf.MARKET_TYPE,sf.CREATE_USER_ID,sf.TEMPLATE_CODE,sf.STATUS,sf.DELETE_FLAG
        ,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_PAGE_INSTANCE">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_INSTANCE(
            ID
            ,TYPE
            ,CHANNEL
            ,OWNER_ID
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,MARKET_TYPE
            ,CREATE_USER_ID
            ,TEMPLATE_CODE
            ,STATUS
        )VALUES(
             null
            , #{type,jdbcType=VARCHAR}
            , #{channel,jdbcType=VARCHAR}
            , #{ownerId,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{tenantId,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{ownerType,jdbcType=VARCHAR}
            , #{marketType,jdbcType=VARCHAR}
            , #{createUserId,jdbcType=VARCHAR}
            , #{templateCode,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_PAGE_INSTANCE">
        INSERT INTO MSHOP_PAGE_INSTANCE(
            ID
            ,TYPE
            ,CHANNEL
            ,OWNER_ID
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,TENANT_ID
            ,UPDATE_BY
            ,OWNER_TYPE
            ,MARKET_TYPE
            ,CREATE_USER_ID
            ,TEMPLATE_CODE
            ,STATUS
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.type,jdbcType=VARCHAR}
                , #{item.channel,jdbcType=VARCHAR}
                , #{item.ownerId,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.tenantId,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.ownerType,jdbcType=VARCHAR}
                , #{item.marketType,jdbcType=VARCHAR}
                , #{item.createUserId,jdbcType=VARCHAR}
                , #{item.templateCode,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_PAGE_INSTANCE">
        <![CDATA[
        UPDATE MSHOP_PAGE_INSTANCE
        SET
            TYPE            = #{type,jdbcType=VARCHAR}
            ,CHANNEL         = #{channel,jdbcType=VARCHAR}
            ,OWNER_ID        = #{ownerId,jdbcType=VARCHAR}
            ,VERSION         = #{version,jdbcType=VARCHAR}
            ,CREATE_BY       = #{createBy,jdbcType=VARCHAR}
            ,PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            ,TENANT_ID       = #{tenantId,jdbcType=VARCHAR}
            ,UPDATE_BY       = #{updateBy,jdbcType=VARCHAR}
            ,OWNER_TYPE      = #{ownerType,jdbcType=VARCHAR}
            ,MARKET_TYPE     = #{marketType,jdbcType=VARCHAR}
            ,CREATE_USER_ID  = #{createUserId,jdbcType=VARCHAR}
            ,TEMPLATE_CODE   = #{templateCode,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_PAGE_INSTANCE">
        <![CDATA[
        DELETE FROM MSHOP_PAGE_INSTANCE
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_PAGE_INSTANCE">
        SELECT *
        FROM MSHOP_PAGE_INSTANCE
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

</table>
