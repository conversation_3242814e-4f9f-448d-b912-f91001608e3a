<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_PAGE_DRAFT" physicalName="MSHOP_PAGE_DRAFT" remark="微页面草稿版本表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,NAME,TITLE,VERSION,CREATE_BY 
        ,PAGE_CODE,SUB_TITLE,UPDATE_BY,TIME_CONFIG,SHARE_CONFIG 
        ,CREATE_USER_ID,BACKGROUND_COLOR,BACKGROUND_IMG_URL,PRIVATE_MARKET_CONFIG,STATUS 
        ,BIZ_TYPE,SEARCH_BOX,DELETE_FLAG,SEARCH_FLAG,CREATE_TIME 
        ,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.NAME,sf.TITLE,sf.VERSION,sf.CREATE_BY
        ,sf.PAGE_CODE,sf.SUB_TITLE,sf.UPDATE_BY,sf.TIME_CONFIG,sf.SHARE_CONFIG
        ,sf.CREATE_USER_ID,sf.BACKGROUND_COLOR,sf.BACKGROUND_IMG_URL,sf.PRIVATE_MARKET_CONFIG,sf.STATUS
        ,sf.BIZ_TYPE,sf.SEARCH_BOX,sf.DELETE_FLAG,sf.SEARCH_FLAG,sf.CREATE_TIME
        ,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_PAGE_DRAFT">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_PAGE_DRAFT(
            ID
            ,NAME
            ,TITLE
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,SUB_TITLE
            ,UPDATE_BY
            ,TIME_CONFIG
            ,SHARE_CONFIG
            ,CREATE_USER_ID
            ,BACKGROUND_COLOR
            ,BACKGROUND_IMG_URL
            ,PRIVATE_MARKET_CONFIG
            ,STATUS
            ,BIZ_TYPE
            ,SEARCH_BOX
            ,DELETE_FLAG
            ,SEARCH_FLAG
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{name,jdbcType=VARCHAR}
            , #{title,jdbcType=VARCHAR}
            , #{version,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{pageCode,jdbcType=VARCHAR}
            , #{subTitle,jdbcType=VARCHAR}
            , #{updateBy,jdbcType=VARCHAR}
            , #{timeConfig,jdbcType=LONGVARCHAR}
            , #{shareConfig,jdbcType=LONGVARCHAR}
            , #{createUserId,jdbcType=VARCHAR}
            , #{backgroundColor,jdbcType=VARCHAR}
            , #{backgroundImgUrl,jdbcType=VARCHAR}
            , #{privateMarketConfig,jdbcType=LONGVARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{bizType,jdbcType=TINYINT}
            , #{searchBox,jdbcType=INTEGER}
            , #{deleteFlag,jdbcType=INTEGER}
            , #{searchFlag,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_PAGE_DRAFT">
        INSERT INTO MSHOP_PAGE_DRAFT(
            ID
            ,NAME
            ,TITLE
            ,VERSION
            ,CREATE_BY
            ,PAGE_CODE
            ,SUB_TITLE
            ,UPDATE_BY
            ,TIME_CONFIG
            ,SHARE_CONFIG
            ,CREATE_USER_ID
            ,BACKGROUND_COLOR
            ,BACKGROUND_IMG_URL
            ,PRIVATE_MARKET_CONFIG
            ,STATUS
            ,BIZ_TYPE
            ,SEARCH_BOX
            ,DELETE_FLAG
            ,SEARCH_FLAG
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.name,jdbcType=VARCHAR}
                , #{item.title,jdbcType=VARCHAR}
                , #{item.version,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.pageCode,jdbcType=VARCHAR}
                , #{item.subTitle,jdbcType=VARCHAR}
                , #{item.updateBy,jdbcType=VARCHAR}
                , #{item.timeConfig,jdbcType=LONGVARCHAR}
                , #{item.shareConfig,jdbcType=LONGVARCHAR}
                , #{item.createUserId,jdbcType=VARCHAR}
                , #{item.backgroundColor,jdbcType=VARCHAR}
                , #{item.backgroundImgUrl,jdbcType=VARCHAR}
                , #{item.privateMarketConfig,jdbcType=LONGVARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.bizType,jdbcType=TINYINT}
                , #{item.searchBox,jdbcType=INTEGER}
                , #{item.deleteFlag,jdbcType=INTEGER}
                , #{item.searchFlag,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_PAGE_DRAFT">
        <![CDATA[
        UPDATE MSHOP_PAGE_DRAFT
        SET
            NAME            = #{name,jdbcType=VARCHAR}
            ,TITLE           = #{title,jdbcType=VARCHAR}
            ,VERSION         = #{version,jdbcType=VARCHAR}
            ,CREATE_BY       = #{createBy,jdbcType=VARCHAR}
            ,PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            ,SUB_TITLE       = #{subTitle,jdbcType=VARCHAR}
            ,UPDATE_BY       = #{updateBy,jdbcType=VARCHAR}
            ,TIME_CONFIG     = #{timeConfig,jdbcType=LONGVARCHAR}
            ,SHARE_CONFIG    = #{shareConfig,jdbcType=LONGVARCHAR}
            ,CREATE_USER_ID  = #{createUserId,jdbcType=VARCHAR}
            ,BACKGROUND_COLOR = #{backgroundColor,jdbcType=VARCHAR}
            ,BACKGROUND_IMG_URL = #{backgroundImgUrl,jdbcType=VARCHAR}
            ,PRIVATE_MARKET_CONFIG = #{privateMarketConfig,jdbcType=LONGVARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
            ,SEARCH_BOX      = #{searchBox,jdbcType=INTEGER}
            ,DELETE_FLAG     = #{deleteFlag,jdbcType=INTEGER}
            ,SEARCH_FLAG     = #{searchFlag,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_PAGE_DRAFT">
        <![CDATA[
        DELETE FROM MSHOP_PAGE_DRAFT
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_PAGE_DRAFT">
        SELECT *
        FROM MSHOP_PAGE_DRAFT
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据普通索引查询数据 -->
    <operation name="queryByMshopPageDraftPageCodeIdx" multiplicity="many" remark="根据普通索引MshopPageDraftPageCodeIdx获取数据:MSHOP_PAGE_DRAFT">
        SELECT *
        FROM MSHOP_PAGE_DRAFT
        WHERE
        <![CDATA[
            PAGE_CODE       = #{pageCode,jdbcType=VARCHAR}
            AND VERSION         = #{version,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
