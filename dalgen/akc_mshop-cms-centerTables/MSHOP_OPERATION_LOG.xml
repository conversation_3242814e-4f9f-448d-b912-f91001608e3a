<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="MSHOP_OPERATION_LOG" physicalName="MSHOP_OPERATION_LOG" remark="操作记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,ACTION,REMARK,BIZ_CODE,CREATE_BY 
        ,AFTER_DATA,BEFORE_DATA,CREATE_USER_ID,BIZ_TYPE,CREATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.ACTION,sf.REMARK,sf.BIZ_CODE,sf.CREATE_BY
        ,sf.AFTER_DATA,sf.BEFORE_DATA,sf.CREATE_USER_ID,sf.BIZ_TYPE,sf.CREATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:MSHOP_OPERATION_LOG">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO MSHOP_OPERATION_LOG(
            ID
            ,ACTION
            ,REMARK
            ,BIZ_CODE
            ,CREATE_BY
            ,AFTER_DATA
            ,BEFORE_DATA
            ,CREATE_USER_ID
            ,BIZ_TYPE
            ,CREATE_TIME
        )VALUES(
             null
            , #{action,jdbcType=VARCHAR}
            , #{remark,jdbcType=VARCHAR}
            , #{bizCode,jdbcType=VARCHAR}
            , #{createBy,jdbcType=VARCHAR}
            , #{afterData,jdbcType=LONGVARCHAR}
            , #{beforeData,jdbcType=LONGVARCHAR}
            , #{createUserId,jdbcType=VARCHAR}
            , #{bizType,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:MSHOP_OPERATION_LOG">
        INSERT INTO MSHOP_OPERATION_LOG(
            ID
            ,ACTION
            ,REMARK
            ,BIZ_CODE
            ,CREATE_BY
            ,AFTER_DATA
            ,BEFORE_DATA
            ,CREATE_USER_ID
            ,BIZ_TYPE
            ,CREATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.action,jdbcType=VARCHAR}
                , #{item.remark,jdbcType=VARCHAR}
                , #{item.bizCode,jdbcType=VARCHAR}
                , #{item.createBy,jdbcType=VARCHAR}
                , #{item.afterData,jdbcType=LONGVARCHAR}
                , #{item.beforeData,jdbcType=LONGVARCHAR}
                , #{item.createUserId,jdbcType=VARCHAR}
                , #{item.bizType,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:MSHOP_OPERATION_LOG">
        <![CDATA[
        UPDATE MSHOP_OPERATION_LOG
        SET
            ACTION          = #{action,jdbcType=VARCHAR}
            ,REMARK          = #{remark,jdbcType=VARCHAR}
            ,BIZ_CODE        = #{bizCode,jdbcType=VARCHAR}
            ,CREATE_BY       = #{createBy,jdbcType=VARCHAR}
            ,AFTER_DATA      = #{afterData,jdbcType=LONGVARCHAR}
            ,BEFORE_DATA     = #{beforeData,jdbcType=LONGVARCHAR}
            ,CREATE_USER_ID  = #{createUserId,jdbcType=VARCHAR}
            ,BIZ_TYPE        = #{bizType,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:MSHOP_OPERATION_LOG">
        <![CDATA[
        DELETE FROM MSHOP_OPERATION_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:MSHOP_OPERATION_LOG">
        SELECT *
        FROM MSHOP_OPERATION_LOG
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

</table>
