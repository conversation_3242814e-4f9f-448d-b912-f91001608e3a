<?xml version="1.0" encoding="UTF-8"?>

<!-- ============================================================== -->
<!-- Master configuration file for auto-generation of dal. -->
<!-- ============================================================== -->

<config>
    <!-- ========================================================== -->
    <!-- The typemap("Type Map") maps from one java type to another -->
    <!-- java type. If you feel the original sql data type to java -->
    <!-- type mapping is not satisfactory, you can use typemap to -->
    <!-- convert it to a more appropriate one. -->
    <!-- ========================================================== -->
    <typemap from="java.sql.Date" to="java.util.Date"/>
    <typemap from="java.sql.Time" to="java.util.Date"/>
    <typemap from="java.sql.Timestamp" to="java.util.Date"/>
    <typemap from="java.math.BigDecimal" to="Long"/>
    <typemap from="byte" to="int"/>
    <typemap from="short" to="int"/>

    <!-- ========================================================== -->
    <!-- datasource config  可以配置多个-->
    <!-- database name 自定义-->
    <!-- ========================================================== -->
    <!-- -->
    <database name="akc_mshop-cms-center" class="com.mysql.cj.jdbc.Driver" type="mysql">
        <property name="url"
                  value="*********************************************************************************************************************************************************"/>
        <property name="userid" value="test1"/>
        <property name="password" value="hDGwrnCL2vFY8113"/>
<!--        <property name="schema" value="trans"/>-->
    </database>

    <!-- ========project.name pom.xml中的值========================= -->
    <!--<package value="com.bgdf.${project.name}.common.dal.${database.name}.auto"/>-->
    <!--database="fbi" 此参数可选，多数据源时如果不使用此参数则为通用-->
    <package value="com.mengxiang.mshop.cms.common.dal">
        <subClass name="dal" value="dal"/>
        <subClass name="mapper" value="mapper"/>
        <subClass name="mapper.xml" value="mapper"/>
        <subClass name="paging" value="paging"/>
        <subClass name="dao" value="dao"/>
        <subClass name="dataobject" value="dataobject"/>
        <subClass name="resultmap" value="resultmap"/>
    </package>


    <!-- ========================================================== -->

    <!--分库分表规则  分表后缀 支持多个-->
<!--    <splitTableSuffixs>-->
<!--        <splitTableSuffix value="_000"/>-->
<!--    </splitTableSuffixs>-->

    <!-- 表省略前置 支持多个 -->
    <tablePrefixs database="mshop_cms_center">
        <!--长的放前面-->
        <tablePrefix value="mshop_cms_center_"/>
    </tablePrefixs>

    <!-- &lt;!&ndash; 索引省略前置 支持多个 &ndash;&gt;
     <indexPrefixs>
         &lt;!&ndash;长的放前面&ndash;&gt;
         <indexPrefix value="idx_"/>
         <indexPrefix value="uk_" replace=""/>
     </indexPrefixs> -->

    <!-- 软删除字段 软删除存在唯一约束问题,如果defVal为数字 则当前数字为未删除,大于此数字为删除,为数值是delVal不生效-->
    <softDelete cloumn="is_deleted" defVal="N" delVal="Y"/>

    <extParams>
        <extParam name="Repository" value="true"/>
    </extParams>
</config>
